"use client";

import { dummyJobs } from "@/data/jobs-data";
import { type Job as ExtendedJob } from "@/hooks/employer/use-manage-jobs";
import { Grid, Text } from "@mantine/core";
import { useState } from "react";
import JobCard from "./JobCard";

export default function JobsList() {
  // Set default view mode to grid
  const [viewMode] = useState<"grid" | "list">("grid");

  // Transform dummy jobs data to match the expected format for JobCard
  const jobs = dummyJobs.map((job) => {
    // Map job type and location to expected enum values
    let jobType:
      | "full-time"
      | "part-time"
      | "contract"
      | "internship"
      | "temporary" = "full-time";
    if (job.type) {
      const type = job.type.toLowerCase();
      if (type.includes("part")) jobType = "part-time";
      else if (type.includes("contract")) jobType = "contract";
      else if (type.includes("intern")) jobType = "internship";
      else if (type.includes("temp")) jobType = "temporary";
    }

    let location: "remote" | "onsite" | "hybrid" = "onsite";
    if (job.workType) {
      const locType = job.workType.toLowerCase();
      if (locType.includes("remote")) location = "remote";
      else if (locType.includes("hybrid")) location = "hybrid";
    } else if (job.location && job.location.toLowerCase().includes("remote")) {
      location = "remote";
    }

    // Parse salary string to extract min and max values
    let minSalary: number | undefined = undefined;
    let maxSalary: number | undefined = undefined;
    let currency: string | undefined = undefined;

    if (job.salary && typeof job.salary === "string") {
      // Try to extract salary range and currency from string like "$80,000 - $100,000"
      const salaryMatch = job.salary.match(
        /(\$|€|£|¥)?([0-9,.]+).*?(\$|€|£|¥)?([0-9,.]+)/,
      );
      if (salaryMatch) {
        const currencySymbol = salaryMatch[1] || salaryMatch[3] || "$";
        // Convert currency symbol to code
        switch (currencySymbol) {
          case "€":
            currency = "EUR";
            break;
          case "£":
            currency = "GBP";
            break;
          case "¥":
            currency = "JPY";
            break;
          default:
            currency = "USD";
        }

        // Parse min and max values, removing commas
        minSalary = parseFloat(salaryMatch[2].replace(/,/g, ""));
        maxSalary = parseFloat(salaryMatch[4].replace(/,/g, ""));
      }
    }

    // Create a properly typed job object
    const jobId = job.id || 0;
    return {
      _id: jobId.toString(),
      id: job.id,
      title: job.title,
      company: {
        _id: "1",
        name:
          typeof job.company === "string"
            ? job.company
            : job.company.name || "Unknown Company",
      },
      description: job.description || "",
      requirements: job.description || "", // Use description as requirements if not available
      jobType,
      location,
      category: job.category || "",
      tags: job.skills || [],
      minSalary,
      maxSalary,
      currency,
      showSalary: true,
      benefits: [],
      applicationDeadline: job.datePosted,
      isActive: typeof jobId === "number" ? jobId % 3 !== 0 : true, // Every third job is inactive
      questions: [],
      createdAt: job.datePosted || new Date().toISOString(),
      updatedAt: job.datePosted || new Date().toISOString(),
      requiredExperience: job.experience || "1-2 years",
      status:
        typeof jobId === "number"
          ? jobId % 3 === 0
            ? "Closed"
            : jobId % 2 === 0
              ? "Pending"
              : "Active"
          : "Active",
    } as ExtendedJob;
  });

  if (jobs.length === 0) {
    return <Text className="text-center text-lg">No jobs available.</Text>;
  }

  return (
    <Grid gutter="lg">
      {jobs.map((job) => (
        <JobCard
          key={job._id}
          job={job}
          viewMode={viewMode}
          onDelete={() => console.log(`Would delete job: ${job._id}`)}
          onToggleStatus={() =>
            console.log(`Would toggle status for job: ${job._id}`)
          }
        />
      ))}
    </Grid>
  );
}
