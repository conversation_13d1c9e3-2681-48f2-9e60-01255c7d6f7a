"use client";

import { PasswordInput, type PasswordInputProps } from "@mantine/core";

interface FormPasswordInputProps
  extends Omit<PasswordInputProps, "leftSection"> {
  icon?: React.ReactNode;
  formProps?: any;
}

/**
 * Reusable password input component with standardized styling
 */
export default function FormPasswordInput({
  icon,
  formProps,
  ...props
}: FormPasswordInputProps) {
  return <PasswordInput leftSection={icon} {...formProps} {...props} />;
}
