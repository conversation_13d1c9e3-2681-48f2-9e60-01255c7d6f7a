"use client";

import { useUserDetailsModal } from "@/stores/modal-store";
import {
  Avatar,
  Badge,
  Button,
  Divider,
  Grid,
  Group,
  Modal,
  Paper,
  Stack,
  Tabs,
  Text,
  ThemeIcon,
} from "@mantine/core";
import {
  FaBan,
  FaBriefcase,
  FaCalendar,
  FaCheck,
  FaEdit,
  FaEnvelope,
  FaGraduationCap,
  FaHistory,
  FaIdCard,
  FaMapMarkerAlt,
  FaPhone,
  FaTrash,
  FaUser,
} from "react-icons/fa";

interface UserDetailsModalProps {
  userId?: number | null;
}

// Mock user data with more details
const mockUserDetails = {
  id: 1,
  name: "<PERSON>",
  email: "<EMAIL>",
  role: "candidate",
  status: "active",
  phone: "+****************",
  address: "123 Main St, Anytown, USA",
  bio: "Experienced software developer with 5 years of experience in web development. Proficient in JavaScript, React, and Node.js. Passionate about creating user-friendly interfaces and solving complex problems.",
  createdAt: "2023-10-10",
  updatedAt: "2023-10-15",
  avatar: "/images/avatar-placeholder.png",
  skills: ["JavaScript", "React", "Node.js", "TypeScript", "HTML/CSS"],
  education: [
    {
      degree: "Bachelor of Science in Computer Science",
      institution: "University of Technology",
      year: "2018-2022",
    },
  ],
  experience: [
    {
      title: "Frontend Developer",
      company: "Tech Solutions Inc.",
      period: "2022-Present",
      description:
        "Developing and maintaining web applications using React and TypeScript.",
    },
  ],
  applications: [
    {
      jobTitle: "Senior Frontend Developer",
      company: "Global Innovations",
      date: "2023-10-05",
      status: "pending",
    },
  ],
  loginHistory: [
    {
      date: "2023-10-15 14:30",
      ip: "***********",
      device: "Chrome on Windows",
    },
    {
      date: "2023-10-12 09:15",
      ip: "***********",
      device: "Chrome on Windows",
    },
  ],
};

export default function UserDetailsModal({ userId }: UserDetailsModalProps) {
  // In a real application, you would fetch user details based on userId
  const user = mockUserDetails;

  console.log(userId);

  const { close } = useUserDetailsModal();

  const closeModal = () => {
    close();
  };

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case "admin":
        return "purple";
      case "employer":
        return "green";
      case "candidate":
        return "blue";
      default:
        return "gray";
    }
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case "active":
        return "green";
      case "inactive":
        return "yellow";
      case "blocked":
        return "red";
      default:
        return "gray";
    }
  };

  // Use the hook outside of the JSX to avoid hydration issues
  const { isOpen } = useUserDetailsModal();

  return (
    <Modal
      opened={isOpen}
      onClose={closeModal}
      title={
        <Group gap="md">
          <Avatar src={user.avatar} size="lg" radius="xl" />
          <div>
            <Text size="lg" fw={700}>
              {user.name}
            </Text>
            <Group gap="xs">
              <Badge color={getRoleBadgeColor(user.role)} variant="light">
                {user.role}
              </Badge>
              <Badge color={getStatusBadgeColor(user.status)} variant="light">
                {user.status}
              </Badge>
            </Group>
          </div>
        </Group>
      }
      size="xl"
      padding="lg"
      centered
    >
      <Tabs defaultValue="profile" mt="xl">
        <Tabs.List>
          <Tabs.Tab value="profile" leftSection={<FaUser size={14} />}>
            Profile
          </Tabs.Tab>
          {user.role === "candidate" && (
            <Tabs.Tab
              value="applications"
              leftSection={<FaBriefcase size={14} />}
            >
              Applications
            </Tabs.Tab>
          )}
          <Tabs.Tab value="activity" leftSection={<FaHistory size={14} />}>
            Activity
          </Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="profile" pt="md">
          <Grid gutter="md">
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Paper withBorder p="md" radius="md">
                <Text fw={600} size="sm" c="dimmed" mb="md">
                  CONTACT INFORMATION
                </Text>
                <Stack gap="sm">
                  <Group>
                    <ThemeIcon variant="light" color="blue" size="md">
                      <FaEnvelope size={14} />
                    </ThemeIcon>
                    <div>
                      <Text size="xs" c="dimmed">
                        Email
                      </Text>
                      <Text size="sm">{user.email}</Text>
                    </div>
                  </Group>
                  <Group>
                    <ThemeIcon variant="light" color="green" size="md">
                      <FaPhone size={14} />
                    </ThemeIcon>
                    <div>
                      <Text size="xs" c="dimmed">
                        Phone
                      </Text>
                      <Text size="sm">{user.phone}</Text>
                    </div>
                  </Group>
                  <Group align="flex-start">
                    <ThemeIcon
                      variant="light"
                      color="orange"
                      size="md"
                      style={{ marginTop: "4px" }}
                    >
                      <FaMapMarkerAlt size={14} />
                    </ThemeIcon>
                    <div>
                      <Text size="xs" c="dimmed">
                        Address
                      </Text>
                      <Text size="sm">{user.address}</Text>
                    </div>
                  </Group>
                </Stack>
              </Paper>
            </Grid.Col>

            <Grid.Col span={{ base: 12, md: 6 }}>
              <Paper withBorder p="md" radius="md">
                <Text fw={600} size="sm" c="dimmed" mb="md">
                  ACCOUNT INFORMATION
                </Text>
                <Stack gap="sm">
                  <Group>
                    <ThemeIcon variant="light" color="indigo" size="md">
                      <FaIdCard size={14} />
                    </ThemeIcon>
                    <div>
                      <Text size="xs" c="dimmed">
                        User ID
                      </Text>
                      <Text size="sm">#{user.id}</Text>
                    </div>
                  </Group>
                  <Group>
                    <ThemeIcon variant="light" color="grape" size="md">
                      <FaCalendar size={14} />
                    </ThemeIcon>
                    <div>
                      <Text size="xs" c="dimmed">
                        Registered on
                      </Text>
                      <Text size="sm">{user.createdAt}</Text>
                    </div>
                  </Group>
                  <Group>
                    <ThemeIcon variant="light" color="cyan" size="md">
                      <FaCalendar size={14} />
                    </ThemeIcon>
                    <div>
                      <Text size="xs" c="dimmed">
                        Last updated
                      </Text>
                      <Text size="sm">{user.updatedAt}</Text>
                    </div>
                  </Group>
                </Stack>
              </Paper>
            </Grid.Col>

            <Grid.Col span={12}>
              <Paper withBorder p="md" radius="md">
                <Text fw={600} size="sm" c="dimmed" mb="md">
                  BIO
                </Text>
                <Text size="sm" lh={1.6}>
                  {user.bio}
                </Text>
              </Paper>
            </Grid.Col>

            {user.role === "candidate" && (
              <>
                <Grid.Col span={{ base: 12, md: 6 }}>
                  <Paper withBorder p="md" radius="md">
                    <Text fw={600} size="sm" c="dimmed" mb="md">
                      SKILLS
                    </Text>
                    <Group gap="xs">
                      {user.skills.map((skill, index) => (
                        <Badge key={index} variant="light" color="blue">
                          {skill}
                        </Badge>
                      ))}
                    </Group>
                  </Paper>
                </Grid.Col>

                <Grid.Col span={{ base: 12, md: 6 }}>
                  <Paper withBorder p="md" radius="md">
                    <Text fw={600} size="sm" c="dimmed" mb="md">
                      EDUCATION
                    </Text>
                    {user.education.map((edu, index) => (
                      <div key={index} className="mb-2">
                        <Group align="center">
                          <ThemeIcon variant="light" color="teal" size="md">
                            <FaGraduationCap size={14} />
                          </ThemeIcon>
                          <div>
                            <Text size="sm" fw={500}>
                              {edu.degree}
                            </Text>
                            <Text size="xs" c="dimmed">
                              {edu.institution} • {edu.year}
                            </Text>
                          </div>
                        </Group>
                      </div>
                    ))}
                  </Paper>
                </Grid.Col>
              </>
            )}
          </Grid>
        </Tabs.Panel>

        <Tabs.Panel value="applications" pt="md">
          {user.applications.map((app, index) => (
            <Paper key={index} withBorder p="md" radius="md" mb="md">
              <Group justify="space-between">
                <div>
                  <Text fw={500}>{app.jobTitle}</Text>
                  <Text size="sm" c="dimmed">
                    {app.company}
                  </Text>
                </div>
                <Badge
                  color={
                    app.status === "pending"
                      ? "yellow"
                      : app.status === "approved"
                        ? "green"
                        : "red"
                  }
                  variant="light"
                >
                  {app.status}
                </Badge>
              </Group>
              <Text size="xs" c="dimmed" mt="xs">
                Applied on: {app.date}
              </Text>
            </Paper>
          ))}
        </Tabs.Panel>

        <Tabs.Panel value="activity" pt="md">
          <Paper withBorder p="md" radius="md">
            <Text fw={600} size="sm" c="dimmed" mb="md">
              LOGIN HISTORY
            </Text>
            <Stack gap="md">
              {user.loginHistory.map((login, index) => (
                <div key={index} className="border-b pb-2">
                  <Group justify="space-between">
                    <Text size="sm">{login.date}</Text>
                    <Badge size="sm" variant="outline">
                      {login.device}
                    </Badge>
                  </Group>
                  <Text size="xs" c="dimmed">
                    IP: {login.ip}
                  </Text>
                </div>
              ))}
            </Stack>
          </Paper>
        </Tabs.Panel>
      </Tabs>

      <Divider my="lg" />

      <Group justify="space-between">
        <Group>
          {user.status === "active" ? (
            <Button
              variant="outline"
              color="orange"
              leftSection={<FaBan size={14} />}
              size="sm"
            >
              Deactivate
            </Button>
          ) : user.status === "inactive" || user.status === "blocked" ? (
            <Button
              variant="outline"
              color="green"
              leftSection={<FaCheck size={14} />}
              size="sm"
            >
              Activate
            </Button>
          ) : null}
        </Group>
        <Group>
          <Button
            variant="outline"
            leftSection={<FaEdit size={14} />}
            size="sm"
          >
            Edit
          </Button>
          <Button
            variant="filled"
            color="red"
            leftSection={<FaTrash size={14} />}
            size="sm"
          >
            Delete
          </Button>
        </Group>
      </Group>
    </Modal>
  );
}
