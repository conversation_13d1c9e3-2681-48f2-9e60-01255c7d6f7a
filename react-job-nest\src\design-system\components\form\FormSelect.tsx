"use client";

import { Select, type SelectProps } from "@mantine/core";
import { type ReactNode } from "react";

interface FormSelectProps extends Omit<SelectProps, "leftSection"> {
  icon?: ReactNode;
  formProps?: any;
}

/**
 * Reusable select input component with standardized styling
 */
export default function FormSelect({
  icon,
  formProps,
  ...props
}: FormSelectProps) {
  return <Select leftSection={icon} {...formProps} {...props} />;
}
