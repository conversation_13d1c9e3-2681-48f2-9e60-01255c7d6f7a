import { Request, Response, NextFunction } from "express";
import User from "../models/user.model";
import { AppError } from "../utils/app-error";
import { catchAsync } from "../utils/catch-async";

// Get all users (admin only)
export const getAllUsers = catchAsync(
  async (_req: Request, res: Response, _next: NextFunction) => {
    const users = await User.find();

    res.status(200).json({
      success: true,
      count: users.length,
      users,
    });
  }
);

// Get user by ID
export const getUserById = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    const user = await User.findById(req.params.id);

    if (!user) {
      return next(new AppError("No user found with that ID", 404));
    }

    res.status(200).json({
      success: true,
      user,
    });
  }
);

// Update user
export const updateUser = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    // Filter out fields that shouldn't be updated
    const filteredBody: any = {};
    const allowedFields = ["name", "email"];

    Object.keys(req.body).forEach((key) => {
      if (allowedFields.includes(key)) {
        filteredBody[key] = req.body[key];
      }
    });

    const user = await User.findByIdAndUpdate(req.params.id, filteredBody, {
      new: true,
      runValidators: true,
    });

    if (!user) {
      return next(new AppError("No user found with that ID", 404));
    }

    res.status(200).json({
      success: true,
      user,
    });
  }
);

// Delete user
export const deleteUser = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    const user = await User.findByIdAndDelete(req.params.id);

    if (!user) {
      return next(new AppError("No user found with that ID", 404));
    }

    res.status(204).json({
      success: true,
      data: null,
    });
  }
);

// Update current user profile
export const updateMe = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    // 1) Create error if user tries to update password
    if (req.body.password || req.body.passwordConfirmation) {
      return next(
        new AppError(
          "This route is not for password updates. Please use /updatePassword",
          400
        )
      );
    }

    // 2) Filter out fields that shouldn't be updated
    const filteredBody: any = {};
    const allowedFields = ["name", "email"];

    Object.keys(req.body).forEach((key) => {
      if (allowedFields.includes(key)) {
        filteredBody[key] = req.body[key];
      }
    });

    // 3) Update user document
    const updatedUser = await User.findByIdAndUpdate(
      req.user._id,
      filteredBody,
      {
        new: true,
        runValidators: true,
      }
    );

    res.status(200).json({
      success: true,
      user: updatedUser,
    });
  }
);

// Delete current user (set inactive)
export const deleteMe = catchAsync(
  async (req: Request, res: Response, _next: NextFunction) => {
    await User.findByIdAndDelete(req.user._id);

    res.status(204).json({
      success: true,
      data: null,
    });
  }
);
