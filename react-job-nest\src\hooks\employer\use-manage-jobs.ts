import employerApi from "@/services/employer-api";
import type { Job as JobType } from "@/types";
import { notifications } from "@mantine/notifications";
import { useEffect, useState } from "react";

// Extended Job type definition with required fields for employer management
export interface Job extends JobType {
  _id: string;
  company: {
    _id: string;
    name: string;
    logo?: string;
  };
  description: string;
  requirements: string;
  jobType: "full-time" | "part-time" | "contract" | "internship" | "temporary";
  location: "remote" | "onsite" | "hybrid";
  category: string;
  tags: string[];
  showSalary: boolean;
  benefits: string[];
  isActive: boolean;
  questions: string[];
  createdAt: string;
  updatedAt: string;
  requiredExperience: string;
  status: "Active" | "Pending" | "Closed";
}

export default function useManageJobs() {
  const [jobs, setJobs] = useState<Job[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isToggling, setIsToggling] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch jobs from the backend
  const fetchJobs = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await employerApi.jobs.getMyJobs();
      const responseData = response.data as any;

      if (responseData.success) {
        // Map backend jobs to our Job interface
        const fetchedJobs = responseData.jobs.map((job: any) => {
          // Process job data
          let status: "Active" | "Pending" | "Closed" = "Active";

          // Determine status based on isActive and other properties
          if (!job.isActive) {
            status = "Closed";
          } else if (
            job.applicationDeadline &&
            new Date(job.applicationDeadline) < new Date()
          ) {
            status = "Closed"; // Past deadline
          }

          // Format the job object
          return {
            ...job,
            status,
            // Ensure company object is properly structured
            company: {
              _id: job.company._id || job.company,
              name: job.company.name || "Unknown Company",
              logo: job.company.logo || null,
            },
          };
        });

        console.log("Fetched jobs:", fetchedJobs);
        setJobs(fetchedJobs);
      } else {
        throw new Error(responseData.message || "Failed to fetch jobs");
      }
    } catch (error: any) {
      console.error("Error fetching jobs:", error);

      // In development mode, use mock data if backend is not available
      if (process.env.NODE_ENV === "development") {
        console.log("Using mock job data");
        // Import dummy data
        const { dummyJobs } = await import("@/data/jobs-data");

        // Map dummy jobs to our Job interface
        const mockJobs = dummyJobs.map((job) => {
          // Parse salary string to extract min and max values
          let minSalary: number | undefined = undefined;
          let maxSalary: number | undefined = undefined;
          let currency: string | undefined = undefined;

          if (job.salary && typeof job.salary === "string") {
            // Try to extract salary range and currency from string like "$80,000 - $100,000"
            const salaryMatch = job.salary.match(
              /(\$|€|£|¥)?([0-9,.]+).*?(\$|€|£|¥)?([0-9,.]+)/,
            );
            if (salaryMatch) {
              const currencySymbol = salaryMatch[1] || salaryMatch[3] || "$";
              // Convert currency symbol to code
              switch (currencySymbol) {
                case "€":
                  currency = "EUR";
                  break;
                case "£":
                  currency = "GBP";
                  break;
                case "¥":
                  currency = "JPY";
                  break;
                default:
                  currency = "USD";
              }

              // Parse min and max values, removing commas
              minSalary = parseFloat(salaryMatch[2].replace(/,/g, ""));
              maxSalary = parseFloat(salaryMatch[4].replace(/,/g, ""));
            }
          }

          // Map job type and location to expected enum values
          let jobType:
            | "full-time"
            | "part-time"
            | "contract"
            | "internship"
            | "temporary" = "full-time";
          if (job.type) {
            const type = job.type.toLowerCase();
            if (type.includes("part")) jobType = "part-time";
            else if (type.includes("contract")) jobType = "contract";
            else if (type.includes("intern")) jobType = "internship";
            else if (type.includes("temp")) jobType = "temporary";
          }

          let location: "remote" | "onsite" | "hybrid" = "onsite";
          if (job.workType) {
            const locType = job.workType.toLowerCase();
            if (locType.includes("remote")) location = "remote";
            else if (locType.includes("hybrid")) location = "hybrid";
          } else if (
            job.location &&
            job.location.toLowerCase().includes("remote")
          ) {
            location = "remote";
          }

          // Create a properly typed job object
          const jobId = job.id || 0; // Default to 0 if undefined
          return {
            _id: jobId.toString(),
            title: job.title,
            company: {
              _id: "1",
              name: job.company,
            },
            description: job.description || "",
            requirements: job.description || "", // Use description as requirements if not available
            jobType,
            location,
            category: job.category || "",
            tags: job.skills || [],
            minSalary,
            maxSalary,
            currency,
            showSalary: true,
            benefits: [],
            applicationDeadline: job.datePosted,
            isActive: typeof jobId === "number" ? jobId % 3 !== 0 : true, // Every third job is inactive
            questions: [],
            createdAt: job.datePosted || new Date().toISOString(),
            updatedAt: job.datePosted || new Date().toISOString(),
            requiredExperience: job.experience || "1-2 years",
            status:
              typeof jobId === "number"
                ? jobId % 3 === 0
                  ? "Closed"
                  : jobId % 2 === 0
                    ? "Pending"
                    : "Active"
                : "Active",
          } as Job;
        });

        setJobs(mockJobs);
      } else {
        setError("Failed to fetch jobs");
        notifications.show({
          title: "Error",
          message: error.response?.data?.message || "Failed to fetch jobs",
          color: "red",
        });
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Delete a job
  const deleteJob = async (jobId: string) => {
    try {
      setIsDeleting(true);
      setError(null);

      const response = await employerApi.jobs.deleteJob(jobId);
      const responseData = response.data as any;

      if (responseData.success) {
        // Remove the job from the local state
        setJobs(jobs.filter((job) => job._id !== jobId));

        // Show success notification
        notifications.show({
          title: "Success",
          message: "Job has been successfully deleted",
          color: "green",
        });

        return true;
      } else {
        throw new Error(responseData.message || "Failed to delete job");
      }
    } catch (error: any) {
      console.error("Error deleting job:", error);

      // Set error state
      setError(error.response?.data?.message || "Failed to delete job");

      // Show error notification
      notifications.show({
        title: "Error",
        message: error.response?.data?.message || "Failed to delete job",
        color: "red",
      });

      return false;
    } finally {
      setIsDeleting(false);
    }
  };

  // Toggle job status (active/inactive)
  const toggleJobStatus = async (jobId: string) => {
    try {
      setIsToggling(true);
      setError(null);

      // Find the job
      const job = jobs.find((j) => j._id === jobId);
      if (!job) return false;

      // Call the API to update the job
      const response = await employerApi.jobs.updateJob(jobId, {
        isActive: !job.isActive,
      });
      const responseData = response.data as any;

      if (responseData.success) {
        // Update the job in the local state
        setJobs(
          jobs.map((j) =>
            j._id === jobId
              ? {
                  ...j,
                  isActive: !j.isActive,
                  status: !j.isActive ? "Active" : "Closed",
                }
              : j,
          ),
        );

        // Show success notification
        const newStatus = !job.isActive ? "activated" : "deactivated";
        notifications.show({
          title: "Success",
          message: `Job has been successfully ${newStatus}`,
          color: "green",
        });

        return true;
      } else {
        throw new Error(responseData.message || "Failed to update job status");
      }
    } catch (error: any) {
      console.error("Error updating job status:", error);

      // Set error state
      setError(error.response?.data?.message || "Failed to update job status");

      // Show error notification
      notifications.show({
        title: "Error",
        message: error.response?.data?.message || "Failed to update job status",
        color: "red",
      });

      return false;
    } finally {
      setIsToggling(false);
    }
  };

  // Fetch jobs on mount
  useEffect(() => {
    fetchJobs();
  }, []);

  return {
    jobs,
    isLoading,
    isDeleting,
    isToggling,
    error,
    fetchJobs,
    deleteJob,
    toggleJobStatus,
  };
}
