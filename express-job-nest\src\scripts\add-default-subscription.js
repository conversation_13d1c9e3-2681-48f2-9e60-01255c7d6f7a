/**
 * Script to add a default subscription for all employer users
 * Run this script with: node src/scripts/add-default-subscription.js
 */

require("dotenv").config();
const mongoose = require("mongoose");
const path = require("path");
const fs = require("fs");

// Connect to MongoDB
mongoose
  .connect(process.env.MONGODB_URI || "mongodb://localhost:27017/job-nest")
  .then(() => console.log("MongoDB Connected"))
  .catch((err) => {
    console.error("Error connecting to MongoDB:", err);
    process.exit(1);
  });

// Define schemas directly in this script to avoid import issues
const userSchema = new mongoose.Schema({
  name: String,
  email: String,
  password: String,
  role: String,
});

const subscriptionPlanSchema = new mongoose.Schema({
  name: String,
  description: String,
  price: Number,
  annualPrice: Number,
  currency: String,
  billingPeriod: String,
  features: {
    activeJobs: mongoose.Schema.Types.Mixed,
    jobVisibilityDays: Number,
    cvViews: mongoose.Schema.Types.Mixed,
    featuredJobs: mongoose.Schema.Types.Mixed,
    priorityPlacement: Boolean,
    featuredCompanyProfile: Boolean,
    candidateMatching: Boolean,
    advancedAnalytics: Boolean,
    dedicatedSupport: Boolean,
    customFeatures: [String],
  },
  isActive: Boolean,
  isPopular: Boolean,
  sortOrder: Number,
});

const userSubscriptionSchema = new mongoose.Schema({
  user: mongoose.Schema.Types.ObjectId,
  plan: mongoose.Schema.Types.ObjectId,
  status: String,
  startDate: Date,
  endDate: Date,
  billingPeriod: String,
  autoRenew: Boolean,
  paymentMethod: String,
  transactionId: String,
  price: Number,
  currency: String,
  usageStats: {
    jobsPosted: Number,
    cvViews: Number,
    featuredJobsUsed: Number,
  },
  canceledAt: Date,
});

// Define models - let MongoDB use the default collection names
const User = mongoose.model("User", userSchema);
const SubscriptionPlan = mongoose.model(
  "SubscriptionPlan",
  subscriptionPlanSchema
);
const UserSubscription = mongoose.model(
  "UserSubscription",
  userSubscriptionSchema
);

// Create a default unlimited subscription plan if it doesn't exist
async function createDefaultPlan() {
  try {
    let plan = await SubscriptionPlan.findOne({
      name: "Unlimited Developer Plan",
    });

    if (!plan) {
      plan = await SubscriptionPlan.create({
        name: "Unlimited Developer Plan",
        description: "Unlimited plan for development purposes",
        price: 0,
        annualPrice: 0,
        currency: "USD",
        billingPeriod: "month",
        features: {
          activeJobs: "unlimited",
          jobVisibilityDays: 90,
          cvViews: "unlimited",
          featuredJobs: "unlimited",
          priorityPlacement: true,
          featuredCompanyProfile: true,
          candidateMatching: true,
          advancedAnalytics: true,
          dedicatedSupport: true,
          customFeatures: ["All features enabled for development"],
        },
        isActive: true,
        isPopular: false,
        sortOrder: 999,
      });

      console.log("Created default unlimited plan:", plan._id);
    } else {
      console.log("Using existing unlimited plan:", plan._id);
    }

    return plan;
  } catch (error) {
    console.error("Error creating default plan:", error);
    process.exit(1);
  }
}

// Add subscription for all employer users
async function addSubscriptionsForEmployers(plan) {
  try {
    // Get all employer users
    const employers = await User.find({ role: "employer" });
    console.log(`Found ${employers.length} employer users`);

    // Calculate dates
    const startDate = new Date();
    const endDate = new Date();
    endDate.setFullYear(endDate.getFullYear() + 10); // 10 years subscription

    // Add subscription for each employer
    for (const employer of employers) {
      // Check if user already has an active subscription
      const existingSubscription = await UserSubscription.findOne({
        user: employer._id,
        status: "active",
      });

      if (!existingSubscription) {
        // Create new subscription
        const subscription = await UserSubscription.create({
          user: employer._id,
          plan: plan._id,
          status: "active",
          startDate,
          endDate,
          billingPeriod: "month",
          autoRenew: true,
          paymentMethod: "development",
          price: 0,
          currency: "USD",
          usageStats: {
            jobsPosted: 0,
            cvViews: 0,
            featuredJobsUsed: 0,
          },
        });

        console.log(`Added subscription for user ${employer.email}`);
      } else {
        console.log(
          `User ${employer.email} already has an active subscription`
        );
      }
    }

    console.log("Finished adding subscriptions for all employers");
  } catch (error) {
    console.error("Error adding subscriptions:", error);
  }
}

// Main function
async function main() {
  try {
    const plan = await createDefaultPlan();
    await addSubscriptionsForEmployers(plan);
    console.log("Script completed successfully");
    process.exit(0);
  } catch (error) {
    console.error("Error running script:", error);
    process.exit(1);
  }
}

// Run the script
main();
