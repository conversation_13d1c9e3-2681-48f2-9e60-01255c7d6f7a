import type { AxiosRequestConfig } from "axios";
import api from "./api";

// Define response types for better type safety
interface ApiResponse<T> {
  success: boolean;
  [key: string]: any;
  data?: T;
}

interface CompanyResponse {
  success: boolean;
  company: {
    _id: string;
    name: string;
    description: string;
    logo?: string;
    website?: string;
    industry: string;
    size: string;
    location: string;
    foundedYear?: number;
    owner: string;
    isVerified: boolean;
    isActive: boolean;
    socialLinks: {
      linkedin?: string;
      twitter?: string;
      facebook?: string;
      instagram?: string;
    };
    contactEmail: string;
    contactPhone?: string;
    companyType?: string;
    createdAt: string;
    updatedAt: string;
  };
}

interface CompaniesResponse {
  success: boolean;
  count: number;
  companies: Array<{
    _id: string;
    name: string;
    description: string;
    logo?: string;
    website?: string;
    industry: string;
    size: string;
    location: string;
    foundedYear?: number;
    owner: string;
    isVerified: boolean;
    isActive: boolean;
    socialLinks: {
      linkedin?: string;
      twitter?: string;
      facebook?: string;
      instagram?: string;
    };
    contactEmail: string;
    contactPhone?: string;
    companyType?: string;
    createdAt: string;
    updatedAt: string;
  }>;
}

/**
 * Employer API service for handling all employer-related API calls
 */
const employerApi = {
  /**
   * Company management
   */
  company: {
    /**
     * Get companies owned by the current user
     */
    getMyCompanies: () => api.get<CompaniesResponse>("/companies/my/companies"),

    /**
     * Get company by ID
     * @param id Company ID
     */
    getCompanyById: (id: string) =>
      api.get<CompanyResponse>(`/companies/${id}`),

    /**
     * Create a new company
     * @param data Company data
     */
    createCompany: (data: any) => api.post<CompanyResponse>("/companies", data),

    /**
     * Update company
     * @param id Company ID
     * @param data Company data to update
     */
    updateCompany: (id: string, data: any) =>
      api.patch<CompanyResponse>(`/companies/${id}`, data),

    /**
     * Delete company
     * @param id Company ID
     */
    deleteCompany: (id: string) =>
      api.delete<ApiResponse<null>>(`/companies/${id}`),

    /**
     * Upload company logo
     * @param id Company ID
     * @param logo Logo file
     */
    uploadLogo: (id: string, logo: File) => {
      const formData = new FormData();
      formData.append("logo", logo);

      const config: AxiosRequestConfig = {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      };

      return api.post<CompanyResponse>(
        `/companies/${id}/upload-logo`,
        formData,
        config,
      );
    },

    /**
     * Remove company logo
     * @param id Company ID
     */
    removeLogo: (id: string) => {
      return api.delete<CompanyResponse>(`/companies/${id}/remove-logo`);
    },

    /**
     * Request company verification
     * @param id Company ID
     * @param data Additional verification data
     */
    requestVerification: (id: string, data?: any) =>
      api.post<ApiResponse<{ verificationStatus: any }>>(
        `/companies/${id}/request-verification`,
        data || {},
      ),

    /**
     * Get company statistics
     * @param id Company ID
     */
    getCompanyStats: (id: string) =>
      api.get<ApiResponse<{ stats: any }>>(`/companies/${id}/stats`),

    /**
     * Get company verification status
     * @param id Company ID
     */
    getVerificationStatus: (id: string) =>
      api.get<ApiResponse<{ verificationStatus: any }>>(
        `/companies/${id}/verification-status`,
      ),

    /**
     * Get all industries
     */
    getIndustries: () =>
      api.get<ApiResponse<{ industries: string[] }>>("/companies/industries"),
  },

  /**
   * Job management
   */
  jobs: {
    /**
     * Get jobs posted by the current user's companies
     */
    getMyJobs: () => api.get<ApiResponse<{ jobs: any[] }>>("/jobs/my/jobs"),

    /**
     * Get job by ID
     * @param id Job ID
     */
    getJobById: (id: string) =>
      api.get<ApiResponse<{ job: any }>>(`/jobs/${id}`),

    /**
     * Create a new job
     * @param data Job data
     */
    createJob: (data: any) =>
      api.post<ApiResponse<{ job: any }>>("/jobs", data),

    /**
     * Update job
     * @param id Job ID
     * @param data Job data to update
     */
    updateJob: (id: string, data: any) =>
      api.patch<ApiResponse<{ job: any }>>(`/jobs/${id}`, data),

    /**
     * Delete job
     * @param id Job ID
     */
    deleteJob: (id: string) => api.delete<ApiResponse<null>>(`/jobs/${id}`),
  },

  /**
   * Profile management
   */
  profile: {
    /**
     * Get current employer's profile
     */
    getMyProfile: () =>
      api.get<ApiResponse<{ profile: any }>>("/employer/profiles/me"),

    /**
     * Update employer profile
     * @param data Profile data to update
     */
    updateProfile: (data: any) =>
      api.patch<ApiResponse<{ profile: any }>>("/employer/profiles/me", data),

    /**
     * Upload profile image
     * @param file Profile image file
     */
    uploadProfileImage: (file: File) => {
      const formData = new FormData();
      formData.append("profileImage", file);

      const config: AxiosRequestConfig = {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      };

      return api.post<ApiResponse<{ profileImageUrl: string; user: any }>>(
        "/employer/profiles/profile-image",
        formData,
        config,
      );
    },
  },

  /**
   * Dashboard
   */
  dashboard: {
    /**
     * Get employer dashboard statistics
     */
    getStats: () =>
      api.get<
        ApiResponse<{
          stats: {
            companyName: string;
            activeJobsCount: number;
            totalJobsCount: number;
            totalApplications: number;
            newApplications: number;
            interviewsScheduled: number;
            profileCompletion: number;
            jobCategories: Array<{ _id: string; count: number }>;
            jobStatus: Array<{ name: string; value: number; color: string }>;
            applicationTrends: Array<{ date: string; Applications: number }>;
          };
        }>
      >("/employer/dashboard/stats"),

    /**
     * Get recent activities
     */
    getRecentActivities: () =>
      api.get<
        ApiResponse<{
          activities: Array<{
            id: string;
            type: string;
            message: string;
            date: string;
            status?: string;
            data?: any;
          }>;
        }>
      >("/employer/dashboard/activities"),
  },

  /**
   * Candidates
   */
  candidates: {
    /**
     * Get candidates who applied to jobs
     */
    // We'll use the getAllApplications endpoint for admin, but it's restricted
    // For now, we'll use the job-specific endpoint with a specific job ID
    getApplicants: async (params?: any) => {
      try {
        // If we have a jobId in params, use the job-specific endpoint
        if (params?.jobId) {
          return await api.get<
            ApiResponse<{ applications: any[]; count: number }>
          >(`/applications/job/${params.jobId}`, {
            params: { ...params, jobId: undefined },
          });
        }

        // Otherwise, get the user's jobs and then get applications for the first job
        const jobsResponse =
          await api.get<ApiResponse<{ jobs: any[] }>>("/jobs/my/jobs");

        if (
          jobsResponse.data.success &&
          jobsResponse.data.jobs &&
          jobsResponse.data.jobs.length > 0
        ) {
          const firstJobId = jobsResponse.data.jobs[0]._id;
          return await api.get<
            ApiResponse<{ applications: any[]; count: number }>
          >(`/applications/job/${firstJobId}`, { params });
        }

        // If no jobs found, create a mock response with the same structure as the API response
        return {
          data: {
            success: true,
            applications: [],
            count: 0,
          },
          status: 200,
          statusText: "OK",
          headers: {},
          config: {},
        };
      } catch (error) {
        console.error("Error in getApplicants:", error);
        throw error;
      }
    },

    /**
     * Get candidate profile
     * @param userId User ID
     */
    getCandidateProfile: (userId: string) =>
      api.get<ApiResponse<{ profile: any }>>(
        `/candidate-profiles/user/${userId}`,
      ),

    /**
     * Update application status
     * @param applicationId Application ID
     * @param status New status
     */
    updateApplicationStatus: (applicationId: string, status: string) =>
      api.patch<ApiResponse<{ application: any }>>(
        `/applications/${applicationId}/status`,
        { status },
      ),

    /**
     * Get applications for a specific job
     * @param jobId Job ID
     */
    getJobApplications: (jobId: string) =>
      api.get<ApiResponse<{ applications: any[]; count: number }>>(
        `/applications/job/${jobId}`,
      ),
  },
};

export default employerApi;
