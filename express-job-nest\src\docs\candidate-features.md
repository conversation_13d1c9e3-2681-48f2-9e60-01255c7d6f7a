# Candidate Features for Job Nest

This document outlines the candidate features implemented in the Job Nest application.

## Overview

The candidate module provides functionality for job seekers to:

1. Create and manage their professional profiles
2. Search for jobs with advanced filtering
3. Receive personalized job recommendations
4. Track their job applications
5. View similar jobs to ones they're interested in

## API Endpoints

### Candidate Profile

#### Get Current User's Profile
- **Endpoint**: `GET /api/candidate-profiles/me`
- **Description**: Retrieves the current user's candidate profile
- **Authentication**: Required (Candidate)
- **Response**: Returns the candidate profile or creates a new one if it doesn't exist

#### Update Current User's Profile
- **Endpoint**: `PATCH /api/candidate-profiles/me`
- **Description**: Updates the current user's candidate profile
- **Authentication**: Required (Candidate)
- **Request Body**: Profile fields to update
- **Response**: Returns the updated profile

#### Education Management
- **Add Education**: `POST /api/candidate-profiles/education`
- **Update Education**: `PATCH /api/candidate-profiles/education/:educationId`
- **Delete Education**: `DELETE /api/candidate-profiles/education/:educationId`

#### Experience Management
- **Add Experience**: `POST /api/candidate-profiles/experience`
- **Update Experience**: `PATCH /api/candidate-profiles/experience/:experienceId`
- **Delete Experience**: `DELETE /api/candidate-profiles/experience/:experienceId`

#### Get Candidate Profile (For Employers)
- **Endpoint**: `GET /api/candidate-profiles/user/:userId`
- **Description**: Retrieves a candidate's profile by user ID
- **Authentication**: Required (Employer or Admin)
- **Response**: Returns the candidate profile

### Job Recommendations

#### Get Recommended Jobs
- **Endpoint**: `GET /api/job-recommendations/recommended`
- **Description**: Retrieves job recommendations based on candidate's profile
- **Authentication**: Required (Candidate)
- **Response**: Returns a list of recommended jobs

#### Get Similar Jobs
- **Endpoint**: `GET /api/job-recommendations/similar/:jobId`
- **Description**: Retrieves jobs similar to a specific job
- **Authentication**: Required (Candidate)
- **Response**: Returns a list of similar jobs

#### Get Application Statistics
- **Endpoint**: `GET /api/job-recommendations/application-stats`
- **Description**: Retrieves statistics about the candidate's job applications
- **Authentication**: Required (Candidate)
- **Response**: Returns application statistics and recent applications

### Job Search

#### Search Jobs
- **Endpoint**: `GET /api/job-search/search`
- **Description**: Searches for jobs with advanced filtering
- **Authentication**: Not required
- **Query Parameters**:
  - `keyword`: Search term for job title, description, skills, etc.
  - `location`: Filter by job location
  - `category`: Filter by job category
  - `jobType`: Filter by job type (full-time, part-time, etc.)
  - `experienceLevel`: Filter by required experience level
  - `salary`: Filter by salary range (format: min-max)
  - `company`: Filter by company name
  - `postedWithin`: Filter by posting date (in days)
  - `page`: Page number for pagination
  - `limit`: Number of results per page
  - `sortBy`: Field to sort by
  - `sortOrder`: Sort order (asc or desc)
- **Response**: Returns paginated job results

#### Get Filter Options
- **Get Categories**: `GET /api/job-search/categories`
- **Get Job Types**: `GET /api/job-search/types`
- **Get Experience Levels**: `GET /api/job-search/experience-levels`
- **Get Locations**: `GET /api/job-search/locations`

## Data Models

### Candidate Profile

The candidate profile model includes:

- **Basic Information**: User reference, headline, summary
- **Skills**: List of skills
- **Experience**: Work history with company, position, dates, and description
- **Education**: Academic history with institution, degree, dates, and field of study
- **Certifications**: Professional certifications
- **Languages**: Language proficiency
- **References**: Professional references
- **Portfolio Links**: Links to portfolio items
- **Social Links**: LinkedIn, GitHub, Twitter, personal website
- **Job Preferences**: Preferred job types, locations, salary range
- **Availability**: When the candidate is available to start
- **Resume**: Link to uploaded resume
- **Profile Completeness**: Percentage indicating profile completion

## Usage Examples

### Creating a Candidate Profile

```javascript
// Example request to create/update a profile
const response = await fetch('/api/candidate-profiles/me', {
  method: 'PATCH',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer YOUR_TOKEN'
  },
  body: JSON.stringify({
    headline: 'Senior Frontend Developer',
    summary: 'Experienced developer with 5+ years in React and TypeScript',
    skills: ['React', 'TypeScript', 'Node.js', 'GraphQL'],
    preferredJobTypes: ['Full-time', 'Remote'],
    preferredLocations: ['New York', 'Remote'],
    preferredSalary: {
      min: 100000,
      max: 150000,
      currency: 'USD'
    }
  })
});

const data = await response.json();
console.log(data.profile);
```

### Searching for Jobs

```javascript
// Example request to search for jobs
const response = await fetch('/api/job-search/search?keyword=react&location=new%20york&jobType=Full-time&page=1&limit=10', {
  method: 'GET',
  headers: {
    'Content-Type': 'application/json'
  }
});

const data = await response.json();
console.log(data.jobs);
```

### Getting Job Recommendations

```javascript
// Example request to get job recommendations
const response = await fetch('/api/job-recommendations/recommended', {
  method: 'GET',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer YOUR_TOKEN'
  }
});

const data = await response.json();
console.log(data.jobs);
```

## Integration with Frontend

The frontend should:

1. Provide forms for creating and updating candidate profiles
2. Display job search results with filtering options
3. Show job recommendations on the candidate dashboard
4. Track application status and display statistics
5. Allow candidates to upload resumes and other documents
