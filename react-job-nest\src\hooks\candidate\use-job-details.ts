import { mockJobDetails } from "@/data/job-details";
import candidateApi from "@/services/candidate-api";
import { type Job } from "@/types";
import { notifications } from "@mantine/notifications";
import { useEffect, useState } from "react";

interface JobDetailsResponse {
  job: {
    _id: string;
    title: string;
    company: {
      _id: string;
      name: string;
      logo?: string;
      location?: string;
      description?: string;
      website?: string;
      socialLinks?: {
        linkedin?: string;
        twitter?: string;
        facebook?: string;
      };
    };
    description: string;
    requirements: string;
    jobType: string;
    location: string;
    category: string;
    tags: string[];
    minSalary?: number;
    maxSalary?: number;
    currency?: string;
    showSalary: boolean;
    benefits: string[];
    applicationDeadline?: string;
    isActive: boolean;
    questions: string[];
    createdAt: string;
    updatedAt: string;
    requiredExperience: string;
  };
}

interface UseJobDetailsReturn {
  jobDetails: Job | null;
  isLoading: boolean;
  error: string | null;
  companyInfo: {
    name: string;
    description?: string;
    website?: string;
    location?: string;
    socialLinks?: {
      linkedin?: string;
      twitter?: string;
      facebook?: string;
    };
  } | null;
}

export default function useJobDetails(
  jobId: string | undefined,
): UseJobDetailsReturn {
  const [jobDetails, setJobDetails] = useState<Job | null>(null);
  const [companyInfo, setCompanyInfo] = useState<{
    name: string;
    description?: string;
    website?: string;
    location?: string;
    socialLinks?: {
      linkedin?: string;
      twitter?: string;
      facebook?: string;
    };
  } | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchJobDetails = async () => {
      if (!jobId) {
        setError("Job ID is required");
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);

        const response = await candidateApi.jobs.getJobById(jobId);

        if (response.data && response.data.success) {
          const jobData = response.data.job as JobDetailsResponse["job"];

          // Format salary string
          const salaryString =
            jobData.showSalary && jobData.minSalary && jobData.maxSalary
              ? `${jobData.currency || "$"}${jobData.minSalary.toLocaleString()} - ${jobData.currency || "$"}${jobData.maxSalary.toLocaleString()}`
              : "Salary not disclosed";

          // Map backend job data to frontend format
          const mappedJob: Job = {
            id: jobData._id,
            _id: jobData._id,
            title: jobData.title,
            company: jobData.company.name,
            location: jobData.location,
            type: jobData.jobType,
            workType: jobData.location, // Backend uses location for what frontend calls workType
            salary: salaryString,
            experience: jobData.requiredExperience,
            skills: jobData.tags || [],
            datePosted: jobData.createdAt,
            category: jobData.category,
            description: jobData.description,
            requirements: jobData.requirements,
            benefits: jobData.benefits,
            applicationDeadline: jobData.applicationDeadline,
            questions: jobData.questions,
          };

          setJobDetails(mappedJob);

          // Set company info
          setCompanyInfo({
            name: jobData.company.name,
            description: jobData.company.description,
            website: jobData.company.website,
            location: jobData.company.location,
            socialLinks: jobData.company.socialLinks,
          });
        } else {
          throw new Error("Failed to fetch job details");
        }
      } catch (error: any) {
        console.error("Error fetching job details:", error);

        // Show error notification
        notifications.show({
          title: "Error",
          message: error.message || "Failed to fetch job details",
          color: "red",
        });

        setError(error.message || "Failed to fetch job details");

        // In development mode, use mock data if backend is not available
        if (process.env.NODE_ENV === "development") {
          console.log("Using mock job details data");
          setJobDetails(mockJobDetails as unknown as Job);
          setCompanyInfo(mockJobDetails.companyInfo);
          setError(null);
        }
      } finally {
        setIsLoading(false);
      }
    };

    fetchJobDetails();
  }, [jobId]);

  return {
    jobDetails,
    isLoading,
    error,
    companyInfo,
  };
}
