import { useJobsStore, useJobsViewModeStore } from "@/stores/candidate-store";
import { Alert, Button, Card, Center, Grid, Loader, Text } from "@mantine/core";
import { FaBriefcase } from "react-icons/fa";
import JobCard from "./JobCard";

export default function JobsList() {
  const viewMode = useJobsViewModeStore((state) => state.viewMode);
  const { jobs, isLoading, error } = useJobsStore((state) => state);

  if (isLoading) {
    return (
      <div className="flex justify-center p-8">
        <Loader size="xl" />
      </div>
    );
  }

  if (error) {
    return (
      <Alert color="red" title="Error" variant="filled">
        {error}
      </Alert>
    );
  }

  if (jobs.length === 0) {
    return (
      <Card withBorder p="xl" radius="md" className="text-center">
        <Center className="flex-col gap-4 py-8">
          <div className="rounded-full bg-gray-100 p-6">
            <FaBriefcase size={40} className="text-gray-400" />
          </div>
          <Text size="xl" fw={600}>
            No jobs found
          </Text>
          <Text c="dimmed" className="max-w-md">
            We couldn&apos;t find any jobs matching your search criteria. Try
            adjusting your filters or search for something else.
          </Text>
          <Button
            variant="outline"
            mt="md"
            onClick={() => {
              // Reset filters functionality would go here
            }}
          >
            Clear Filters
          </Button>
        </Center>
      </Card>
    );
  }

  const renderJobCards = () => {
    return jobs.map((job) =>
      viewMode === "grid" ? (
        <Grid.Col key={job.id} span={{ base: 12, sm: 6 }}>
          <JobCard job={job} />
        </Grid.Col>
      ) : (
        <Grid.Col key={job.id} span={12}>
          <JobCard job={job} />
        </Grid.Col>
      ),
    );
  };

  return (
    <div className="space-y-4">
      <Grid gutter="md">{renderJobCards()}</Grid>
    </div>
  );
}
