import { Request, Response, NextFunction } from "express";
import { catchAsync } from "../utils/catch-async";
import Job from "../models/job.model";
import Application from "../models/application.model";
import Company from "../models/company.model";
import { AppError } from "../utils/app-error";

// Get employer dashboard statistics
export const getDashboardStats = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    // Get the employer's user ID
    const userId = req.user._id;

    // Find the employer's company
    const company = await Company.findOne({ owner: userId });

    if (!company) {
      return next(new AppError("No company found for this user", 404));
    }

    // Get company name
    const companyName = company.name;

    // Get job counts
    const totalJobsCount = await Job.countDocuments({ company: company._id });
    const activeJobsCount = await Job.countDocuments({
      company: company._id,
      isActive: true,
    });

    // Get job categories
    const jobCategories = await Job.aggregate([
      { $match: { company: company._id } },
      { $group: { _id: "$category", count: { $sum: 1 } } },
      { $sort: { count: -1 } },
    ]);

    // Get job status distribution
    const activeJobs = await Job.countDocuments({
      company: company._id,
      isActive: true,
    });

    const closedJobs = await Job.countDocuments({
      company: company._id,
      isActive: false,
    });

    const jobStatus = [
      { name: "Active", value: activeJobs, color: "blue" },
      { name: "Closed", value: closedJobs, color: "red" },
    ];

    // Get application counts
    const jobIds = await Job.find({ company: company._id }).distinct("_id");

    const totalApplications = await Application.countDocuments({
      job: { $in: jobIds },
    });

    const newApplications = await Application.countDocuments({
      job: { $in: jobIds },
      status: "pending",
      createdAt: { $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) }, // Last 7 days
    });

    const interviewsScheduled = await Application.countDocuments({
      job: { $in: jobIds },
      status: "interview",
    });

    // Calculate profile completion percentage
    let profileCompletion = 0;
    const requiredFields = [
      "name",
      "description",
      "industry",
      "size",
      "location",
      "contactEmail",
    ];
    const optionalFields = [
      "logo",
      "website",
      "foundedYear",
      "contactPhone",
      "socialLinks",
    ];

    // Count required fields
    let completedRequiredFields = 0;
    for (const field of requiredFields) {
      if (company.get(field)) completedRequiredFields++;
    }

    // Count optional fields
    let completedOptionalFields = 0;
    for (const field of optionalFields) {
      if (field === "socialLinks") {
        if (
          company.socialLinks &&
          Object.values(company.socialLinks).some((link) => link)
        ) {
          completedOptionalFields++;
        }
      } else if (company.get(field)) {
        completedOptionalFields++;
      }
    }

    // Calculate percentage (required fields have more weight)
    profileCompletion = Math.round(
      ((completedRequiredFields / requiredFields.length) * 0.7 +
        (completedOptionalFields / optionalFields.length) * 0.3) *
        100
    );

    // Get application trends (by month for the current year)
    const currentYear = new Date().getFullYear();
    const months = [
      "Jan",
      "Feb",
      "Mar",
      "Apr",
      "May",
      "Jun",
      "Jul",
      "Aug",
      "Sep",
      "Oct",
      "Nov",
      "Dec",
    ];

    // Initialize trends with all months
    const applicationTrends = months.map((month) => ({
      date: month,
      Applications: 0,
    }));

    // Get application counts by month
    const applicationsByMonth = await Application.aggregate([
      {
        $match: {
          job: { $in: jobIds },
          createdAt: {
            $gte: new Date(currentYear, 0, 1),
            $lt: new Date(currentYear + 1, 0, 1),
          },
        },
      },
      {
        $group: {
          _id: { $month: "$createdAt" },
          count: { $sum: 1 },
        },
      },
      { $sort: { _id: 1 } },
    ]);

    // Update trends with actual data
    applicationsByMonth.forEach((item) => {
      const monthIndex = item._id - 1; // MongoDB months are 1-based
      if (monthIndex >= 0 && monthIndex < 12) {
        applicationTrends[monthIndex].Applications = item.count;
      }
    });

    // Get candidate progress data
    const candidateProgress = await Application.aggregate([
      { $match: { job: { $in: jobIds } } },
      { $group: { _id: "$status", count: { $sum: 1 } } },
      { $sort: { count: -1 } },
    ]);

    // Format candidate progress data for the frontend
    const formattedCandidateProgress = candidateProgress.map((item) => {
      // Convert status to a more readable label
      let label = item._id;
      if (item._id === "pending") label = "Applied";
      if (item._id === "shortlisted") label = "Shortlisted";
      if (item._id === "interview") label = "Interviewing";
      if (item._id === "offered") label = "Offered";
      if (item._id === "hired") label = "Hired";
      if (item._id === "rejected") label = "Rejected";

      return {
        label,
        value: item.count,
      };
    });

    // Prepare stats object
    const stats = {
      companyName,
      activeJobsCount,
      totalJobsCount,
      totalApplications,
      newApplications,
      interviewsScheduled,
      profileCompletion,
      jobCategories,
      jobStatus,
      applicationTrends,
      candidateProgress: formattedCandidateProgress,
    };

    // Return the stats
    res.status(200).json({
      success: true,
      stats,
    });
  }
);

// Get employer dashboard activities
export const getDashboardActivities = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    // Get the employer's user ID
    const userId = req.user._id;

    // Find the employer's company
    const company = await Company.findOne({ owner: userId });

    if (!company) {
      return next(new AppError("No company found for this user", 404));
    }

    // Get all jobs for this company
    const jobs = await Job.find({ company: company._id }).select("_id title");
    const jobIds = jobs.map((job) => job._id);

    // Get recent applications (last 30 days)
    const recentApplications = await Application.find({
      job: { $in: jobIds },
      createdAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) },
    })
      .populate({
        path: "candidate",
        select: "name email",
      })
      .populate({
        path: "job",
        select: "title",
      })
      .sort({ createdAt: -1 })
      .limit(10);

    // Get applications with status changes (last 30 days)
    const recentStatusChanges = await Application.find({
      job: { $in: jobIds },
      updatedAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) },
      // Only include applications that have been updated (status changed)
      $expr: { $ne: ["$createdAt", "$updatedAt"] },
    })
      .populate({
        path: "candidate",
        select: "name email",
      })
      .populate({
        path: "job",
        select: "title",
      })
      .sort({ updatedAt: -1 })
      .limit(5);

    // Get recently created jobs (last 30 days)
    const recentJobs = await Job.find({
      company: company._id,
      createdAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) },
    })
      .select("title createdAt isActive")
      .sort({ createdAt: -1 })
      .limit(5);

    // Get jobs with upcoming deadlines (next 7 days)
    const upcomingDeadlines = await Job.find({
      company: company._id,
      isActive: true,
      applicationDeadline: {
        $gte: new Date(),
        $lte: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
      },
    })
      .select("title applicationDeadline")
      .sort({ applicationDeadline: 1 })
      .limit(5);

    // Define activity interface
    interface Activity {
      id: string;
      type: string;
      message: string;
      date: string;
      status: string;
      data: Record<string, any>;
    }

    // Combine all activities
    const activities: Activity[] = [];

    // Add application activities
    recentApplications.forEach((app: any) => {
      const candidate = app.candidate as any;
      const job = app.job as any;

      activities.push({
        id: app._id.toString(),
        type: "application",
        message: `New application from ${candidate.name} for ${job.title}`,
        date: app.createdAt.toISOString(),
        status: "new",
        data: {
          applicationId: app._id.toString(),
          userId: candidate._id.toString(),
          jobId: job._id.toString(),
          jobTitle: job.title,
          candidateName: candidate.name,
          candidateEmail: candidate.email,
        },
      });
    });

    // Add status change activities
    recentStatusChanges.forEach((app: any) => {
      const candidate = app.candidate as any;
      const job = app.job as any;

      activities.push({
        id: `status-${app._id.toString()}`,
        type: "status-change",
        message: `Application status updated to ${app.status} for ${candidate.name}`,
        date: app.updatedAt.toISOString(),
        status: app.status,
        data: {
          applicationId: app._id.toString(),
          userId: candidate._id.toString(),
          jobId: job._id.toString(),
          jobTitle: job.title,
          candidateName: candidate.name,
          candidateEmail: candidate.email,
          status: app.status,
        },
      });
    });

    // Add job creation activities
    recentJobs.forEach((job: any) => {
      activities.push({
        id: `job-${job._id.toString()}`,
        type: "job",
        message: `New job posted: ${job.title}`,
        date: job.createdAt.toISOString(),
        status: job.isActive ? "active" : "inactive",
        data: {
          jobId: job._id.toString(),
          jobTitle: job.title,
          isActive: job.isActive,
        },
      });
    });

    // Add deadline activities
    upcomingDeadlines.forEach((job: any) => {
      if (job.applicationDeadline) {
        activities.push({
          id: `deadline-${job._id.toString()}`,
          type: "deadline",
          message: `Application deadline approaching for ${job.title}`,
          date: job.applicationDeadline.toISOString(),
          status: "warning",
          data: {
            jobId: job._id.toString(),
            jobTitle: job.title,
            deadline: job.applicationDeadline.toISOString(),
          },
        });
      }
    });

    // Sort activities by date (newest first)
    activities.sort(
      (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()
    );

    // Limit to 20 most recent activities
    const limitedActivities = activities.slice(0, 20);

    // Return the activities
    res.status(200).json({
      success: true,
      activities: limitedActivities,
    });
  }
);
