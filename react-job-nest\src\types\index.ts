export interface Candidate {
  id: number;
  name: string;
  email: string;
  status: string;
  jobId: number;
  appliedDate: string;
  skills?: string[];
}

export interface Job {
  id?: number | string;
  _id?: string;
  title: string;
  company:
    | string
    | {
        _id: string;
        name: string;
        logo?: string;
      };
  location: string;
  type?: string;
  jobType?: string; // Backend uses jobType instead of type
  workType?: string;
  salary?: string;
  minSalary?: number;
  maxSalary?: number;
  currency?: string;
  experience?: string;
  requiredExperience?: string; // Backend uses requiredExperience
  skills?: string[];
  tags?: string[]; // Backend uses tags instead of skills
  datePosted?: string;
  createdAt?: string; // Backend uses createdAt instead of datePosted
  updatedAt?: string;
  careerLevel?: string;
  category?: string;
  description: string;
  requirements?: string | string[];
  benefits?: string[];
  applicationDeadline?: string;
  questions?: string[];
  isActive?: boolean;
  status?: "Active" | "Pending" | "Closed";
}
