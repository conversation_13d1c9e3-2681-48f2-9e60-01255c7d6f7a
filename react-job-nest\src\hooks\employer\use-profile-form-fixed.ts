import employerApi from "@/services/employer-api";
import { useAuthStore } from "@/stores/auth-store";
import { getFullUrl } from "@/utils/url-utils";
import { useForm } from "@mantine/form";
import { notifications } from "@mantine/notifications";
import { zodResolver } from "mantine-form-zod-resolver";
import { useCallback, useEffect, useState } from "react";
import { z } from "zod";
import { profileSchema } from "../../schemas/employer/profile-schema";

// Type for form values
type FormValues = z.infer<typeof profileSchema>;

// Company type from backend
interface Company {
  _id: string;
  name: string;
  description: string;
  logo?: string;
  website?: string;
  industry: string;
  size: string;
  location: string;
  foundedYear?: number;
  owner: string;
  isVerified: boolean;
  isActive: boolean;
  socialLinks: {
    linkedin?: string;
    twitter?: string;
    facebook?: string;
    instagram?: string;
  };
  contactEmail: string;
  contactPhone?: string;
  createdAt: string;
  updatedAt: string;
}

// Mock data for development
const mockCompanyData: FormValues = {
  companyName: "Tech Corp",
  contactPerson: "<PERSON>",
  email: "<EMAIL>",
  phone: "1234567890",
  website: "https://techcorp.com",
  companyDescription:
    "A leading tech company specializing in software solutions.",
  industry: "Technology",
  location: "San Francisco, CA",
  companyLogo: undefined,
  linkedin: "https://linkedin.com/company/techcorp",
  twitter: "https://twitter.com/techcorp",
  facebook: "https://facebook.com/techcorp",
  instagram: "https://instagram.com/techcorp",
  companySize: "201-500",
  companyType: "Startup",
  foundedYear: 2010,
  verificationRequested: false,
};

// Company statistics interface
interface CompanyStats {
  activeJobs: number;
  totalJobs: number;
  totalApplications: number;
  newApplications: number;
  viewCount: number;
  averageResponseTime: number;
}

// Verification status interface
interface VerificationStatus {
  isVerified: boolean;
  requestedAt?: string;
  verifiedAt?: string;
  status: "not_requested" | "pending" | "verified" | "rejected";
  rejectionReason?: string;
}

export default function useProfileForm() {
  const [isLoading, setIsLoading] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const [company, setCompany] = useState<Company | null>(null);
  const [logoPreview, setLogoPreview] = useState<string | null>(null);
  const [companyStats, setCompanyStats] = useState<CompanyStats | null>(null);
  const [verificationStatus, setVerificationStatus] =
    useState<VerificationStatus>({
      isVerified: false,
      status: "not_requested",
    });
  const [industries, setIndustries] = useState<string[]>([]);
  const { user } = useAuthStore();

  const form = useForm<FormValues>({
    initialValues: {
      companyName: "",
      contactPerson: "",
      email: "",
      phone: "",
      website: "",
      companyDescription: "",
      industry: "",
      location: "",
      companyLogo: undefined,
      linkedin: "",
      twitter: "",
      facebook: "",
      instagram: "",
      companySize: "",
      companyType: "",
      foundedYear: null,
      verificationRequested: false,
    },
    validate: zodResolver(profileSchema),
  });

  // Initialize form with mock data for development
  const initializeWithMockData = useCallback(() => {
    form.setValues(mockCompanyData);
    setIsInitialized(true);
  }, [form]);

  // Fetch industries for dropdown
  const fetchIndustries = useCallback(async () => {
    try {
      // In development mode, use predefined industries
      if (process.env.NODE_ENV === "development") {
        // Import industries from schema
        const { INDUSTRIES } = await import(
          "@/schemas/employer/profile-schema"
        );
        setIndustries(INDUSTRIES);
        return;
      }

      // In production, fetch from API
      const response = await employerApi.company.getIndustries();
      if (response.data.success) {
        setIndustries(response.data.industries);
      }
    } catch (error) {
      console.warn("Error fetching industries:", error);
      // Fallback to predefined industries
      const { INDUSTRIES } = await import("@/schemas/employer/profile-schema");
      setIndustries(INDUSTRIES);
    }
  }, []);

  // Fetch company statistics
  const fetchCompanyStats = useCallback(async (companyId: string) => {
    try {
      const response = await employerApi.company.getCompanyStats(companyId);
      if (response.data.success) {
        setCompanyStats(response.data.stats);
      }
    } catch (error: any) {
      console.warn("Error fetching company stats:", error);

      // Always use mock stats in development mode or if we get a 403 error
      // This handles the case where the endpoint doesn't exist or user doesn't have permission
      if (
        process.env.NODE_ENV === "development" ||
        error?.response?.status === 403
      ) {
        console.log("Using mock company stats data");
        setCompanyStats({
          activeJobs: 5,
          totalJobs: 12,
          totalApplications: 87,
          newApplications: 14,
          viewCount: 342,
          averageResponseTime: 2.5, // days
        });
      }
    }
  }, []);

  // Fetch verification status
  const fetchVerificationStatus = useCallback(async (companyId: string) => {
    try {
      const response =
        await employerApi.company.getVerificationStatus(companyId);
      if (response.data.success) {
        setVerificationStatus(response.data.verificationStatus);
      }
    } catch (error: any) {
      console.warn("Error fetching verification status:", error);

      // Always use mock verification status in development mode or if we get a 403 error
      // This handles the case where the endpoint doesn't exist or user doesn't have permission
      if (
        process.env.NODE_ENV === "development" ||
        error?.response?.status === 403
      ) {
        console.log("Using mock verification status data");
        setVerificationStatus({
          isVerified: false,
          status: "not_requested",
        });
      }
    }
  }, []);

  // Fetch company data only once when component mounts
  useEffect(() => {
    // Skip if already initialized or no user
    if (isInitialized || !user) return;

    // Fetch industries for dropdown
    fetchIndustries();

    const fetchCompanyData = async () => {
      try {
        setIsLoading(true);

        // Try to get the user's companies
        try {
          const response = await employerApi.company.getMyCompanies();
          const data = response.data;

          if (data.success && data.companies && data.companies.length > 0) {
            const companyData = data.companies[0];
            setCompany(companyData);

            // Fetch additional company data
            fetchCompanyStats(companyData._id);
            fetchVerificationStatus(companyData._id);

            // Map company data to form values
            form.setValues({
              companyName: companyData.name,
              contactPerson: user?.name || "",
              email: companyData.contactEmail,
              phone: companyData.contactPhone || "",
              website: companyData.website || "",
              companyDescription: companyData.description,
              industry: companyData.industry,
              location: companyData.location,
              companyLogo: undefined, // File can't be set from API
              linkedin: companyData.socialLinks?.linkedin || "",
              twitter: companyData.socialLinks?.twitter || "",
              facebook: companyData.socialLinks?.facebook || "",
              instagram: companyData.socialLinks?.instagram || "",
              companySize: companyData.size,
              companyType: companyData.companyType || "Startup", // Default if not available
              foundedYear: companyData.foundedYear || null,
              verificationRequested: false, // Will be set based on verification status
            });

            // Set logo preview if available
            if (companyData.logo) {
              // Get the full URL for the logo
              const logoUrl = getFullUrl(companyData.logo);
              console.log("Setting logo preview URL:", logoUrl);
              setLogoPreview(logoUrl);
            }

            setIsInitialized(true);
            return;
          }
        } catch (error) {
          console.warn("Backend connection issue:", error);
        }

        // If we get here, either the API call failed or returned no companies
        // Use mock data instead
        initializeWithMockData();

        // Set mock stats in development mode
        setCompanyStats({
          activeJobs: 5,
          totalJobs: 12,
          totalApplications: 87,
          newApplications: 14,
          viewCount: 342,
          averageResponseTime: 2.5, // days
        });

        setVerificationStatus({
          isVerified: false,
          status: "not_requested",
        });

        setIsInitialized(true);
      } catch (error) {
        console.error("Error fetching company data:", error);
        initializeWithMockData();
      } finally {
        setIsLoading(false);
      }
    };

    fetchCompanyData();
  }, [
    user,
    isInitialized,
    initializeWithMockData,
    form,
    fetchCompanyStats,
    fetchVerificationStatus,
    fetchIndustries,
  ]);

  // Calculate profile completeness score (weighted)
  const calculateProfileCompleteness = useCallback(
    (values: FormValues): number => {
      // Define weights for different fields (total should be 100)
      const weights = {
        companyName: 10,
        contactPerson: 5,
        email: 10,
        phone: 5,
        website: 5,
        companyDescription: 15,
        industry: 10,
        location: 10,
        companyLogo: 10,
        socialLinks: 5, // Combined weight for all social links
        companySize: 5,
        companyType: 5,
        foundedYear: 5,
      };

      let score = 0;

      // Basic fields
      if (values.companyName) score += weights.companyName;
      if (values.contactPerson) score += weights.contactPerson;
      if (values.email) score += weights.email;
      if (values.phone) score += weights.phone;
      if (values.website) score += weights.website;
      if (values.companyDescription && values.companyDescription.length >= 50) {
        score += weights.companyDescription;
      } else if (values.companyDescription) {
        score += weights.companyDescription / 2; // Partial credit for short description
      }
      if (values.industry) score += weights.industry;
      if (values.location) score += weights.location;
      if (logoPreview) score += weights.companyLogo;

      // Social links (divide weight among all social links)
      const socialLinkWeight = weights.socialLinks / 4;
      if (values.linkedin) score += socialLinkWeight;
      if (values.twitter) score += socialLinkWeight;
      if (values.facebook) score += socialLinkWeight;
      if (values.instagram) score += socialLinkWeight;

      // Company details
      if (values.companySize) score += weights.companySize;
      if (values.companyType) score += weights.companyType;
      if (values.foundedYear) score += weights.foundedYear;

      return Math.min(100, Math.round(score));
    },
    [logoPreview],
  );

  // Request company verification
  const requestVerification = useCallback(async (companyId: string) => {
    try {
      setIsLoading(true);

      // In development mode, simulate a successful verification request
      if (process.env.NODE_ENV === "development") {
        // Simulate a delay for the API call
        await new Promise((resolve) => setTimeout(resolve, 1000));

        // Update verification status
        setVerificationStatus({
          isVerified: false,
          status: "pending",
          requestedAt: new Date().toISOString(),
        });

        notifications.show({
          title: "Verification Requested",
          message:
            "Your company verification request has been submitted successfully",
          color: "blue",
        });
      } else {
        // Production mode - normal flow
        const response =
          await employerApi.company.requestVerification(companyId);

        if (response.data.success) {
          setVerificationStatus(response.data.verificationStatus);

          notifications.show({
            title: "Verification Requested",
            message:
              "Your company verification request has been submitted successfully",
            color: "blue",
          });
        }
      }
    } catch (error) {
      console.error("Error requesting verification:", error);
      notifications.show({
        title: "Error",
        message: "Failed to request company verification",
        color: "red",
      });
    } finally {
      setIsLoading(false);
    }
  }, []);

  const handleSubmit = async (values: FormValues) => {
    try {
      setIsLoading(true);

      // Prepare company data for update
      const companyData = {
        name: values.companyName,
        description: values.companyDescription,
        website: values.website || undefined,
        industry: values.industry,
        size: values.companySize,
        location: values.location,
        contactEmail: values.email,
        contactPhone: values.phone || undefined,
        socialLinks: {
          linkedin: values.linkedin || undefined,
          twitter: values.twitter || undefined,
          facebook: values.facebook || undefined,
          instagram: values.instagram || undefined,
        },
        foundedYear: values.foundedYear || undefined,
        companyType: values.companyType || undefined,
      };

      // Track if logo was removed
      const logoWasRemoved = company?.logo && !logoPreview;

      console.log("Submitting company data:", companyData);

      if (!company) {
        // Create a new company if none exists
        console.log("Creating new company");
        const response = await employerApi.company.createCompany(companyData);
        console.log("Create company response:", response.data);
        const newCompany = response.data.company;
        setCompany(newCompany);

        // Handle logo upload if a new logo was selected
        if (values.companyLogo instanceof File) {
          const logoResponse = await employerApi.company.uploadLogo(
            newCompany._id,
            values.companyLogo,
          );
          console.log(
            "Logo upload response for new company:",
            logoResponse.data,
          );

          // Update company with the new data including the logo URL
          if (logoResponse.data.success && logoResponse.data.company) {
            setCompany(logoResponse.data.company);

            // Set the logo preview with the correct URL from the server
            if (logoResponse.data.company.logo) {
              const logoUrl = getFullUrl(logoResponse.data.company.logo);
              console.log(
                "Setting logo URL after upload for new company:",
                logoUrl,
              );
              setLogoPreview(logoUrl);
            }
          } else {
            // Fallback to local URL if server response doesn't include the logo
            const imageUrl = URL.createObjectURL(values.companyLogo);
            setLogoPreview(imageUrl);
          }
        }

        // Handle verification request if requested
        if (values.verificationRequested) {
          await requestVerification(newCompany._id);
        }
      } else {
        // Update existing company
        console.log("Updating existing company with ID:", company._id);
        const response = await employerApi.company.updateCompany(
          company._id,
          companyData,
        );
        console.log("Update company response:", response.data);

        // Update the company state with the updated data
        if (response.data.success && response.data.company) {
          setCompany(response.data.company);
        }

        // Handle logo upload if a new logo was selected
        if (values.companyLogo instanceof File) {
          const logoResponse = await employerApi.company.uploadLogo(
            company._id,
            values.companyLogo,
          );
          console.log("Logo upload response:", logoResponse.data);

          // Update company with the new data including the logo URL
          if (logoResponse.data.success && logoResponse.data.company) {
            setCompany(logoResponse.data.company);

            // Set the logo preview with the correct URL from the server
            if (logoResponse.data.company.logo) {
              const logoUrl = getFullUrl(logoResponse.data.company.logo);
              console.log("Setting logo URL after upload:", logoUrl);
              setLogoPreview(logoUrl);
            }
          } else {
            // Fallback to local URL if server response doesn't include the logo
            const imageUrl = URL.createObjectURL(values.companyLogo);
            setLogoPreview(imageUrl);
          }
        }
        // Handle logo removal if the logo was removed
        else if (logoWasRemoved) {
          console.log("Removing company logo");
          const logoResponse = await employerApi.company.removeLogo(
            company._id,
          );
          console.log("Logo removal response:", logoResponse.data);

          // Update company with the new data
          if (logoResponse.data.success && logoResponse.data.company) {
            setCompany(logoResponse.data.company);
            setLogoPreview(null);
          }
        }

        // Handle verification request if requested
        if (
          values.verificationRequested &&
          verificationStatus.status === "not_requested"
        ) {
          await requestVerification(company._id);
        }
      }

      notifications.show({
        title: "Success",
        message: "Company profile updated successfully",
        color: "green",
      });

      // Refresh company stats after update
      if (company) {
        fetchCompanyStats(company._id);
      }
    } catch (error) {
      console.error("Error updating company profile:", error);
      notifications.show({
        title: "Error",
        message: "Failed to update company profile",
        color: "red",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return {
    form,
    handleSubmit,
    isLoading,
    company,
    logoPreview,
    setLogoPreview,
    companyStats,
    verificationStatus,
    industries,
    calculateProfileCompleteness,
    requestVerification,
  };
}
