import employerApi from "@/services/employer-api";
import { useAuthStore } from "@/stores/auth-store";
import { notifications } from "@mantine/notifications";
import { useEffect, useState } from "react";

// Dashboard stats interface
interface DashboardStats {
  companyName: string;
  activeJobsCount: number;
  totalJobsCount: number;
  totalApplications: number;
  newApplications: number;
  interviewsScheduled: number;
  profileCompletion: number;
  jobCategories?: Array<{ _id: string; count: number }>;
  jobStatus?: Array<{ name: string; value: number; color: string }>;
  applicationTrends?: Array<{ date: string; Applications: number }>;
  candidateProgress?: Array<{ label: string; value: number }>;
}

// Recent activity interface
interface Activity {
  id: string;
  type: string;
  message: string;
  date: string;
  status?: string;
  data?: any;
}

export default function useDashboard() {
  const [isLoading, setIsLoading] = useState(true);
  const [stats, setStats] = useState<DashboardStats>({
    companyName: "",
    activeJobsCount: 0,
    totalJobsCount: 0,
    totalApplications: 0,
    newApplications: 0,
    interviewsScheduled: 0,
    profileCompletion: 0,
    jobCategories: [],
    jobStatus: [],
    applicationTrends: [],
    candidateProgress: [],
  });
  const [activities, setActivities] = useState<Activity[]>([]);
  const { user } = useAuthStore();

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setIsLoading(true);

        // Get dashboard stats from the backend
        const statsResponse = await employerApi.dashboard.getStats();
        const statsData = statsResponse.data;

        if (statsData.success) {
          setStats(statsData.stats);
        } else {
          throw new Error(
            statsData.message || "Failed to fetch dashboard stats",
          );
        }

        // Get recent activities
        const activitiesResponse =
          await employerApi.dashboard.getRecentActivities();
        const activitiesData = activitiesResponse.data;

        if (activitiesData.success) {
          setActivities(activitiesData.activities);
        } else {
          throw new Error(
            activitiesData.message || "Failed to fetch activities",
          );
        }
      } catch (error: any) {
        console.error("Error fetching dashboard data:", error);

        // Show error notification
        notifications.show({
          title: "Error",
          message: "Failed to load dashboard data",
          color: "red",
        });

        // Reset to empty data on error
        setStats({
          companyName: user?.name || "",
          activeJobsCount: 0,
          totalJobsCount: 0,
          totalApplications: 0,
          newApplications: 0,
          interviewsScheduled: 0,
          profileCompletion: 0,
          jobCategories: [],
          jobStatus: [],
          applicationTrends: [],
          candidateProgress: [],
        });

        setActivities([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchDashboardData();
  }, [user]);

  return {
    isLoading,
    stats,
    activities,
  };
}
