"use client";

import { getCandidateById, type JobApplication } from "@/data/job-applications";
import { useSelectedCandidateStore } from "@/stores/employer-store";
import { useCandidateDetailsModal } from "@/stores/modal-store";
import { Badge, Button, Card, Group, Stack, Text } from "@mantine/core";
import { useState } from "react";
import { FaCalendar, FaEye, FaUser } from "react-icons/fa";

type ApplicationCardProps = {
  application: JobApplication;
};

export default function ApplicationCard({ application }: ApplicationCardProps) {
  const [candidate] = useState(getCandidateById(application.candidateId));

  const getStatusColor = (status: string) => {
    switch (status) {
      case "pending":
        return "yellow";
      case "reviewed":
        return "blue";
      case "accepted":
        return "green";
      case "rejected":
        return "red";
      default:
        return "gray";
    }
  };

  const setSelectedCandidateId = useSelectedCandidateStore(
    (state) => state.setSelectedCandidateId,
  );
  const candidateDetailsModal = useCandidateDetailsModal();

  const handleViewCandidate = () => {
    // Set the candidate ID in the store
    setSelectedCandidateId(application.candidateId);
    // Open the modal
    candidateDetailsModal.open();
  };

  if (!candidate) {
    return null;
  }

  return (
    <Card withBorder shadow="sm" radius="md" padding="md">
      <Stack>
        <Group justify="space-between">
          <div>
            <Text size="xl" fw={600}>
              {candidate.name}
            </Text>
            <Text size="sm" c="dimmed">
              {candidate.email}
            </Text>
          </div>
          <Badge color={getStatusColor(application.status)} size="lg">
            {application.status.charAt(0).toUpperCase() +
              application.status.slice(1)}
          </Badge>
        </Group>

        <Group>
          <Group gap="xs">
            <FaUser size={14} className="text-blue-500" />
            <Text size="sm">
              Status: <strong>{candidate.status}</strong>
            </Text>
          </Group>
          <Group gap="xs">
            <FaCalendar size={14} className="text-blue-500" />
            <Text size="sm">
              Applied: {new Date(application.appliedDate).toLocaleDateString()}
            </Text>
          </Group>
        </Group>

        {candidate.skills && candidate.skills.length > 0 && (
          <Group>
            {candidate.skills.slice(0, 4).map((skill, index) => (
              <Badge key={index} color="blue" variant="light">
                {skill}
              </Badge>
            ))}
            {candidate.skills.length > 4 && (
              <Badge color="gray" variant="light">
                +{candidate.skills.length - 4} more
              </Badge>
            )}
          </Group>
        )}

        <Button
          variant="light"
          color="blue"
          leftSection={<FaEye size={14} />}
          onClick={handleViewCandidate}
        >
          View Candidate Details
        </Button>
      </Stack>
    </Card>
  );
}
