import { Badge, Group, Text } from "@mantine/core";
import AdminMainCard from "../AdminMainCard";
import AdminSectionHeading from "../AdminSectionHeading";

const recentJobs = [
  {
    id: 1,
    title: "Software Engineer",
    company: "Tech Corp",
    status: "Active",
    postedDate: "2023-10-10",
  },
  {
    id: 2,
    title: "Product Manager",
    company: "Innovate Inc",
    status: "Pending",
    postedDate: "2023-10-09",
  },
  {
    id: 3,
    title: "UX Designer",
    company: "Design Studio",
    status: "Active",
    postedDate: "2023-10-08",
  },
];

export default function RecentJobs() {
  return (
    <AdminMainCard>
      <AdminSectionHeading>Recently Posted Jobs</AdminSectionHeading>
      {recentJobs.map((job) => (
        <div key={job.id} className="mb-4 border-b pb-4">
          <Text size="lg" fw={600}>
            {job.title}
          </Text>
          <Group>
            <Text size="sm" c="dimmed">
              Company: {job.company}
            </Text>
            <Badge
              color={
                job.status === "Active"
                  ? "green"
                  : job.status === "Pending"
                    ? "yellow"
                    : "red"
              }
              variant="light"
            >
              {job.status}
            </Badge>
            <Text size="sm" c="dimmed">
              Posted on: {job.postedDate}
            </Text>
          </Group>
        </div>
      ))}
    </AdminMainCard>
  );
}
