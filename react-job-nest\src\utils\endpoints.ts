import { cache } from "@/utils/cache";

/**
 * Get the backend base URL
 * @returns The base URL of the backend server
 */
export const getBackendBaseUrl = (): string => {
  // In development, the backend is running on localhost:5000
  if (process.env.NODE_ENV === "development") {
    return "http://localhost:5000";
  }

  // In production, the backend URL would be configured differently
  // This is a placeholder for the production URL
  return "http://localhost:5000";
};

/**
 * Get the API base URL with the /api prefix
 */
export const getApiBaseUrl = (): string => {
  return `${getBackendBaseUrl()}/api`;
};

/**
 * Get the authorization header with the token
 */
export const getAuthHeader = (): string | undefined => {
  const token = cache.get("accessToken");
  return token ? `Bearer ${token}` : undefined;
};
