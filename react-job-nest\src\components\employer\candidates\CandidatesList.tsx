import { <PERSON><PERSON>, <PERSON>, Center, Grid, Lo<PERSON>, Stack, Text } from "@mantine/core";
import { FaUser } from "react-icons/fa";
import CandidateCard from "./CandidateCard";
import CandidateListItem from "./CandidateListItem";

// Define the candidate interface for this component
interface Candidate {
  _id: string;
  name: string;
  email: string;
  status: string;
  job: {
    _id: string;
    title: string;
  };
  appliedDate: string;
  skills?: string[];
  resume?: string;
  coverLetter?: string;
}

interface CandidatesListProps {
  viewMode: "grid" | "list";
  candidates: Candidate[];
  isLoading: boolean;
  onResetFilters: () => void;
  onUpdateStatus?: (candidateId: string, newStatus: string) => Promise<boolean>;
}

export default function CandidatesList({
  viewMode,
  candidates,
  isLoading,
  onResetFilters,
  onUpdateStatus,
}: CandidatesListProps) {
  // If loading, show a loader
  if (isLoading) {
    return (
      <Center py={50}>
        <Loader size="lg" />
      </Center>
    );
  }

  // If no candidates, show empty state
  if (!candidates || candidates.length === 0) {
    return (
      <Card withBorder p="xl" radius="md" className="text-center">
        <Center className="flex-col gap-4 py-8">
          <div className="rounded-full bg-gray-100 p-6">
            <FaUser size={40} className="text-gray-400" />
          </div>
          <Text size="xl" fw={600}>
            No candidates found
          </Text>
          <Text c="dimmed" className="max-w-md">
            We couldn&apos;t find any candidates matching your search criteria.
            Try adjusting your filters or search for something else.
          </Text>
          <Button variant="outline" mt="md" onClick={onResetFilters}>
            Clear Filters
          </Button>
        </Center>
      </Card>
    );
  }

  // Grid view
  if (viewMode === "grid") {
    return (
      <Grid gutter="lg">
        {candidates.map((candidate) => (
          <Grid.Col key={candidate._id} span={{ base: 12, sm: 6, lg: 4 }}>
            <CandidateCard
              job={candidate.job}
              candidate={candidate}
              onUpdateStatus={onUpdateStatus}
            />
          </Grid.Col>
        ))}
      </Grid>
    );
  }

  // List view
  return (
    <Stack gap="md">
      {candidates.map((candidate) => (
        <CandidateListItem
          key={candidate._id}
          job={candidate.job}
          candidate={candidate}
          onUpdateStatus={onUpdateStatus}
        />
      ))}
    </Stack>
  );
}
