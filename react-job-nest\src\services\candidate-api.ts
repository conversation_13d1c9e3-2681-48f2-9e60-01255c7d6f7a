import type { AxiosRequestConfig } from "axios";
import api from "./api";

// Define response types for better type safety
interface ApiResponse<T> {
  success: boolean;
  [key: string]: any;
  data?: T;
}

interface JobsResponse {
  success: boolean;
  count: number;
  totalJobs: number;
  totalPages: number;
  currentPage: number;
  jobs: Array<{
    _id: string;
    title: string;
    company: {
      _id: string;
      name: string;
      logo?: string;
    };
    description: string;
    requirements: string;
    jobType: string;
    location: string;
    category: string;
    tags: string[];
    minSalary?: number;
    maxSalary?: number;
    currency?: string;
    showSalary: boolean;
    benefits: string[];
    applicationDeadline?: string;
    isActive: boolean;
    questions: string[];
    createdAt: string;
    updatedAt: string;
    requiredExperience: string;
  }>;
}

interface ApplicationResponse {
  success: boolean;
  application: {
    _id: string;
    job: string;
    candidate: string;
    resume: string;
    coverLetter?: string;
    status: string;
    questionAnswers: {
      question: string;
      answer: string;
    }[];
    notes?: string;
    createdAt: string;
    updatedAt: string;
  };
}

interface ApplicationsResponse {
  success: boolean;
  count: number;
  applications: Array<{
    _id: string;
    job: {
      _id: string;
      title: string;
      location: string;
      jobType: string;
      company: {
        _id: string;
        name: string;
        logo?: string;
      };
    };
    status: string;
    createdAt: string;
    updatedAt: string;
  }>;
}

interface CandidateProfileResponse {
  success: boolean;
  profile: {
    _id: string;
    user: string;
    headline?: string;
    summary?: string;
    skills: string[];
    experience: Array<{
      _id: string;
      company: string;
      position: string;
      location?: string;
      startDate: string;
      endDate?: string;
      current: boolean;
      description?: string;
    }>;
    education: Array<{
      _id: string;
      institution: string;
      degree: string;
      fieldOfStudy?: string;
      startDate: string;
      endDate?: string;
      current: boolean;
      description?: string;
    }>;
    resumeUrl?: string;
    createdAt: string;
    updatedAt: string;
  };
}

interface DashboardStatsResponse {
  success: boolean;
  stats: {
    total: number;
    pending: number;
    reviewing: number;
    shortlisted: number;
    interview: number;
    offered: number;
    hired: number;
    rejected: number;
    successRate: number;
    applicationTrends: Array<{
      year: number;
      week: number;
      count: number;
      date: string;
    }>;
    jobCategories: Array<{ category: string; count: number }>;
    recentApplications: Array<{
      _id: string;
      status: string;
      createdAt: string;
      job: {
        _id: string;
        title: string;
        category: string;
        jobType: string;
        location: string;
        minSalary?: number;
        maxSalary?: number;
        company: {
          _id: string;
          name: string;
          logo?: string;
        };
      };
    }>;
  };
}

interface RecommendedJobsResponse {
  success: boolean;
  count: number;
  jobs: Array<{
    _id: string;
    title: string;
    category: string;
    jobType: string;
    location: string;
    minSalary?: number;
    maxSalary?: number;
    description: string;
    requirements: string[];
    isActive: boolean;
    createdAt: string;
    company: {
      _id: string;
      name: string;
      logo?: string;
    };
    matchScore?: number;
  }>;
}

/**
 * Candidate API service for handling all candidate-related API calls
 */
const candidateApi = {
  /**
   * Profile management
   */
  profile: {
    /**
     * Get current candidate's profile
     */
    getMyProfile: () =>
      api.get<CandidateProfileResponse>("/candidate-profiles/me"),

    /**
     * Update candidate profile
     * @param data Profile data to update
     */
    updateProfile: (data: any) =>
      api.patch<CandidateProfileResponse>("/candidate-profiles/me", data),

    /**
     * Add education to profile
     * @param data Education data
     */
    addEducation: (data: any) =>
      api.post<ApiResponse<{ education: any }>>(
        "/candidate-profiles/education",
        data,
      ),

    /**
     * Update education
     * @param id Education ID
     * @param data Education data to update
     */
    updateEducation: (id: string, data: any) =>
      api.patch<ApiResponse<{ education: any }>>(
        `/candidate-profiles/education/${id}`,
        data,
      ),

    /**
     * Delete education
     * @param id Education ID
     */
    deleteEducation: (id: string) =>
      api.delete<ApiResponse<null>>(`/candidate-profiles/education/${id}`),

    /**
     * Add experience to profile
     * @param data Experience data
     */
    addExperience: (data: any) =>
      api.post<ApiResponse<{ experience: any }>>(
        "/candidate-profiles/experience",
        data,
      ),

    /**
     * Update experience
     * @param id Experience ID
     * @param data Experience data to update
     */
    updateExperience: (id: string, data: any) =>
      api.patch<ApiResponse<{ experience: any }>>(
        `/candidate-profiles/experience/${id}`,
        data,
      ),

    /**
     * Delete experience
     * @param id Experience ID
     */
    deleteExperience: (id: string) =>
      api.delete<ApiResponse<null>>(`/candidate-profiles/experience/${id}`),

    /**
     * Upload resume
     * @param file Resume file
     */
    uploadResume: (file: File) => {
      const formData = new FormData();
      formData.append("resume", file);

      const config: AxiosRequestConfig = {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      };

      return api.post<ApiResponse<{ resumeUrl: string }>>(
        "/candidate-profiles/resume",
        formData,
        config,
      );
    },

    /**
     * Upload profile image
     * @param file Profile image file
     */
    uploadProfileImage: (file: File) => {
      const formData = new FormData();
      formData.append("profileImage", file);

      const config: AxiosRequestConfig = {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      };

      return api.post<ApiResponse<{ profileImageUrl: string }>>(
        "/candidate-profiles/profile-image",
        formData,
        config,
      );
    },

    /**
     * Add skill to profile
     * @param data Skill data
     */
    addSkill: (data: { skill: string; level?: string }) =>
      api.post<ApiResponse<{ skills: any[] }>>(
        "/candidate-profiles/skills",
        data,
      ),

    /**
     * Remove skill from profile
     * @param skill Skill to remove
     */
    removeSkill: (skill: string) =>
      api.delete<ApiResponse<{ skills: string[] }>>(
        `/candidate-profiles/skills/${skill}`,
      ),
  },

  /**
   * Job applications
   */
  applications: {
    /**
     * Get applications submitted by the current candidate
     */
    getMyApplications: () => api.get<ApplicationsResponse>("/applications/my"),

    /**
     * Get application by ID
     * @param id Application ID
     */
    getApplicationById: (id: string) =>
      api.get<ApplicationResponse>(`/applications/${id}`),

    /**
     * Submit job application
     * @param data Application data
     */
    submitApplication: (data: any) =>
      api.post<ApplicationResponse>("/applications", data),
  },

  /**
   * Job recommendations
   */
  recommendations: {
    /**
     * Get recommended jobs based on candidate profile
     */
    getRecommendedJobs: () =>
      api.get<RecommendedJobsResponse>("/job-recommendations/recommended"),

    /**
     * Get similar jobs to a specific job
     * @param jobId Job ID
     */
    getSimilarJobs: (jobId: string) =>
      api.get<RecommendedJobsResponse>(`/job-recommendations/similar/${jobId}`),

    /**
     * Get job application statistics for dashboard
     */
    getApplicationStats: () =>
      api.get<DashboardStatsResponse>("/job-recommendations/application-stats"),
  },

  /**
   * Job search
   */
  jobs: {
    /**
     * Search for jobs with filters
     * @param params Search parameters
     */
    searchJobs: (params?: {
      keyword?: string;
      location?: string;
      category?: string;
      jobType?: string;
      experienceLevel?: string;
      salary?: string;
      company?: string;
      postedWithin?: string;
      page?: number;
      limit?: number;
      sortBy?: string;
      sortOrder?: "asc" | "desc";
    }) => api.get<JobsResponse>("/job-search/search", { params }),

    /**
     * Get job by ID
     * @param id Job ID
     */
    getJobById: (id: string) =>
      api.get<ApiResponse<{ job: any }>>(`/jobs/${id}`),

    /**
     * Get job categories
     */
    getCategories: () =>
      api.get<ApiResponse<{ categories: string[] }>>("/job-search/categories"),

    /**
     * Get job types
     */
    getJobTypes: () =>
      api.get<ApiResponse<{ types: string[] }>>("/job-search/types"),

    /**
     * Get experience levels
     */
    getExperienceLevels: () =>
      api.get<ApiResponse<{ levels: string[] }>>(
        "/job-search/experience-levels",
      ),

    /**
     * Get job locations
     */
    getLocations: () =>
      api.get<ApiResponse<{ locations: string[] }>>("/job-search/locations"),
  },
};

export default candidateApi;
