"use client";

import { TextInput, type TextInputProps } from "@mantine/core";
import { type ReactNode } from "react";

interface FormInputProps extends Omit<TextInputProps, "leftSection"> {
  icon?: ReactNode;
  formProps?: any;
}

/**
 * Reusable form input component with standardized styling
 */
export default function FormInput({
  icon,
  formProps,
  ...props
}: FormInputProps) {
  return <TextInput leftSection={icon} {...formProps} {...props} />;
}
