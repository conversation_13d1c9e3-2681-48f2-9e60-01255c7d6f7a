"use client";

import { loginSchema } from "@/schemas/auth/auth-schemas";
import { useAuthStore } from "@/stores/auth-store";
import { type LoginCredentials } from "@/types/auth";
import { URLS } from "@/utils/urls";
import {
  Button,
  Divider,
  Group,
  LoadingOverlay,
  PasswordInput,
  TextInput,
} from "@mantine/core";
import { useForm } from "@mantine/form";
import { zodResolver } from "mantine-form-zod-resolver";
import { useState } from "react";
import { FaEnvelope, FaLock } from "react-icons/fa";
import { Link, useNavigate } from "react-router";

interface LoginFormProps {
  initialRole?: "employer" | "candidate" | "admin" | null;
}

export default function LoginForm({ initialRole = null }: LoginFormProps) {
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm({
    initialValues: {
      email: "",
      password: "",
    },
    validate: zod<PERSON><PERSON>olver(loginSchema),
  });

  const handleSubmit = async (values: LoginCredentials) => {
    setIsSubmitting(true);

    try {
      // Get login function from auth store
      const { login } = useAuthStore.getState();

      // Call login function
      const success = await login(values);

      if (success) {
        // Get the updated user after login
        const { user } = useAuthStore.getState();

        if (user) {
          // If initialRole is provided and doesn't match the user's role, show a message
          if (initialRole && initialRole !== user.role) {
            console.warn(
              `User role (${user.role}) doesn't match expected role (${initialRole})`,
            );
            // We'll still redirect them to their dashboard based on their actual role
          }

          // Redirect based on user role
          switch (user.role) {
            case "candidate":
              navigate(URLS.candidate.dashboard);
              break;
            case "employer":
              navigate(URLS.employer.dashboard);
              break;
            case "admin":
              navigate(URLS.admin.dashboard);
              break;
            default:
              navigate("/");
          }
        } else {
          // If user is not available yet, redirect to home page
          navigate("/");
        }
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form
      noValidate
      onSubmit={form.onSubmit(handleSubmit)}
      className="relative flex flex-col gap-4"
    >
      <LoadingOverlay visible={isSubmitting} overlayProps={{ blur: 2 }} />
      <TextInput
        label="Email"
        placeholder="<EMAIL>"
        required
        leftSection={<FaEnvelope />}
        {...form.getInputProps("email")}
      />

      <PasswordInput
        label="Password"
        placeholder="Your password"
        required
        leftSection={<FaLock />}
        {...form.getInputProps("password")}
      />

      <Group justify="space-between" mt="md">
        <Link
          to="/auth/forgot-password"
          className="text-sm text-blue-600 hover:underline"
        >
          Forgot password?
        </Link>
      </Group>

      <Button type="submit" fullWidth mt="xl" loading={isSubmitting}>
        Login
      </Button>

      <Divider label="Don't have an account?" labelPosition="center" my="lg" />

      <Button component={Link} to="/auth/register" variant="outline" fullWidth>
        Create Account
      </Button>
    </form>
  );
}
