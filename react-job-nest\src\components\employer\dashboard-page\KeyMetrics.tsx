import useDashboard from "@/hooks/employer/use-dashboard";
import { Card, Grid, Group, Progress, Text } from "@mantine/core";
import {
  FaBriefcase,
  FaCheckCircle,
  FaClock,
  FaFileAlt,
  FaHandshake,
  FaHourglassHalf,
  FaUsers,
} from "react-icons/fa";

export default function KeyMetrics() {
  // Get dashboard data from hook
  const { stats } = useDashboard();
  return (
    <div className="mb-8">
      <Grid gutter="md">
        {/* First Row - Main Metrics */}
        <Grid.Col span={{ base: 12, md: 6, lg: 3 }}>
          <Card withBorder radius="md" className="h-full shadow-sm">
            <div className="flex items-center justify-between">
              <div>
                <Text c="dimmed" size="sm" className="mb-1">
                  Total Jobs Posted
                </Text>
                <Text fw={700} size="xl" className="mb-1">
                  {stats.totalJobsCount}
                </Text>
                <Group gap="xs" className="mt-1">
                  <Text size="xs" className="text-green-600">
                    +12%
                  </Text>
                  <Text size="xs" c="dimmed">
                    from last month
                  </Text>
                </Group>
              </div>
              <div className="rounded-full bg-blue-100 p-3">
                <FaBriefcase className="text-blue-500" size={24} />
              </div>
            </div>
          </Card>
        </Grid.Col>

        <Grid.Col span={{ base: 12, md: 6, lg: 3 }}>
          <Card withBorder radius="md" className="h-full shadow-sm">
            <div className="flex items-center justify-between">
              <div>
                <Text c="dimmed" size="sm" className="mb-1">
                  Total Candidates
                </Text>
                <Text fw={700} size="xl" className="mb-1">
                  {stats.totalApplications}
                </Text>
                <Group gap="xs" className="mt-1">
                  <Text size="xs" className="text-red-600">
                    -5%
                  </Text>
                  <Text size="xs" c="dimmed">
                    from last month
                  </Text>
                </Group>
              </div>
              <div className="rounded-full bg-green-100 p-3">
                <FaUsers className="text-green-500" size={24} />
              </div>
            </div>
          </Card>
        </Grid.Col>

        <Grid.Col span={{ base: 12, md: 6, lg: 3 }}>
          <Card withBorder radius="md" className="h-full shadow-sm">
            <div className="flex items-center justify-between">
              <div>
                <Text c="dimmed" size="sm" className="mb-1">
                  Active Jobs
                </Text>
                <Text fw={700} size="xl" className="mb-1">
                  {stats.activeJobsCount}
                </Text>
                <Progress
                  value={
                    stats.totalJobsCount > 0
                      ? (stats.activeJobsCount / stats.totalJobsCount) * 100
                      : 0
                  }
                  color="blue"
                  size="sm"
                  radius="xl"
                  className="mt-2 w-24"
                />
              </div>
              <div className="rounded-full bg-blue-100 p-3">
                <FaFileAlt className="text-blue-500" size={24} />
              </div>
            </div>
          </Card>
        </Grid.Col>

        <Grid.Col span={{ base: 12, md: 6, lg: 3 }}>
          <Card withBorder radius="md" className="h-full shadow-sm">
            <div className="flex items-center justify-between">
              <div>
                <Text c="dimmed" size="sm" className="mb-1">
                  Closed Jobs
                </Text>
                <Text fw={700} size="xl" className="mb-1">
                  {stats.totalJobsCount - stats.activeJobsCount}
                </Text>
                <Progress
                  value={
                    stats.totalJobsCount > 0
                      ? ((stats.totalJobsCount - stats.activeJobsCount) /
                          stats.totalJobsCount) *
                        100
                      : 0
                  }
                  color="gray"
                  size="sm"
                  radius="xl"
                  className="mt-2 w-24"
                />
              </div>
              <div className="rounded-full bg-gray-100 p-3">
                <FaCheckCircle className="text-gray-500" size={24} />
              </div>
            </div>
          </Card>
        </Grid.Col>

        {/* Second Row - Additional Metrics */}
        <Grid.Col span={{ base: 12, md: 4 }}>
          <Card withBorder radius="md" className="h-full shadow-sm">
            <div className="flex items-center justify-between">
              <div>
                <Text c="dimmed" size="sm" className="mb-1">
                  Interviews Scheduled
                </Text>
                <Text fw={700} size="xl" className="mb-1">
                  {stats.interviewsScheduled}
                </Text>
                <Text size="xs" c="dimmed" className="mt-1">
                  Next interview in 2 days
                </Text>
              </div>
              <div className="rounded-full bg-purple-100 p-3">
                <FaHandshake className="text-purple-500" size={24} />
              </div>
            </div>
          </Card>
        </Grid.Col>

        <Grid.Col span={{ base: 12, md: 4 }}>
          <Card withBorder radius="md" className="h-full shadow-sm">
            <div className="flex items-center justify-between">
              <div>
                <Text c="dimmed" size="sm" className="mb-1">
                  Pending Reviews
                </Text>
                <Text fw={700} size="xl" className="mb-1">
                  {stats.newApplications}
                </Text>
                <Text size="xs" c="dimmed" className="mt-1">
                  Applications awaiting your review
                </Text>
              </div>
              <div className="rounded-full bg-yellow-100 p-3">
                <FaHourglassHalf className="text-yellow-500" size={24} />
              </div>
            </div>
          </Card>
        </Grid.Col>

        <Grid.Col span={{ base: 12, md: 4 }}>
          <Card withBorder radius="md" className="h-full shadow-sm">
            <div className="flex items-center justify-between">
              <div>
                <Text c="dimmed" size="sm" className="mb-1">
                  Average Time to Hire
                </Text>
                <Text fw={700} size="xl" className="mb-1">
                  14 days
                </Text>
                <Text size="xs" c="dimmed" className="mt-1">
                  -2 days compared to last quarter
                </Text>
              </div>
              <div className="rounded-full bg-cyan-100 p-3">
                <FaClock className="text-cyan-500" size={24} />
              </div>
            </div>
          </Card>
        </Grid.Col>
      </Grid>
    </div>
  );
}
