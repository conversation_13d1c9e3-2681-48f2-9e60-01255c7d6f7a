declare module 'mammoth' {
  interface MammothOptions {
    path?: string;
    buffer?: Buffer;
    arrayBuffer?: ArrayBuffer;
    styleMap?: string | string[];
    includeDefaultStyleMap?: boolean;
    includeEmbeddedStyleMap?: boolean;
    convertImage?: (image: any) => Promise<any>;
    ignoreEmptyParagraphs?: boolean;
    idPrefix?: string;
    transformDocument?: (document: any) => any;
  }

  interface MammothResult {
    value: string;
    messages: Array<{
      type: string;
      message: string;
      [key: string]: any;
    }>;
  }

  function extractRawText(options: MammothOptions): Promise<MammothResult>;
  function convertToHtml(options: MammothOptions): Promise<MammothResult>;
  function convertToMarkdown(options: MammothOptions): Promise<MammothResult>;
  function embedStyleMap(docxBuffer: Buffer, styleMap: string): Promise<Buffer>;

  export = {
    extractRawText,
    convertToHtml,
    convertToMarkdown,
    embedStyleMap
  };
}
