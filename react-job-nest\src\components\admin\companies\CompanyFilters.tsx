"use client";

import SectionHeading from "@/design-system/components/typography/SectionHeading";
import {
  Badge,
  Button,
  Checkbox,
  Chip,
  Divider,
  Group,
  MultiSelect,
  RangeSlider,
  Select,
  Stack,
  Text,
  TextInput,
} from "@mantine/core";
import { DatePickerInput } from "@mantine/dates";
import { type UseFormReturnType } from "@mantine/form";
import {
  FaBuilding,
  FaCalendarAlt,
  FaFilter,
  FaMapMarkerAlt,
  FaSearch,
  FaSort,
  FaTrash,
} from "react-icons/fa";

interface CompanyFiltersProps {
  form: UseFormReturnType<any>;
  onSubmit: () => void;
  onReset: () => void;
}

export default function CompanyFilters({
  form,
  onSubmit,
  onReset,
}: CompanyFiltersProps) {
  return (
    <>
      <SectionHeading variant="admin" className="mb-4">
        <Group gap="xs">
          <FaFilter size={16} />
          <span>Filters</span>
        </Group>
      </SectionHeading>

      <form onSubmit={form.onSubmit(onSubmit)}>
        <Stack gap="md">
          <TextInput
            label="Search Companies"
            placeholder="Company name or keyword"
            leftSection={<FaSearch size={14} />}
            {...form.getInputProps("search")}
          />

          <Divider label="Company Status" labelPosition="center" />

          <Select
            label="Status"
            placeholder="Select company status"
            data={[
              { label: "All Companies", value: "all" },
              { label: "Active Companies", value: "active" },
              { label: "Pending Approval", value: "pending" },
              { label: "Blocked Companies", value: "blocked" },
            ]}
            clearable={false}
            searchable
            value={form.values.status}
            onChange={(value) => form.setFieldValue("status", value || "all")}
          />

          <Divider label="Industry" labelPosition="center" />

          <MultiSelect
            label="Company Industries"
            placeholder="Select industries"
            data={[
              { value: "technology", label: "Technology" },
              { value: "healthcare", label: "Healthcare" },
              { value: "finance", label: "Finance" },
              { value: "education", label: "Education" },
              { value: "retail", label: "Retail" },
              { value: "manufacturing", label: "Manufacturing" },
              { value: "design", label: "Design" },
              { value: "marketing", label: "Marketing" },
              { value: "consulting", label: "Consulting" },
            ]}
            searchable
            clearable
            leftSection={<FaBuilding size={14} />}
          />

          <Divider label="Company Size" labelPosition="center" />

          <div>
            <Text size="sm" fw={500} mb="xs">
              Number of Employees
            </Text>
            <Chip.Group multiple>
              <Group gap="xs" mb="xs">
                <Chip value="1-10" variant="light" color="blue">
                  1-10
                </Chip>
                <Chip value="11-50" variant="light" color="blue">
                  11-50
                </Chip>
                <Chip value="51-200" variant="light" color="blue">
                  51-200
                </Chip>
              </Group>
              <Group gap="xs">
                <Chip value="201-500" variant="light" color="blue">
                  201-500
                </Chip>
                <Chip value="501+" variant="light" color="blue">
                  501+
                </Chip>
              </Group>
            </Chip.Group>
          </div>

          <Divider label="Location" labelPosition="center" />

          <TextInput
            label="Company Location"
            placeholder="City, state, or country"
            leftSection={<FaMapMarkerAlt size={14} />}
          />

          <Divider label="Verification" labelPosition="center" />

          <div>
            <Text size="sm" fw={500} mb="xs">
              Verification Status
            </Text>
            <Group gap="xs">
              <Checkbox
                label={
                  <Group gap="xs">
                    <span>Verified Companies</span>
                    <Badge size="xs" variant="light" color="green">
                      30
                    </Badge>
                  </Group>
                }
              />
            </Group>
            <Group gap="xs" mt="xs">
              <Checkbox
                label={
                  <Group gap="xs">
                    <span>Unverified Companies</span>
                    <Badge size="xs" variant="light" color="gray">
                      15
                    </Badge>
                  </Group>
                }
              />
            </Group>
          </div>

          <Divider label="Job Count" labelPosition="center" />

          <div>
            <Group justify="space-between" mb="xs">
              <Text size="sm" fw={500}>
                Number of Jobs Posted
              </Text>
              <Text size="sm" c="dimmed">
                0 - 50+
              </Text>
            </Group>
            <RangeSlider
              min={0}
              max={50}
              step={5}
              minRange={5}
              defaultValue={[0, 50]}
              marks={[
                { value: 0, label: "0" },
                { value: 25, label: "25" },
                { value: 50, label: "50+" },
              ]}
            />
          </div>

          <Divider label="Registration Date" labelPosition="center" />

          <DatePickerInput
            type="range"
            label="Date Range"
            placeholder="Select date range"
            valueFormat="YYYY-MM-DD"
            clearable
            leftSection={<FaCalendarAlt size={14} />}
          />

          <Divider label="Sorting" labelPosition="center" />

          <Select
            label="Sort By"
            placeholder="Select field"
            leftSection={<FaSort size={14} />}
            data={[
              { value: "name", label: "Company Name" },
              { value: "industry", label: "Industry" },
              { value: "createdAt", label: "Registration Date" },
              { value: "status", label: "Status" },
              { value: "jobCount", label: "Number of Jobs" },
            ]}
            {...form.getInputProps("sortBy")}
          />

          <Select
            label="Sort Order"
            placeholder="Select sort direction"
            data={[
              { label: "Ascending (A-Z, Oldest first)", value: "asc" },
              { label: "Descending (Z-A, Newest first)", value: "desc" },
            ]}
            clearable={false}
            value={form.values.sortOrder}
            onChange={(value) =>
              form.setFieldValue("sortOrder", value || "asc")
            }
          />

          <Divider />

          <Group justify="space-between">
            <Button
              variant="subtle"
              color="red"
              onClick={onReset}
              leftSection={<FaTrash size={14} />}
            >
              Clear All
            </Button>
            <Button
              type="submit"
              leftSection={<FaFilter size={14} />}
              className="bg-primary-color hover:bg-primary-color/90"
            >
              Apply Filters
            </Button>
          </Group>
        </Stack>
      </form>
    </>
  );
}
