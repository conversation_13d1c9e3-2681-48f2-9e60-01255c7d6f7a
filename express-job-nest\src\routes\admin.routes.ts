import express from "express";
import {
  getDashboardStats,
  getAllUsers,
  getUserById,
  updateUser,
  deleteUser,
} from "../controllers/admin.controller";
import { protect, restrictTo } from "../middleware/auth.middleware";
import { body } from "express-validator";
import { validationMiddleware } from "../validators/validation.middleware";

// Define validators
const updateUserValidator = [
  body("name")
    .optional()
    .isString()
    .withMessage("Name must be a string")
    .isLength({ min: 2 })
    .withMessage("Name must be at least 2 characters long"),
  body("email")
    .optional()
    .isEmail()
    .withMessage("Please provide a valid email"),
  body("role")
    .optional()
    .isIn(["candidate", "employer", "admin"])
    .withMessage("Role must be either: candidate, employer, or admin"),
];

const router = express.Router();

// All routes are protected and restricted to admins
router.use(protect);
router.use(restrictTo("admin"));

// Dashboard routes
router.get("/dashboard", getDashboardStats);

// User management routes
router.get("/users", getAllUsers);
router.get("/users/:id", getUserById);
router.patch(
  "/users/:id",
  updateUserValidator,
  validationMiddleware,
  updateUser
);
router.delete("/users/:id", deleteUser);

export default router;
