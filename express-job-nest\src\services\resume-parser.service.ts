import path from "path";
import fs from "fs";
import pdf from "pdf-parse";
import mammoth from "mammoth";
import { IEducation, IExperience } from "../models/candidate-profile.model";

// Interface for parsed resume data
export interface ParsedResumeData {
  fullName?: string;
  email?: string;
  phone?: string;
  summary?: string;
  skills: string[];
  experience: Partial<IExperience>[];
  education: Partial<IEducation>[];
  languages: string[];
  certifications: string[];
  links: { title: string; url: string }[];
}

class ResumeParserService {
  /**
   * Parse a resume file
   * @param filePath Path to the resume file
   * @returns Parsed resume data
   */
  public async parseResume(filePath: string): Promise<ParsedResumeData> {
    try {
      const fullPath = path.join(process.cwd(), filePath);
      const fileExt = path.extname(fullPath).toLowerCase();

      // Check if file exists
      if (!fs.existsSync(fullPath)) {
        throw new Error("File not found");
      }

      let text = "";

      // Extract text based on file type
      if (fileExt === ".pdf") {
        text = await this.extractTextFromPdf(fullPath);
      } else if (fileExt === ".docx") {
        text = await this.extractTextFromDocx(fullPath);
      } else if (fileExt === ".doc") {
        // For .doc files, we can't reliably extract text without external tools
        throw new Error(
          "DOC format is not supported. Please upload a PDF or DOCX file."
        );
      } else {
        throw new Error("Unsupported file format");
      }

      // Parse the extracted text
      return this.parseText(text);
    } catch (error) {
      console.error("Error parsing resume:", error);
      // Return empty data structure on error
      return {
        skills: [],
        experience: [],
        education: [],
        languages: [],
        certifications: [],
        links: [],
      };
    }
  }

  /**
   * Extract text from a PDF file
   * @param filePath Path to the PDF file
   * @returns Extracted text
   */
  private async extractTextFromPdf(filePath: string): Promise<string> {
    const dataBuffer = fs.readFileSync(filePath);
    const data = await pdf(dataBuffer);
    return data.text;
  }

  /**
   * Extract text from a DOCX file
   * @param filePath Path to the DOCX file
   * @returns Extracted text
   */
  private async extractTextFromDocx(filePath: string): Promise<string> {
    const result = await mammoth.extractRawText({ path: filePath });
    return result.value;
  }

  /**
   * Parse extracted text into structured resume data
   * @param text Extracted text from resume
   * @returns Parsed resume data
   */
  private parseText(text: string): ParsedResumeData {
    // Initialize result object
    const result: ParsedResumeData = {
      skills: [],
      experience: [],
      education: [],
      languages: [],
      certifications: [],
      links: [],
    };

    // Extract email
    const emailRegex = /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g;
    const emails = text.match(emailRegex);
    if (emails && emails.length > 0) {
      result.email = emails[0];
    }

    // Extract phone number (various formats)
    const phoneRegex = /(\+\d{1,3}[ -]?)?\(?\d{3}\)?[ -]?\d{3}[ -]?\d{4}/g;
    const phones = text.match(phoneRegex);
    if (phones && phones.length > 0) {
      result.phone = phones[0];
    }

    // Extract skills (common programming languages, frameworks, tools)
    const skillKeywords = [
      "JavaScript",
      "TypeScript",
      "Python",
      "Java",
      "C#",
      "C++",
      "Ruby",
      "PHP",
      "Swift",
      "React",
      "Angular",
      "Vue",
      "Node.js",
      "Express",
      "Django",
      "Flask",
      "Spring",
      "AWS",
      "Azure",
      "GCP",
      "Docker",
      "Kubernetes",
      "Git",
      "SQL",
      "NoSQL",
      "MongoDB",
      "PostgreSQL",
      "MySQL",
      "Oracle",
      "HTML",
      "CSS",
      "SASS",
      "LESS",
      "Bootstrap",
      "Tailwind",
      "Material UI",
      "Redux",
      "GraphQL",
      "REST",
      "API",
      "Microservices",
      "CI/CD",
      "Jenkins",
      "Travis",
      "Agile",
      "Scrum",
      "Kanban",
      "Jira",
      "Confluence",
      "TDD",
      "BDD",
      "Unit Testing",
      "Integration Testing",
      "E2E Testing",
      "Jest",
      "Mocha",
      "Chai",
      "Selenium",
      "Cypress",
      "Playwright",
      "Webpack",
      "Babel",
      "ESLint",
      "Prettier",
      "npm",
      "yarn",
      "pnpm",
      "Linux",
      "Windows",
      "MacOS",
      "Bash",
      "PowerShell",
      "Networking",
      "Security",
      "Authentication",
      "Authorization",
      "OAuth",
      "JWT",
      "SAML",
      "LDAP",
      "Active Directory",
      "Firewall",
      "VPN",
      "SSL/TLS",
      "Encryption",
      "Hashing",
      "Blockchain",
      "Machine Learning",
      "AI",
      "Data Science",
      "Big Data",
      "Hadoop",
      "Spark",
      "Kafka",
      "ELK",
      "Grafana",
      "Prometheus",
      "Datadog",
      "New Relic",
      "Sentry",
      "Splunk",
      "Sumo Logic",
      "Papertrail",
      "Logstash",
      "Kibana",
      "ElasticSearch",
      "Redis",
      "Memcached",
      "RabbitMQ",
      "ActiveMQ",
      "ZeroMQ",
      "MQTT",
      "WebSockets",
      "gRPC",
      "Thrift",
      "Avro",
      "Protobuf",
      "JSON",
      "XML",
      "YAML",
      "TOML",
      "INI",
      "CSV",
      "Excel",
      "Word",
      "PowerPoint",
      "Outlook",
      "Teams",
      "Slack",
      "Discord",
      "Zoom",
      "Google Meet",
      "Skype",
      "WebEx",
      "GoToMeeting",
      "BlueJeans",
      "Trello",
      "Asana",
      "Monday",
      "Notion",
      "Evernote",
      "OneNote",
      "Google Docs",
      "Google Sheets",
      "Google Slides",
      "Google Drive",
      "Dropbox",
      "OneDrive",
      "iCloud",
      "Box",
      "S3",
      "Glacier",
      "EBS",
      "EFS",
      "EC2",
      "RDS",
      "DynamoDB",
      "Lambda",
      "API Gateway",
      "CloudFront",
      "Route53",
      "VPC",
      "IAM",
      "CloudWatch",
      "CloudTrail",
      "CloudFormation",
      "Terraform",
      "Ansible",
      "Puppet",
      "Chef",
      "SaltStack",
      "Vagrant",
      "VirtualBox",
      "VMware",
      "Hyper-V",
      "KVM",
      "Xen",
      "QEMU",
      "ESXi",
      "vSphere",
      "vCenter",
      "NSX",
      "vSAN",
      "vRealize",
      "OpenStack",
      "OpenShift",
      "Rancher",
      "Helm",
      "Istio",
      "Linkerd",
      "Envoy",
      "Consul",
      "Vault",
      "Nomad",
      "Packer",
      "Boundary",
      "Waypoint",
      "Traefik",
      "NGINX",
      "Apache",
      "IIS",
      "Tomcat",
      "JBoss",
      "WebLogic",
      "WebSphere",
      "Jetty",
      "Undertow",
      "Caddy",
      "HAProxy",
      "F5",
      "Citrix",
      "Akamai",
      "Cloudflare",
      "Fastly",
      "Imperva",
      "Barracuda",
      "Fortinet",
      "Palo Alto",
      "Cisco",
      "Juniper",
      "Arista",
      "Cumulus",
      "Aruba",
      "Meraki",
      "Ubiquiti",
      "MikroTik",
      "pfSense",
      "OPNsense",
      "Wireshark",
      "tcpdump",
      "nmap",
      "Metasploit",
      "Burp Suite",
      "OWASP ZAP",
      "Nessus",
      "Qualys",
      "Rapid7",
      "Tenable",
      "CrowdStrike",
      "Carbon Black",
      "Cylance",
      "Symantec",
      "McAfee",
      "Trend Micro",
      "Sophos",
      "Kaspersky",
      "Bitdefender",
      "ESET",
      "Avast",
      "AVG",
      "Norton",
      "Webroot",
      "Malwarebytes",
      "Windows Defender",
      "SELinux",
      "AppArmor",
      "iptables",
      "ufw",
      "firewalld",
      "fail2ban",
      "ClamAV",
      "OpenVAS",
      "Snort",
      "Suricata",
      "Zeek",
      "Wazuh",
      "OSSEC",
      "Tripwire",
      "AIDE",
      "Lynis",
      "OpenSCAP",
      "Compliance",
      "GDPR",
      "HIPAA",
      "PCI DSS",
      "SOX",
      "ISO 27001",
      "NIST",
      "CIS",
      "COBIT",
      "ITIL",
      "TOGAF",
      "SABSA",
      "FAIR",
      "COSO",
      "CMMI",
      "Six Sigma",
      "Lean",
      "DevOps",
      "DevSecOps",
      "SRE",
      "Platform Engineering",
      "Cloud Native",
      "Serverless",
      "Containerization",
      "Virtualization",
      "Orchestration",
      "Automation",
      "Infrastructure as Code",
      "GitOps",
      "ChatOps",
      "NoOps",
      "AIOps",
      "MLOps",
      "DataOps",
      "FinOps",
      "SecOps",
      "NetOps",
    ];

    // Extract skills from text
    result.skills = skillKeywords.filter((skill) =>
      new RegExp(`\\b${skill}\\b`, "i").test(text)
    );

    // Extract education (simple pattern matching)
    const educationKeywords = [
      "Bachelor",
      "Master",
      "PhD",
      "Doctorate",
      "BSc",
      "MSc",
      "MBA",
      "Degree",
      "University",
      "College",
      "School",
    ];
    const educationRegex = new RegExp(
      `(${educationKeywords.join("|")})([^.]*?)\\s*(\\d{4})`,
      "gi"
    );
    let educationMatch;

    while ((educationMatch = educationRegex.exec(text)) !== null) {
      const educationText = educationMatch[0];
      const year = educationMatch[3];

      // Try to extract institution and degree
      const parts = educationText.split(/\s+at\s+|\s+from\s+/i);
      let degree = parts[0].trim();
      let institution = parts.length > 1 ? parts[1].trim() : "";

      // Create a date object for the end year
      const endDate = new Date();
      endDate.setFullYear(parseInt(year));

      // Create a date object for the start year (estimated 4 years before end date)
      const startDate = new Date(endDate);
      startDate.setFullYear(endDate.getFullYear() - 4);

      result.education.push({
        institution,
        degree,
        fieldOfStudy: "",
        startDate: startDate,
        endDate: endDate,
      });
    }

    // Extract experience (simple pattern matching)
    const experienceRegex =
      /([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)\s+(?:at|@)\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)\s+(?:from\s+)?(\d{4})(?:\s*-\s*(?:present|current|now|\d{4}))?/gi;
    let experienceMatch;

    while ((experienceMatch = experienceRegex.exec(text)) !== null) {
      const position = experienceMatch[1].trim();
      const company = experienceMatch[2].trim();
      const startYear = experienceMatch[3];
      const endYear = experienceMatch[4] || "Present";

      // Create a date object for the start year
      const startDate = new Date();
      startDate.setFullYear(parseInt(startYear));

      // Create a date object for the end year
      let endDate: Date;
      if (endYear === "Present") {
        endDate = new Date(); // Current date for "Present"
      } else {
        endDate = new Date();
        endDate.setFullYear(parseInt(endYear));
      }

      result.experience.push({
        position,
        company,
        location: "",
        startDate: startDate,
        endDate: endDate,
        description: "",
      });
    }

    return result;
  }
}

// Create a singleton instance
const resumeParserService = new ResumeParserService();

export default resumeParserService;
