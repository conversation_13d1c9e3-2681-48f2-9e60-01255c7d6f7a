import { useThemeClasses } from "@/design-system/utils/theme-utils";
import { useJobsViewModeStore } from "@/stores/candidate-store";
import { SegmentedControl, useMantineColorScheme } from "@mantine/core";
import { FaList, FaTh } from "react-icons/fa";

export default function JobsViewControls() {
  const { viewMode, setViewMode } = useJobsViewModeStore();
  const { colorScheme } = useMantineColorScheme();
  const isDark = colorScheme === "dark";

  return (
    <SegmentedControl
      className="hidden sm:flex"
      value={viewMode}
      onChange={(value) => setViewMode(value as "grid" | "list")}
      data={[
        {
          value: "grid",
          label: (
            <div className="flex items-center gap-2">
              <FaTh
                size={14}
                className={useThemeClasses("text-gray-700", "text-gray-300")}
              />
              <span>Grid</span>
            </div>
          ),
        },
        {
          value: "list",
          label: (
            <div className="flex items-center gap-2">
              <FaList
                size={14}
                className={useThemeClasses("text-gray-700", "text-gray-300")}
              />
              <span>List</span>
            </div>
          ),
        },
      ]}
      styles={{
        root: {
          backgroundColor: isDark
            ? "var(--mantine-color-dark-6) !important"
            : undefined,
          borderColor: isDark
            ? "var(--mantine-color-dark-4) !important"
            : undefined,
        },
        indicator: {
          backgroundColor: isDark
            ? "var(--mantine-color-dark-4) !important"
            : undefined,
        },
        label: {
          color: isDark ? "var(--mantine-color-white) !important" : undefined,
          "&[data-active]": {
            color: isDark ? "var(--mantine-color-white) !important" : undefined,
          },
        },
      }}
    />
  );
}
