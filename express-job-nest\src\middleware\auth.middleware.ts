import { Request, Response, NextFunction } from "express";
import jwt from "jsonwebtoken";
import { promisify } from "util";
import User, { IUser } from "../models/user.model";
import { AppError } from "../utils/app-error";
import { catchAsync } from "../utils/catch-async";

// Extend Express Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user: IUser;
    }
  }
}

// Protect routes - only authenticated users can access
export const protect = catchAsync(
  async (req: Request, _res: Response, next: NextFunction) => {
    try {
      // 1) Get token and check if it exists
      let token;
      if (
        req.headers.authorization &&
        req.headers.authorization.startsWith("Bearer")
      ) {
        token = req.headers.authorization.split(" ")[1];
      } else if (req.cookies && req.cookies.jwt) {
        token = req.cookies.jwt;
      }

      if (!token || token === "loggedout") {
        return next(
          new AppError(
            "You are not logged in. Please log in to get access",
            401
          )
        );
      }

      // 2) Verify token
      try {
        const decoded = (await (promisify(jwt.verify) as any)(
          token,
          process.env.JWT_SECRET || "your-secret-key"
        )) as { id: string; iat: number; exp: number };

        // 3) Check if user still exists
        const currentUser = await User.findById(decoded.id);
        if (!currentUser) {
          return next(
            new AppError(
              "The user belonging to this token no longer exists",
              401
            )
          );
        }

        // GRANT ACCESS TO PROTECTED ROUTE
        req.user = currentUser;
        next();
      } catch (error) {
        // Handle JWT verification errors
        console.error("JWT verification error:", error);
        return next(new AppError("Invalid token. Please log in again.", 401));
      }
    } catch (error) {
      console.error("Auth middleware error:", error);
      return next(new AppError("Authentication error. Please try again.", 500));
    }
  }
);

// Restrict to certain roles
export const restrictTo = (...roles: string[]) => {
  return (req: Request, _res: Response, next: NextFunction): void => {
    // roles is an array ['admin', 'employer']
    if (!roles.includes(req.user.role)) {
      next(
        new AppError("You do not have permission to perform this action", 403)
      );
      return;
    }

    next();
  };
};
