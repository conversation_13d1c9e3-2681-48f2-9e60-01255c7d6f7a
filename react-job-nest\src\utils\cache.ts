interface Cache {
  /***
   * Set a value in the cache
   */
  set: (key: string, value: any) => void;
  /***
   * Get a value from the cache
   */
  get(key: string): any;
  /***
   * clear cache
   */
  clear(): void;
}

export const cache: Cache = {
  /***
   * Set a value in the cache
   */
  set(key: string, value: any) {
    localStorage.setItem(key, JSON.stringify(value));
  },
  /***
   * Get a value from the cache
   */
  get(key: string) {
    const item = localStorage.getItem(key);
    if (!item) return null;
    try {
      return JSON.parse(item);
    } catch {
      return null;
    }
  },
  /**
   * clear cache
   */
  clear() {
    localStorage.clear();
  },
};
