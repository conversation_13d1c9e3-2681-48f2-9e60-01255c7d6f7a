import { <PERSON><PERSON><PERSON><PERSON>, PageHeading } from "@/design-system/components";
import { URLS } from "@/utils/urls";
import {
  Anchor,
  Box,
  Button,
  Card,
  Divider,
  Grid,
  Group,
  List,
  Stepper,
  Tabs,
  Text,
  TextInput,
  ThemeIcon,
  Title,
} from "@mantine/core";
import { useForm } from "@mantine/form";
import { notifications } from "@mantine/notifications";
import { useState } from "react";
import {
  FaCheck,
  FaCreditCard,
  FaInfoCircle,
  FaMobile,
  FaMoneyBillWave,
  FaShieldAlt,
  FaUniversity,
  FaUser,
} from "react-icons/fa";
import { Link, useNavigate, useSearchParams } from "react-router";

import vodafone_cash from "@/assets/icons/vodafone-cash.svg";

// Plan data - in a real app, this would come from an API or context
const plans = {
  basic: {
    name: "Basic",
    price: 29,
    annualPrice: 295,
    features: [
      "2 active job postings",
      "30-day job visibility",
      "50 CV views per month",
      "Basic candidate filtering",
      "Email notifications",
    ],
  },
  professional: {
    name: "Professional",
    price: 59,
    annualPrice: 565,
    features: [
      "5 active job postings",
      "45-day job visibility",
      "200 CV views per month",
      "Advanced candidate filtering",
      "Priority placement",
    ],
  },
  enterprise: {
    name: "Enterprise",
    price: 149,
    annualPrice: 1341,
    features: [
      "Unlimited active job postings",
      "60-day job visibility",
      "Unlimited CV views",
      "Advanced analytics and reporting",
      "Dedicated account manager",
    ],
  },
};

export default function PaymentPage() {
  const [searchParams] = useSearchParams();
  const planId = searchParams.get("plan") || "professional";
  const billingPeriod = searchParams.get("billing") || "month";
  const plan = plans[planId as keyof typeof plans];
  const navigate = useNavigate();

  // Payment method state
  const [paymentMethod, setPaymentMethod] = useState<string>("vodafone");

  // Format price with currency
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      maximumFractionDigits: 0,
    }).format(price);
  };

  // Get price based on billing period
  const getPrice = () => {
    if (billingPeriod === "month") {
      return plan.price;
    } else {
      return plan.annualPrice;
    }
  };

  // Calculate tax (example: 10%)
  const calculateTax = () => {
    return getPrice() * 0.1;
  };

  // Calculate total
  const calculateTotal = () => {
    return getPrice() + calculateTax();
  };

  // Vodafone Cash form validation
  const vodafoneForm = useForm({
    initialValues: {
      phoneNumber: "",
      fullName: "",
    },
    validate: {
      phoneNumber: (value) =>
        /^01[0125][0-9]{8}$/.test(value)
          ? null
          : "Invalid phone number (must be an Egyptian number)",
      fullName: (value) => (value.length < 3 ? "Full name is too short" : null),
    },
  });

  // Bank transfer form validation
  const bankTransferForm = useForm({
    initialValues: {
      accountName: "",
    },
    validate: {
      accountName: (value) =>
        value.length < 3 ? "Account name is too short" : null,
    },
  });

  // Handle form submission
  const handleSubmit = (values: any) => {
    // In a real app, you would send this data to your payment processor
    console.log(paymentMethod, values);

    // Show success notification
    notifications.show({
      title: "Payment information received",
      message: "Processing your payment...",
      color: "green",
      loading: true,
      autoClose: 2000,
    });

    // Simulate payment processing
    setTimeout(() => {
      navigate(
        `/employer/checkout/confirmation?plan=${planId}&billing=${billingPeriod}&method=${paymentMethod}`,
      );
    }, 2000);
  };

  return (
    <PageContainer
      breadcrumbItems={[
        { title: "Home", href: "/" },
        { title: "Pricing", href: URLS.employer.pricing },
        {
          title: "Checkout",
          href: `/employer/checkout?plan=${planId}&billing=${billingPeriod}`,
        },
        { title: "Payment" },
      ]}
      variant="employer"
      className="pb-12"
    >
      <PageHeading
        title="Payment Details"
        subtitle="Choose your preferred payment method"
        className="mb-8"
        variant="employer"
      />

      {/* Checkout Process Stepper */}
      <Stepper
        active={1}
        className="mb-8"
        color="primary"
        onStepClick={(step) => {
          // Only allow clicking on previous steps
          if (step < 1) {
            // Navigate back to billing information
            navigate(
              `/employer/checkout?plan=${planId}&billing=${billingPeriod}`,
            );
          }
        }}
        allowNextStepsSelect={false}
      >
        <Stepper.Step
          label="Billing Information"
          description="Enter your details"
          icon={<FaUser size={18} />}
          state="stepCompleted"
        />
        <Stepper.Step
          label="Payment Method"
          description="Choose how to pay"
          icon={<FaCreditCard size={18} />}
        />
        <Stepper.Step
          label="Confirmation"
          description="Review and confirm"
          icon={<FaCheck size={18} />}
        />
      </Stepper>

      <Grid gutter="xl">
        {/* Left Column - Payment Forms */}
        <Grid.Col span={{ base: 12, md: 8 }}>
          <Card withBorder radius="md" className="mb-4">
            <Title order={3} className="mb-4">
              Payment Method
            </Title>

            <Tabs
              value={paymentMethod}
              onChange={(value) => setPaymentMethod(value as string)}
              className="mb-4"
            >
              <Tabs.List>
                <Tabs.Tab value="vodafone" leftSection={<FaMobile size={16} />}>
                  Vodafone Cash
                </Tabs.Tab>
                <Tabs.Tab
                  value="bankTransfer"
                  leftSection={<FaUniversity size={16} />}
                >
                  Bank Transfer
                </Tabs.Tab>
              </Tabs.List>

              <Tabs.Panel value="vodafone" pt="md">
                <Box className="mb-4">
                  <Group
                    align="center"
                    className="mb-6 bg-red-50 p-4 rounded-md"
                  >
                    <div className="flex-shrink-0">
                      <div className="flex w-[60px] h-[60px] items-center justify-center bg-white p-2 rounded-full shadow-sm">
                        <img
                          src={vodafone_cash}
                          alt="Vodafone Cash"
                          className="w-auto"
                        />
                      </div>
                    </div>
                    <div>
                      <Text fw={500} size="lg">
                        Vodafone Cash
                      </Text>
                      <Text size="sm" c="dimmed">
                        Fast and secure mobile payment method
                      </Text>
                    </div>
                  </Group>

                  <form
                    noValidate
                    onSubmit={vodafoneForm.onSubmit(handleSubmit)}
                  >
                    <Grid>
                      <Grid.Col span={12}>
                        <TextInput
                          label="Phone Number"
                          placeholder="01xxxxxxxxx"
                          description="Enter your Vodafone Cash registered number"
                          required
                          leftSection={<FaMobile size={16} />}
                          {...vodafoneForm.getInputProps("phoneNumber")}
                        />
                      </Grid.Col>
                      <Grid.Col span={12}>
                        <TextInput
                          label="Full Name"
                          placeholder="As registered with Vodafone Cash"
                          required
                          {...vodafoneForm.getInputProps("fullName")}
                        />
                      </Grid.Col>
                      <Grid.Col span={12}>
                        <Box className="bg-gray-50 p-4 rounded-md mb-4">
                          <Text fw={500} className="mb-2">
                            How to pay:
                          </Text>
                          <List size="sm" spacing="xs">
                            <List.Item>
                              After clicking &quot;Continue&quot;, you&apos;ll
                              receive payment instructions
                            </List.Item>
                            <List.Item>
                              Dial *9# on your Vodafone line
                            </List.Item>
                            <List.Item>
                              Select &quot;Pay Bill&quot; and enter our merchant
                              code
                            </List.Item>
                            <List.Item>
                              Enter the amount: {formatPrice(calculateTotal())}
                            </List.Item>
                            <List.Item>
                              Confirm payment with your Vodafone Cash PIN
                            </List.Item>
                          </List>
                        </Box>
                      </Grid.Col>
                      <Grid.Col span={12} className="mt-4">
                        <Group justify="flex-end">
                          <Button
                            type="submit"
                            size="md"
                            leftSection={<FaMoneyBillWave size={14} />}
                            color="red"
                          >
                            Continue with Vodafone Cash
                          </Button>
                        </Group>
                      </Grid.Col>
                    </Grid>
                  </form>
                </Box>
              </Tabs.Panel>

              <Tabs.Panel value="bankTransfer" pt="md">
                <Box className="mb-4">
                  <Group
                    align="center"
                    className="mb-6 bg-blue-50 p-4 rounded-md"
                  >
                    <div className="flex-shrink-0">
                      <div className="flex items-center justify-center bg-blue-100 p-3 rounded-full shadow-sm">
                        <FaUniversity size={36} className="text-blue-600" />
                      </div>
                    </div>
                    <div>
                      <Text fw={500} size="lg">
                        Bank Transfer
                      </Text>
                      <Text size="sm" c="dimmed">
                        Direct bank transfer to our account
                      </Text>
                    </div>
                  </Group>

                  <form
                    noValidate
                    onSubmit={bankTransferForm.onSubmit(handleSubmit)}
                  >
                    <Grid>
                      <Grid.Col span={12}>
                        <TextInput
                          label="Account Holder Name"
                          placeholder="Your full name"
                          description="Name of the account making the transfer"
                          required
                          {...bankTransferForm.getInputProps("accountName")}
                        />
                      </Grid.Col>
                      <Grid.Col span={12}>
                        <Box className="bg-gray-50 p-4 rounded-md mb-4">
                          <Text fw={500} className="mb-2">
                            Our Bank Details:
                          </Text>
                          <List size="sm" spacing="xs">
                            <List.Item>
                              Bank Name: National Bank of Egypt
                            </List.Item>
                            <List.Item>
                              Account Name: JobNest Recruitment Services
                            </List.Item>
                            <List.Item>
                              Account Number: ****************
                            </List.Item>
                            <List.Item>Branch: Main Branch, Cairo</List.Item>
                            <List.Item>
                              Reference: Please include your Order ID as
                              reference
                            </List.Item>
                          </List>
                        </Box>
                      </Grid.Col>
                      <Grid.Col span={12}>
                        <Text size="sm" c="dimmed" className="mb-4">
                          After making the transfer, please click the button
                          below. Our team will verify your payment within 24
                          hours and activate your subscription.
                        </Text>
                      </Grid.Col>
                      <Grid.Col span={12} className="mt-4">
                        <Group justify="flex-end">
                          <Button
                            type="submit"
                            size="md"
                            leftSection={<FaUniversity size={14} />}
                            color="blue"
                          >
                            Confirm Bank Transfer
                          </Button>
                        </Group>
                      </Grid.Col>
                    </Grid>
                  </form>
                </Box>
              </Tabs.Panel>
            </Tabs>
          </Card>
        </Grid.Col>

        {/* Right Column - Order Summary */}
        <Grid.Col span={{ base: 12, md: 4 }}>
          <Card withBorder radius="md" className="bg-gray-50">
            <Title order={3} className="mb-4">
              Order Summary
            </Title>
            <Box className="mb-4">
              <Group justify="space-between" className="mb-2">
                <Text fw={500}>{plan.name} Plan</Text>
                <Text fw={500}>{formatPrice(getPrice())}</Text>
              </Group>
              <Text size="sm" c="dimmed">
                {billingPeriod === "month" ? "Monthly" : "Annual"} subscription
              </Text>
            </Box>

            <List
              spacing="xs"
              size="sm"
              center
              icon={
                <ThemeIcon color="green" size={20} radius="xl">
                  <FaCheck size={10} />
                </ThemeIcon>
              }
              className="mb-4"
            >
              {plan.features.map((feature, index) => (
                <List.Item key={index}>{feature}</List.Item>
              ))}
            </List>

            <Divider my="md" />

            <Group justify="space-between" className="mb-2">
              <Text>Subtotal</Text>
              <Text>{formatPrice(getPrice())}</Text>
            </Group>
            <Group justify="space-between" className="mb-2">
              <Text>Tax (10%)</Text>
              <Text>{formatPrice(calculateTax())}</Text>
            </Group>
            <Divider my="md" />
            <Group justify="space-between" className="mb-2">
              <Text fw={700}>Total</Text>
              <Text fw={700} size="lg">
                {formatPrice(calculateTotal())}
              </Text>
            </Group>
            <Text size="xs" c="dimmed" className="mt-4">
              {billingPeriod === "month"
                ? "You will be charged monthly until you cancel."
                : "You will be charged annually until you cancel."}
            </Text>

            <Box className="mt-6 bg-primary-50 p-3 rounded-md">
              <Group gap="xs">
                <ThemeIcon
                  color="primary"
                  variant="light"
                  size="md"
                  radius="xl"
                >
                  <FaShieldAlt size={14} />
                </ThemeIcon>
                <Text size="sm" fw={500}>
                  Secure Checkout
                </Text>
              </Group>
              <Text size="xs" c="dimmed" className="mt-2">
                Your payment information is encrypted and secure. We use
                industry-standard security measures to protect your data.
              </Text>
            </Box>
          </Card>

          <Box className="mt-4">
            <Group gap="xs" className="mb-2">
              <FaInfoCircle size={14} className="text-gray-500" />
              <Text size="sm">Need help?</Text>
            </Group>
            <Text size="xs" c="dimmed">
              If you have any questions about your purchase, please{" "}
              <Anchor component={Link} to="/contact-us" size="xs">
                contact our support team
              </Anchor>
              .
            </Text>
          </Box>
        </Grid.Col>
      </Grid>
    </PageContainer>
  );
}
