import Card, { type CardProps } from "@/design-system/components/card/Card";
import { type PropsWithChildren } from "react";

/**
 * @deprecated Use Card from design-system/components with variant="employer" instead
 */
export default function EmployerMainCard({
  className = "",
  children,
  withBorder = true,
  withShadow = false,
  padding = "lg",
  radius = "md",
  ...rest
}: PropsWithChildren<CardProps>) {
  return (
    <Card
      className={className}
      withBorder={withBorder}
      withShadow={withShadow}
      padding={padding}
      radius={radius}
      variant="employer"
      fullHeight
      fullWidth
      {...rest}
    >
      {children}
    </Card>
  );
}
