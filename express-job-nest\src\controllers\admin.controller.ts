import { Request, Response, NextFunction } from "express";
import { catchAsync } from "../utils/catch-async";
import { AppError } from "../utils/app-error";
import User from "../models/user.model";
import Job from "../models/job.model";
import Company from "../models/company.model";
import Application from "../models/application.model";

// Get dashboard statistics
export const getDashboardStats = catchAsync(
  async (_req: Request, res: Response, _next: NextFunction) => {
    // Get counts
    const userCount = await User.countDocuments();
    const jobCount = await Job.countDocuments();
    const companyCount = await Company.countDocuments();
    const applicationCount = await Application.countDocuments();

    // Get user counts by role
    const candidateCount = await User.countDocuments({ role: "candidate" });
    const employerCount = await User.countDocuments({ role: "employer" });
    const adminCount = await User.countDocuments({ role: "admin" });

    // Get active job count
    const activeJobCount = await Job.countDocuments({ isActive: true });

    // Get verified company count
    const verifiedCompanyCount = await Company.countDocuments({
      isVerified: true,
    });

    // Get recent users
    const recentUsers = await User.find()
      .sort({ createdAt: -1 })
      .limit(5)
      .select("name email role createdAt");

    // Get recent jobs
    const recentJobs = await Job.find()
      .sort({ createdAt: -1 })
      .limit(5)
      .populate({
        path: "company",
        select: "name",
      })
      .select("title company jobType location createdAt isActive");

    // Get recent applications
    const recentApplications = await Application.find()
      .sort({ createdAt: -1 })
      .limit(5)
      .populate({
        path: "job",
        select: "title",
      })
      .populate({
        path: "candidate",
        select: "name email",
      })
      .select("job candidate status createdAt");

    // Get user registration trends (last 6 months)
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

    const userTrends = await User.aggregate([
      {
        $match: {
          createdAt: { $gte: sixMonthsAgo },
        },
      },
      {
        $group: {
          _id: {
            year: { $year: "$createdAt" },
            month: { $month: "$createdAt" },
          },
          count: { $sum: 1 },
        },
      },
      {
        $sort: {
          "_id.year": 1,
          "_id.month": 1,
        },
      },
      {
        $project: {
          _id: 0,
          year: "$_id.year",
          month: "$_id.month",
          count: 1,
        },
      },
    ]);

    // Get job posting trends (last 6 months)
    const jobTrends = await Job.aggregate([
      {
        $match: {
          createdAt: { $gte: sixMonthsAgo },
        },
      },
      {
        $group: {
          _id: {
            year: { $year: "$createdAt" },
            month: { $month: "$createdAt" },
          },
          count: { $sum: 1 },
        },
      },
      {
        $sort: {
          "_id.year": 1,
          "_id.month": 1,
        },
      },
      {
        $project: {
          _id: 0,
          year: "$_id.year",
          month: "$_id.month",
          count: 1,
        },
      },
    ]);

    res.status(200).json({
      success: true,
      stats: {
        counts: {
          users: userCount,
          jobs: jobCount,
          companies: companyCount,
          applications: applicationCount,
          candidates: candidateCount,
          employers: employerCount,
          admins: adminCount,
          activeJobs: activeJobCount,
          verifiedCompanies: verifiedCompanyCount,
        },
        recent: {
          users: recentUsers,
          jobs: recentJobs,
          applications: recentApplications,
        },
        trends: {
          users: userTrends,
          jobs: jobTrends,
        },
      },
    });
  }
);

// Get all users with filtering and pagination
export const getAllUsers = catchAsync(
  async (req: Request, res: Response, _next: NextFunction) => {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const skip = (page - 1) * limit;

    // Build query
    const queryObj: any = {};

    // Filter by role
    if (req.query.role && req.query.role !== "all") {
      queryObj.role = req.query.role;
    }

    // Search by name or email
    if (req.query.search) {
      queryObj.$or = [
        { name: { $regex: req.query.search, $options: "i" } },
        { email: { $regex: req.query.search, $options: "i" } },
      ];
    }

    // Execute query with pagination
    const users = await User.find(queryObj)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    // Get total count
    const totalUsers = await User.countDocuments(queryObj);

    res.status(200).json({
      success: true,
      count: users.length,
      totalPages: Math.ceil(totalUsers / limit),
      currentPage: page,
      users,
    });
  }
);

// Get user by ID
export const getUserById = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    const user = await User.findById(req.params.id);

    if (!user) {
      return next(new AppError("User not found", 404));
    }

    res.status(200).json({
      success: true,
      user,
    });
  }
);

// Update user
export const updateUser = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    // Find the user
    const user = await User.findById(req.params.id);

    if (!user) {
      return next(new AppError("User not found", 404));
    }

    // Update the user
    const updatedUser = await User.findByIdAndUpdate(req.params.id, req.body, {
      new: true,
      runValidators: true,
    });

    res.status(200).json({
      success: true,
      user: updatedUser,
    });
  }
);

// Delete user
export const deleteUser = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    // Find the user
    const user = await User.findById(req.params.id);

    if (!user) {
      return next(new AppError("User not found", 404));
    }

    // Delete the user
    await User.findByIdAndDelete(req.params.id);

    res.status(200).json({
      success: true,
      message: "User deleted successfully",
    });
  }
);
