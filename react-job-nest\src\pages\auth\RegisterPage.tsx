import RegisterForm from "@/components/auth/RegisterForm";
import { Card, Text, Title } from "@mantine/core";
import { useEffect, useState } from "react";
import { useLocation } from "react-router";

export default function RegisterPage() {
  const location = useLocation();
  const [initialRole, setInitialRole] = useState<
    "candidate" | "employer" | null
  >(null);

  // Extract role from URL query parameters
  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const roleParam = searchParams.get("role");
    if (roleParam === "employer" || roleParam === "candidate") {
      setInitialRole(roleParam);
    }
  }, [location.search]);

  return (
    <div className="py-16">
      <Card
        shadow="md"
        padding="xl"
        radius="md"
        withBorder
        className="mx-auto w-[700px] max-w-full"
      >
        <Title order={2} className="mb-2 text-center">
          Create a JobNest Account
        </Title>

        {initialRole === "employer" ? (
          <Text c="dimmed" size="sm" className="mb-6 text-center">
            Join our platform to post jobs and find the best talent
          </Text>
        ) : initialRole === "candidate" ? (
          <Text c="dimmed" size="sm" className="mb-6 text-center">
            Join our platform to find your dream job and advance your career
          </Text>
        ) : (
          <Text c="dimmed" size="sm" className="mb-6 text-center">
            Join our platform to find your dream job or hire the best talent
          </Text>
        )}

        <RegisterForm initialRole={initialRole} />
      </Card>
    </div>
  );
}
