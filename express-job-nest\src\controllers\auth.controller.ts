import { Request, Response, NextFunction } from "express";
import jwt from "jsonwebtoken";
import crypto from "crypto";
import User, { IUser, UserRole } from "../models/user.model";
import Company from "../models/company.model";
import { AppError } from "../utils/app-error";
import { catchAsync } from "../utils/catch-async";

// Generate JWT token
const signToken = (id: string): string => {
  const secret = process.env.JWT_SECRET || "your-secret-key";
  const expiresIn = process.env.JWT_EXPIRES_IN || "7d";
  // Use any type assertion to bypass TypeScript error
  const sign = jwt.sign as any;
  return sign({ id }, secret, { expiresIn });
};

// Send JWT token as cookie and in response
const createSendToken = (
  user: IUser,
  statusCode: number,
  req: Request,
  res: Response
) => {
  const token = signToken(user._id as unknown as string);

  // Set cookie options
  const cookieOptions = {
    expires: new Date(
      Date.now() +
        parseInt(process.env.JWT_COOKIE_EXPIRES_IN || "7") * 24 * 60 * 60 * 1000
    ),
    httpOnly: true,
    secure: req.secure || req.headers["x-forwarded-proto"] === "https",
  };

  // Send cookie
  res.cookie("jwt", token, cookieOptions);

  // Remove password from output
  user.password = "";

  res.status(statusCode).json({
    success: true,
    token,
    user: {
      id: user._id,
      name: user.name,
      email: user.email,
      role: user.role,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    },
  });
};

// Register a new user
export const register = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    const {
      name,
      email,
      password,
      passwordConfirmation,
      role,
      // Company information fields
      companyName,
      industry,
      companySize,
      phone,
      website,
      description,
    } = req.body;

    // Check if passwords match
    if (password !== passwordConfirmation) {
      return next(new AppError("Passwords do not match", 400));
    }

    // Check if user already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return next(new AppError("Email already in use", 400));
    }

    // Create new user
    const newUser = await User.create({
      name, // For employers, this will be the company name
      email,
      password,
      role: role as UserRole,
    });

    // If the user is an employer and company information is provided, create a company
    if (role === "employer" && companyName && industry && companySize) {
      try {
        // Create a new company associated with the user
        await Company.create({
          name: companyName,
          description:
            description ||
            `${companyName} is a company in the ${industry} industry.`,
          website: website || "",
          industry,
          size: companySize,
          location: "Not specified", // Default value
          owner: newUser._id,
          contactEmail: email, // Use the same email for contact
          contactPhone: phone || "", // Use provided phone or empty string
        });
      } catch (error) {
        console.error("Error creating company:", error);
        // We don't want to fail the registration if company creation fails
        // In a production app, you might want to handle this differently
      }
    }

    // Send token
    createSendToken(newUser, 201, req, res);
  }
);

// Login user
export const login = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    const { email, password } = req.body;

    // Check if email and password exist
    if (!email || !password) {
      return next(new AppError("Please provide email and password", 400));
    }

    // Check if user exists && password is correct
    const user = await User.findOne({ email }).select("+password");

    if (!user || !(await user.comparePassword(password))) {
      return next(new AppError("Incorrect email or password", 401));
    }

    // Send token
    createSendToken(user, 200, req, res);
  }
);

// Logout user
export const logout = (_req: Request, res: Response) => {
  res.cookie("jwt", "loggedout", {
    expires: new Date(Date.now() + 10 * 1000),
    httpOnly: true,
  });
  res.status(200).json({ success: true, message: "Logged out successfully" });
};

// Get current user
export const getCurrentUser = catchAsync(
  async (req: Request, res: Response, _next: NextFunction) => {
    try {
      // Get user from request (set by protect middleware)
      const user = req.user;

      if (!user) {
        return res.status(401).json({
          success: false,
          message: "Not authenticated",
        });
      }

      // Send user data with token
      const token = signToken(user._id as unknown as string);

      res.status(200).json({
        success: true,
        token,
        user: {
          id: user._id,
          name: user.name,
          email: user.email,
          role: user.role,
          createdAt: user.createdAt,
          updatedAt: user.updatedAt,
        },
      });
    } catch (error) {
      console.error("Error in getCurrentUser:", error);
      res.status(500).json({
        success: false,
        message: "Error retrieving user data",
      });
    }
  }
);

// Forgot password
export const forgotPassword = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    // Get user based on POSTed email
    const user = await User.findOne({ email: req.body.email });
    if (!user) {
      return next(
        new AppError("There is no user with that email address", 404)
      );
    }

    // Generate random reset token
    const resetToken = crypto.randomBytes(32).toString("hex");

    // Hash token and set to passwordResetToken field
    user.passwordResetToken = crypto
      .createHash("sha256")
      .update(resetToken)
      .digest("hex");

    // Set token expiry (10 minutes)
    user.passwordResetExpires = new Date(Date.now() + 10 * 60 * 1000);

    await user.save({ validateBeforeSave: false });

    // In a real application, send email with reset token
    // For now, just return the token in the response
    res.status(200).json({
      success: true,
      message: "Token sent to email",
      resetToken, // In production, this would be sent via email, not in the response
    });
  }
);

// Reset password
export const resetPassword = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    // Get user based on the token
    const hashedToken = crypto
      .createHash("sha256")
      .update(req.params.token)
      .digest("hex");

    const user = await User.findOne({
      passwordResetToken: hashedToken,
      passwordResetExpires: { $gt: new Date() },
    });

    // If token has not expired, and there is a user, set the new password
    if (!user) {
      return next(new AppError("Token is invalid or has expired", 400));
    }

    // Update password
    user.password = req.body.password;
    user.passwordResetToken = undefined;
    user.passwordResetExpires = undefined;
    await user.save();

    // Log the user in, send JWT
    createSendToken(user, 200, req, res);
  }
);
