import mongoose, { Document, Schema } from 'mongoose';

export type SubscriptionStatus = 'active' | 'canceled' | 'expired' | 'pending';

export interface IUserSubscription extends Document {
  user: mongoose.Types.ObjectId;
  plan: mongoose.Types.ObjectId;
  status: SubscriptionStatus;
  startDate: Date;
  endDate: Date;
  billingPeriod: 'month' | 'year';
  autoRenew: boolean;
  paymentMethod: string;
  transactionId?: string;
  invoiceId?: string;
  price: number;
  currency: string;
  usageStats: {
    jobsPosted: number;
    cvViews: number;
    featuredJobsUsed: number;
  };
  canceledAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

const userSubscriptionSchema = new Schema<IUserSubscription>(
  {
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'User is required'],
    },
    plan: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'SubscriptionPlan',
      required: [true, 'Subscription plan is required'],
    },
    status: {
      type: String,
      enum: {
        values: ['active', 'canceled', 'expired', 'pending'],
        message: 'Status must be one of: active, canceled, expired, pending',
      },
      default: 'pending',
    },
    startDate: {
      type: Date,
      required: [true, 'Start date is required'],
    },
    endDate: {
      type: Date,
      required: [true, 'End date is required'],
    },
    billingPeriod: {
      type: String,
      enum: {
        values: ['month', 'year'],
        message: 'Billing period must be either month or year',
      },
      required: [true, 'Billing period is required'],
    },
    autoRenew: {
      type: Boolean,
      default: true,
    },
    paymentMethod: {
      type: String,
      required: [true, 'Payment method is required'],
    },
    transactionId: {
      type: String,
    },
    invoiceId: {
      type: String,
    },
    price: {
      type: Number,
      required: [true, 'Price is required'],
      min: [0, 'Price cannot be negative'],
    },
    currency: {
      type: String,
      default: 'USD',
    },
    usageStats: {
      jobsPosted: {
        type: Number,
        default: 0,
      },
      cvViews: {
        type: Number,
        default: 0,
      },
      featuredJobsUsed: {
        type: Number,
        default: 0,
      },
    },
    canceledAt: {
      type: Date,
    },
  },
  {
    timestamps: true, // Automatically add createdAt and updatedAt fields
  }
);

// Create indexes for better query performance
userSubscriptionSchema.index({ user: 1 });
userSubscriptionSchema.index({ status: 1 });
userSubscriptionSchema.index({ endDate: 1 });

const UserSubscription = mongoose.model<IUserSubscription>('UserSubscription', userSubscriptionSchema);

export default UserSubscription;
