"use client";

import { TrackingModal } from "@/components/candidate/applications/tracking-modal";
import { type Application } from "@/data/applications";
import { PageContainer, PageHeading } from "@/design-system/components";
import { useThemeClassesStatic } from "@/design-system/utils/theme-utils-static";
import candidateApi from "@/services/candidate-api";
import {
  Badge,
  Button,
  Card,
  Divider,
  Grid,
  Group,
  Loader,
  Paper,
  Progress,
  RingProgress,
  Select,
  SimpleGrid,
  Stack,
  Tabs,
  Text,
  TextInput,
  Title,
  useMantineColorScheme,
} from "@mantine/core";
import { notifications } from "@mantine/notifications";
import { useEffect, useMemo, useState } from "react";
import {
  BiBriefcase,
  BiBuildings,
  BiCalendar,
  BiCheckCircle,
  BiChevronRight,
  BiEnvelope,
  BiMap,
  BiSearch,
  BiSortAlt2,
  BiTime,
  BiX,
} from "react-icons/bi";

export default function CandidateApplicationsPage() {
  const [statusFilter, setStatusFilter] = useState<string | null>("");
  const [searchQuery, setSearchQuery] = useState("");
  const [sortBy, setSortBy] = useState<string>("newest");
  const [selectedApplication, setSelectedApplication] =
    useState<Application | null>(null);
  const [activeTab, setActiveTab] = useState<string | null>("all");

  const { colorScheme } = useMantineColorScheme();
  const isDark = colorScheme === "dark";
  const { getClasses } = useThemeClassesStatic();

  // State for API data
  const [applications, setApplications] = useState<Application[]>([]);
  // State for displayed applications (after filtering)
  const [displayedApplications, setDisplayedApplications] = useState<
    Application[]
  >([]);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch applications from the API
  useEffect(() => {
    const fetchApplications = async () => {
      try {
        setIsLoading(true);

        // Call the API to get the user's applications
        const response = await candidateApi.applications.getMyApplications();
        // Log the response for debugging
        console.log("Applications response:", response);

        if (response.data && response.data.applications) {
          // Transform the API response to match our Application type
          const fetchedApplications = response.data.applications.map(
            (app: any) => ({
              id: app._id,
              jobTitle: app.job?.title || "Unknown Job",
              company: app.job?.company?.name || "Unknown Company",
              appliedDate: new Date(app.createdAt).toISOString().split("T")[0],
              status: app.status || "pending", // Default to pending if status is missing
              jobType: app.job?.jobType || "Unknown",
              location: app.job?.location || "Unknown",
              companyLogo: app.job?.company?.logo || null,
            }),
          );

          console.log("Fetched applications:", fetchedApplications);
          setApplications(fetchedApplications);

          // Also set displayed applications initially
          if (activeTab === "all") {
            setDisplayedApplications(fetchedApplications);
          } else {
            const filtered = fetchedApplications.filter(
              (app) => app.status === activeTab,
            );
            setDisplayedApplications(filtered);
          }
        } else {
          // If no applications found, set empty array
          console.log("No applications found in response");
          setApplications([]);
          setDisplayedApplications([]);
        }
      } catch (error: any) {
        console.error("Error fetching applications:", error);

        // Set empty applications array on error
        setApplications([]);
        setDisplayedApplications([]);

        // Show error notification
        notifications.show({
          title: "Error",
          message:
            error.response?.data?.message ||
            "Failed to fetch applications. Please try again later.",
          color: "red",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchApplications();
  }, []);

  // Calculate application statistics
  const stats = useMemo(() => {
    const total = applications.length;
    const pending = applications.filter(
      (app) => app.status === "pending",
    ).length;
    const reviewing = applications.filter(
      (app) => app.status === "reviewing",
    ).length;
    const shortlisted = applications.filter(
      (app) => app.status === "shortlisted",
    ).length;
    const interview = applications.filter(
      (app) => app.status === "interview",
    ).length;
    const offered = applications.filter(
      (app) => app.status === "offered",
    ).length;
    const hired = applications.filter((app) => app.status === "hired").length;
    const rejected = applications.filter(
      (app) => app.status === "rejected",
    ).length;

    // For backward compatibility with UI components
    const accepted = hired + offered;
    const reviewed = reviewing + shortlisted + interview;

    return {
      total,
      pending,
      reviewing,
      shortlisted,
      interview,
      offered,
      hired,
      rejected,
      // For backward compatibility
      reviewed,
      accepted,
    };
  }, [applications]);

  // Update displayed applications when applications data or active tab changes
  useEffect(() => {
    console.log("Updating displayed applications based on tab:", activeTab);
    console.log("Applications data:", applications);

    // Create a copy of the applications array
    let filtered = [...applications];

    // Apply tab filtering
    if (activeTab && activeTab !== "all") {
      filtered = filtered.filter((app) => app.status === activeTab);
    }

    // Apply status filter if it's set
    if (statusFilter && statusFilter !== "") {
      filtered = filtered.filter((app) => app.status === statusFilter);
    }

    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        (app) =>
          app.jobTitle.toLowerCase().includes(query) ||
          app.company.toLowerCase().includes(query) ||
          app.location.toLowerCase().includes(query),
      );
    }

    // Sort applications
    filtered.sort((a, b) => {
      if (sortBy === "newest") {
        return (
          new Date(b.appliedDate).getTime() - new Date(a.appliedDate).getTime()
        );
      } else if (sortBy === "oldest") {
        return (
          new Date(a.appliedDate).getTime() - new Date(b.appliedDate).getTime()
        );
      } else if (sortBy === "company") {
        return a.company.localeCompare(b.company);
      } else {
        return 0;
      }
    });

    console.log("Setting displayed applications:", filtered.length, filtered);
    setDisplayedApplications(filtered);
  }, [applications, activeTab, statusFilter, searchQuery, sortBy]);

  // Get color based on status
  const getStatusColor = (status: string) => {
    const colors = {
      pending: "yellow",
      reviewing: "blue",
      shortlisted: "cyan",
      interview: "indigo",
      offered: "violet",
      hired: "green",
      rejected: "red",
    };
    return colors[status as keyof typeof colors] || "gray";
  };

  // Get icon based on status
  const getStatusIcon = (status: string) => {
    switch (status) {
      case "pending":
        return <BiTime size={18} />;
      case "reviewing":
        return <BiEnvelope size={18} />;
      case "shortlisted":
        return <BiCheckCircle size={18} />;
      case "interview":
        return <BiCalendar size={18} />;
      case "offered":
        return <BiEnvelope size={18} />;
      case "hired":
        return <BiCheckCircle size={18} />;
      case "rejected":
        return <BiX size={18} />;
      default:
        return <BiTime size={18} />;
    }
  };

  return (
    <PageContainer
      breadcrumbItems={[
        { title: "Home", href: "/" },
        { title: "Applications" },
      ]}
      variant="candidate"
    >
      {/* Header with title and search */}
      <div className="mb-6">
        <PageHeading
          title="My Applications"
          subtitle="Track the status of your job applications"
          variant="candidate"
        />
      </div>

      {/* Dashboard Summary Cards */}
      <SimpleGrid
        cols={{ base: 1, sm: 2, md: 4 }}
        spacing="md"
        className="mb-6"
      >
        <Card
          withBorder
          radius="md"
          p="md"
          className={getClasses("bg-blue-50", "bg-blue-900/20")}
        >
          <Group justify="space-between" align="flex-start">
            <div>
              <Text size="xs" c="dimmed" fw={500} className="mb-1">
                TOTAL APPLICATIONS
              </Text>
              <Text fw={700} size="xl">
                {stats.total}
              </Text>
            </div>
            <div
              className={getClasses(
                "rounded-full bg-blue-100 p-2",
                "rounded-full bg-blue-800/30 p-2",
              )}
            >
              <BiBriefcase size={24} className="text-blue-500" />
            </div>
          </Group>
        </Card>

        <Card
          withBorder
          radius="md"
          p="md"
          className={getClasses("bg-yellow-50", "bg-yellow-900/20")}
        >
          <Group justify="space-between" align="flex-start">
            <div>
              <Text size="xs" c="dimmed" fw={500} className="mb-1">
                PENDING
              </Text>
              <Text fw={700} size="xl">
                {stats.pending}
              </Text>
            </div>
            <div
              className={getClasses(
                "rounded-full bg-yellow-100 p-2",
                "rounded-full bg-yellow-800/30 p-2",
              )}
            >
              <BiTime size={24} className="text-yellow-500" />
            </div>
          </Group>
        </Card>

        <Card
          withBorder
          radius="md"
          p="md"
          className={getClasses("bg-green-50", "bg-green-900/20")}
        >
          <Group justify="space-between" align="flex-start">
            <div>
              <Text size="xs" c="dimmed" fw={500} className="mb-1">
                ACCEPTED
              </Text>
              <Text fw={700} size="xl">
                {stats.accepted}
              </Text>
            </div>
            <div
              className={getClasses(
                "rounded-full bg-green-100 p-2",
                "rounded-full bg-green-800/30 p-2",
              )}
            >
              <BiCheckCircle size={24} className="text-green-500" />
            </div>
          </Group>
        </Card>

        <Card
          withBorder
          radius="md"
          p="md"
          className={getClasses("bg-red-50", "bg-red-900/20")}
        >
          <Group justify="space-between" align="flex-start">
            <div>
              <Text size="xs" c="dimmed" fw={500} className="mb-1">
                REJECTED
              </Text>
              <Text fw={700} size="xl">
                {stats.rejected}
              </Text>
            </div>
            <div
              className={getClasses(
                "rounded-full bg-red-100 p-2",
                "rounded-full bg-red-800/30 p-2",
              )}
            >
              <BiX size={24} className="text-red-500" />
            </div>
          </Group>
        </Card>
      </SimpleGrid>

      {/* Progress Overview */}
      <Card withBorder radius="md" className="mb-6">
        <Group justify="space-between" className="mb-4">
          <Title order={4}>Application Progress</Title>
          <Text size="sm" c="dimmed">
            Last updated: {new Date().toLocaleDateString()}
          </Text>
        </Group>

        <Grid>
          <Grid.Col span={{ base: 12, md: 8 }}>
            <div className="mb-4">
              <Group justify="space-between" className="mb-1">
                <Text size="sm" fw={500}>
                  Pending
                </Text>
                <Text size="sm" c="dimmed">
                  {stats.pending}/{stats.total}
                </Text>
              </Group>
              <Progress
                value={(stats.pending / stats.total) * 100}
                color="yellow"
                size="md"
                radius="xl"
              />
            </div>

            <div className="mb-4">
              <Group justify="space-between" className="mb-1">
                <Text size="sm" fw={500}>
                  Reviewing
                </Text>
                <Text size="sm" c="dimmed">
                  {stats.reviewing}/{stats.total}
                </Text>
              </Group>
              <Progress
                value={(stats.reviewing / stats.total) * 100}
                color="blue"
                size="md"
                radius="xl"
              />
            </div>

            <div className="mb-4">
              <Group justify="space-between" className="mb-1">
                <Text size="sm" fw={500}>
                  Shortlisted
                </Text>
                <Text size="sm" c="dimmed">
                  {stats.shortlisted}/{stats.total}
                </Text>
              </Group>
              <Progress
                value={(stats.shortlisted / stats.total) * 100}
                color="cyan"
                size="md"
                radius="xl"
              />
            </div>

            <div className="mb-4">
              <Group justify="space-between" className="mb-1">
                <Text size="sm" fw={500}>
                  Interview
                </Text>
                <Text size="sm" c="dimmed">
                  {stats.interview}/{stats.total}
                </Text>
              </Group>
              <Progress
                value={(stats.interview / stats.total) * 100}
                color="indigo"
                size="md"
                radius="xl"
              />
            </div>

            <div className="mb-4">
              <Group justify="space-between" className="mb-1">
                <Text size="sm" fw={500}>
                  Hired
                </Text>
                <Text size="sm" c="dimmed">
                  {stats.hired}/{stats.total}
                </Text>
              </Group>
              <Progress
                value={(stats.hired / stats.total) * 100}
                color="green"
                size="md"
                radius="xl"
              />
            </div>

            <div>
              <Group justify="space-between" className="mb-1">
                <Text size="sm" fw={500}>
                  Rejected
                </Text>
                <Text size="sm" c="dimmed">
                  {stats.rejected}/{stats.total}
                </Text>
              </Group>
              <Progress
                value={(stats.rejected / stats.total) * 100}
                color="red"
                size="md"
                radius="xl"
              />
            </div>
          </Grid.Col>

          <Grid.Col
            span={{ base: 12, md: 4 }}
            className="flex items-center justify-center"
          >
            <div className="text-center">
              <RingProgress
                size={180}
                thickness={16}
                roundCaps
                sections={[
                  {
                    value: (stats.pending / stats.total) * 100,
                    color: "yellow",
                  },
                  {
                    value: (stats.reviewing / stats.total) * 100,
                    color: "blue",
                  },
                  {
                    value: (stats.shortlisted / stats.total) * 100,
                    color: "cyan",
                  },
                  {
                    value: (stats.interview / stats.total) * 100,
                    color: "indigo",
                  },
                  {
                    value: (stats.hired / stats.total) * 100,
                    color: "green",
                  },
                  { value: (stats.rejected / stats.total) * 100, color: "red" },
                ]}
                label={
                  <div className="text-center">
                    <Text fw={700} size="xl">
                      {stats.total}
                    </Text>
                    <Text size="xs" c="dimmed">
                      Applications
                    </Text>
                  </div>
                }
              />
            </div>
          </Grid.Col>
        </Grid>
      </Card>

      {/* Filters and Search */}
      <Card withBorder radius="md" className="mb-6">
        <Group justify="space-between" wrap="wrap" gap="md">
          <TextInput
            placeholder="Search jobs or companies"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.currentTarget.value)}
            leftSection={<BiSearch size={18} />}
            style={{ flexGrow: 1, maxWidth: "400px" }}
          />

          <Group gap="md">
            <Select
              placeholder="Sort by"
              value={sortBy}
              onChange={(value) => setSortBy(value || "newest")}
              data={[
                { value: "newest", label: "Newest first" },
                { value: "oldest", label: "Oldest first" },
                { value: "company", label: "Company name" },
              ]}
              leftSection={<BiSortAlt2 size={18} />}
            />

            <Select
              placeholder="Filter by status"
              value={statusFilter}
              onChange={(value) => {
                // When changing status filter, set the active tab to "all"
                // to avoid conflicts between tab and filter
                if (value !== null) {
                  setActiveTab("all");
                  setStatusFilter(value);

                  // Immediately update displayed applications based on the new filter
                  if (value === "") {
                    setDisplayedApplications([...applications]);
                  } else {
                    const filtered = applications.filter(
                      (app) => app.status === value,
                    );
                    setDisplayedApplications(filtered);
                  }
                } else {
                  setStatusFilter("");
                  // Show all applications when filter is cleared
                  setDisplayedApplications([...applications]);
                }
              }}
              data={[
                { value: "", label: "All statuses" },
                { value: "pending", label: "Pending" },
                { value: "reviewing", label: "Reviewing" },
                { value: "shortlisted", label: "Shortlisted" },
                { value: "interview", label: "Interview" },
                { value: "offered", label: "Offered" },
                { value: "hired", label: "Hired" },
                { value: "rejected", label: "Rejected" },
              ]}
              clearable
            />
          </Group>
        </Group>
      </Card>

      {/* Tabs */}
      <Tabs
        value={activeTab}
        onChange={(value) => {
          // When changing tabs, reset the status filter to avoid conflicts
          setStatusFilter("");
          setActiveTab(value);

          // Immediately update displayed applications based on the new tab
          if (value === "all") {
            setDisplayedApplications([...applications]);
          } else {
            const filtered = applications.filter((app) => app.status === value);
            setDisplayedApplications(filtered);
          }
        }}
        className="mb-6"
        styles={{
          tab: {
            color: isDark
              ? "var(--mantine-color-gray-4) !important"
              : undefined,
            "&[data-active]": {
              color: isDark
                ? "var(--mantine-color-blue-4) !important"
                : undefined,
              borderColor: isDark
                ? "var(--mantine-color-blue-4) !important"
                : undefined,
            },
          },
        }}
      >
        <Tabs.List>
          <Tabs.Tab value="all">All Applications</Tabs.Tab>
          <Tabs.Tab
            value="pending"
            leftSection={<BiTime size={16} />}
            rightSection={
              <Badge size="xs" variant="light" color="yellow">
                {stats.pending}
              </Badge>
            }
          >
            Pending
          </Tabs.Tab>
          <Tabs.Tab
            value="reviewing"
            leftSection={<BiEnvelope size={16} />}
            rightSection={
              <Badge size="xs" variant="light" color="blue">
                {stats.reviewing}
              </Badge>
            }
          >
            Reviewing
          </Tabs.Tab>
          <Tabs.Tab
            value="shortlisted"
            leftSection={<BiCheckCircle size={16} />}
            rightSection={
              <Badge size="xs" variant="light" color="cyan">
                {stats.shortlisted}
              </Badge>
            }
          >
            Shortlisted
          </Tabs.Tab>
          <Tabs.Tab
            value="interview"
            leftSection={<BiCalendar size={16} />}
            rightSection={
              <Badge size="xs" variant="light" color="indigo">
                {stats.interview}
              </Badge>
            }
          >
            Interview
          </Tabs.Tab>
          <Tabs.Tab
            value="hired"
            leftSection={<BiCheckCircle size={16} />}
            rightSection={
              <Badge size="xs" variant="light" color="green">
                {stats.hired}
              </Badge>
            }
          >
            Hired
          </Tabs.Tab>
          <Tabs.Tab
            value="rejected"
            leftSection={<BiX size={16} />}
            rightSection={
              <Badge size="xs" variant="light" color="red">
                {stats.rejected}
              </Badge>
            }
          >
            Rejected
          </Tabs.Tab>
        </Tabs.List>
      </Tabs>

      {/* Applications List */}
      {isLoading ? (
        <div className="flex justify-center py-12">
          <Loader size="lg" />
        </div>
      ) : displayedApplications.length === 0 ? (
        <Paper withBorder p="xl" radius="md" className="text-center">
          <Title
            order={3}
            className={getClasses("mb-2 text-gray-700", "mb-2 text-gray-300")}
          >
            No applications found
          </Title>
          <Text className={getClasses("text-gray-500", "text-gray-400")}>
            Try adjusting your search or filter criteria
          </Text>
        </Paper>
      ) : (
        <SimpleGrid cols={{ base: 1, md: 2 }} spacing="md">
          {displayedApplications.map((application: Application) => (
            <Card
              key={application.id}
              withBorder
              radius="md"
              className="h-full cursor-pointer transition-all hover:border-blue-300 hover:shadow-md"
              onClick={() => setSelectedApplication(application)}
            >
              <div className="relative">
                {/* Status indicator */}
                <div
                  className={`absolute top-0 right-0 h-3 w-3 rounded-full bg-${getStatusColor(
                    application.status,
                  )}-500 m-1`}
                ></div>

                <Group justify="space-between" className="mb-3">
                  <div className="flex items-center gap-3">
                    <div
                      className={`flex h-10 w-10 items-center justify-center rounded-md bg-${getStatusColor(
                        application.status,
                      )}-100 text-${getStatusColor(application.status)}-500`}
                    >
                      {getStatusIcon(application.status)}
                    </div>
                    <Badge size="lg" color={getStatusColor(application.status)}>
                      {application.status.charAt(0).toUpperCase() +
                        application.status.slice(1)}
                    </Badge>
                  </div>
                </Group>

                <Title
                  order={3}
                  className={getClasses(
                    "mb-1 line-clamp-1 text-xl text-[#0f172a]",
                    "mb-1 line-clamp-1 text-xl text-gray-200",
                  )}
                >
                  {application.jobTitle}
                </Title>

                <Group gap="xs" className="mb-3">
                  <BiBuildings className="text-primary-color" size={18} />
                  <Text className="text-primary-color line-clamp-1 font-medium">
                    {application.company}
                  </Text>
                </Group>

                <Divider className="my-3" />

                <Stack gap="sm" className="mb-3">
                  <Group
                    gap="xs"
                    className={getClasses("text-gray-600", "text-gray-400")}
                  >
                    <BiCalendar size={16} />
                    <Text size="sm">
                      Applied:{" "}
                      {new Date(application.appliedDate).toLocaleDateString(
                        "en-US",
                        {
                          year: "numeric",
                          month: "short",
                          day: "numeric",
                        },
                      )}
                    </Text>
                  </Group>

                  <Group
                    gap="xs"
                    className={getClasses("text-gray-600", "text-gray-400")}
                  >
                    <BiMap size={16} />
                    <Text size="sm" className="line-clamp-1">
                      {application.location}
                    </Text>
                  </Group>

                  <Group
                    gap="xs"
                    className={getClasses("text-gray-600", "text-gray-400")}
                  >
                    <BiBriefcase size={16} />
                    <Text size="sm">{application.jobType}</Text>
                  </Group>
                </Stack>

                <Button
                  variant="light"
                  color="blue"
                  fullWidth
                  rightSection={<BiChevronRight size={18} />}
                  onClick={(e) => {
                    e.stopPropagation();
                    setSelectedApplication(application);
                  }}
                >
                  View Details
                </Button>
              </div>
            </Card>
          ))}
        </SimpleGrid>
      )}

      {selectedApplication && (
        <TrackingModal
          application={selectedApplication}
          opened={!!selectedApplication}
          onClose={() => setSelectedApplication(null)}
        />
      )}
    </PageContainer>
  );
}
