import express from "express";
import {
  getAllSubscriptionPlans,
  getSubscriptionPlanById,
  createSubscriptionPlan,
  updateSubscriptionPlan,
  deleteSubscriptionPlan,
  subscribeToPlan,
  getCurrentSubscription,
  cancelSubscription,
  checkJobPostingEligibility,
} from "../controllers/subscription.controller";
import { protect, restrictTo } from "../middleware/auth.middleware";
// Import validators directly from express-validator
import { body } from "express-validator";
import { validationMiddleware } from "../validators/validation.middleware";

// Define validators inline
const subscribeToPlanValidator = [
  body("planId")
    .notEmpty()
    .withMessage("Plan ID is required")
    .isMongoId()
    .withMessage("Invalid plan ID format"),
  body("billingPeriod")
    .notEmpty()
    .withMessage("Billing period is required")
    .isIn(["month", "year"])
    .withMessage("Billing period must be either month or year"),
  body("paymentMethod")
    .notEmpty()
    .withMessage("Payment method is required")
    .isString()
    .withMessage("Payment method must be a string"),
  body("transactionId")
    .optional()
    .isString()
    .withMessage("Transaction ID must be a string"),
];

const createSubscriptionPlanValidator = [
  body("name")
    .notEmpty()
    .withMessage("Plan name is required")
    .isString()
    .withMessage("Plan name must be a string"),
  body("description")
    .notEmpty()
    .withMessage("Plan description is required")
    .isString()
    .withMessage("Plan description must be a string"),
  body("price")
    .notEmpty()
    .withMessage("Price is required")
    .isNumeric()
    .withMessage("Price must be a number")
    .custom((value) => value >= 0)
    .withMessage("Price cannot be negative"),
  body("annualPrice")
    .notEmpty()
    .withMessage("Annual price is required")
    .isNumeric()
    .withMessage("Annual price must be a number")
    .custom((value) => value >= 0)
    .withMessage("Annual price cannot be negative"),
];

const updateSubscriptionPlanValidator = [
  body("name").optional().isString().withMessage("Plan name must be a string"),
  body("description")
    .optional()
    .isString()
    .withMessage("Plan description must be a string"),
  body("price")
    .optional()
    .isNumeric()
    .withMessage("Price must be a number")
    .custom((value) => value >= 0)
    .withMessage("Price cannot be negative"),
  body("annualPrice")
    .optional()
    .isNumeric()
    .withMessage("Annual price must be a number")
    .custom((value) => value >= 0)
    .withMessage("Annual price cannot be negative"),
];

const router = express.Router();

// Public routes - anyone can view subscription plans
router.get("/plans", getAllSubscriptionPlans);
router.get("/plans/:id", getSubscriptionPlanById);

// Protected routes - user must be logged in
router.use(protect);

// Employer routes
router.get("/current", getCurrentSubscription);
router.post(
  "/subscribe",
  subscribeToPlanValidator,
  validationMiddleware,
  subscribeToPlan
);
router.patch("/cancel", cancelSubscription);
router.get("/check-job-posting-eligibility", checkJobPostingEligibility);

// Admin only routes
router.use(restrictTo("admin"));
router.post(
  "/plans",
  createSubscriptionPlanValidator,
  validationMiddleware,
  createSubscriptionPlan
);
router.patch(
  "/plans/:id",
  updateSubscriptionPlanValidator,
  validationMiddleware,
  updateSubscriptionPlan
);
router.delete("/plans/:id", deleteSubscriptionPlan);

export default router;
