import employerApi from "@/services/employer-api";
import { URLS } from "@/utils/urls";
import { useForm } from "@mantine/form";
import { notifications } from "@mantine/notifications";
import { zodResolver } from "mantine-form-zod-resolver";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router";
import { z } from "zod";
import { editJobSchema } from "../../schemas/employer/edit-job-schema";

// Type for form values
type FormValues = z.infer<typeof editJobSchema>;

// Mock job data for development mode
const mockJobData = {
  title: "Software Engineer",
  minSalary: 100000,
  maxSalary: 150000,
  currency: "USD",
  location: "remote",
  jobType: "full-time",
  description: "Write code and fix bugs",
  requirements: "Good communication skills",
  applicationDeadline: new Date(),
  requiredExperience: "2 years",
  category: "web-development",
  tags: ["React", "Node.js", "MongoDB"],
  benefits: ["Healthcare", "Paid leave", "Flexible schedule"],
  showSalary: true,
  company: {
    _id: "1",
    name: "Example Company",
    logo: null,
  },
  isActive: true,
};

export default function useEditJobForm(jobId: string) {
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [job, setJob] = useState<any>(null);
  const navigate = useNavigate();

  const form = useForm<FormValues>({
    initialValues: {
      jobTitle: "",
      minSalary: undefined,
      maxSalary: undefined,
      currency: "",
      location: "",
      jobType: "",
      jobDescription: "",
      jobRequirements: "",
      applicationDeadline: undefined,
      requiredExperience: "",
      jobAttachment: undefined,
      jobCategory: "",
      jobTags: [],
      benefits: [],
      showSalary: false,
    },
    validate: zodResolver(editJobSchema),
  });

  // Set mock data for development mode
  const setMockData = () => {
    console.log("Using mock job data for jobId:", jobId);

    // Set form values
    form.setValues({
      jobTitle: mockJobData.title,
      minSalary: mockJobData.minSalary,
      maxSalary: mockJobData.maxSalary,
      currency: mockJobData.currency,
      location: mockJobData.location,
      jobType: mockJobData.jobType,
      jobDescription: mockJobData.description,
      jobRequirements: mockJobData.requirements,
      applicationDeadline: mockJobData.applicationDeadline,
      requiredExperience: mockJobData.requiredExperience,
      jobAttachment: undefined,
      jobCategory: mockJobData.category,
      jobTags: mockJobData.tags,
      benefits: mockJobData.benefits,
      showSalary: mockJobData.showSalary,
    });

    // Set job state with a valid ID
    const mockId = jobId || "mock-job-id";
    console.log("Setting mock job with ID:", mockId);

    setJob({
      _id: mockId,
      ...mockJobData,
      applicationDeadline: mockJobData.applicationDeadline.toISOString(),
    });
  };

  // Fetch job data when component mounts
  useEffect(() => {
    const fetchJobData = async () => {
      try {
        setIsLoading(true);

        // Debug log the job ID
        console.log("useEditJobForm - jobId:", jobId);

        // For development mode without backend, use mock data immediately
        if (
          process.env.NODE_ENV === "development" &&
          (!jobId || jobId === "undefined" || jobId === "null")
        ) {
          console.log("Using mock data due to invalid jobId:", jobId);
          setMockData();
          return;
        }

        // Call API to get job details
        const response = await employerApi.jobs.getJobById(jobId);
        const responseData = response.data as any;

        if (responseData.success) {
          const jobData = responseData.job;
          setJob(jobData);

          // Map backend data to form values
          form.setValues({
            jobTitle: jobData.title,
            minSalary: jobData.minSalary,
            maxSalary: jobData.maxSalary,
            currency: jobData.currency,
            location: jobData.location,
            jobType: jobData.jobType,
            jobDescription: jobData.description,
            jobRequirements: jobData.requirements,
            applicationDeadline: jobData.applicationDeadline
              ? new Date(jobData.applicationDeadline)
              : undefined,
            requiredExperience: jobData.requiredExperience,
            jobAttachment: undefined, // Can't set file input value
            jobCategory: jobData.category,
            jobTags: jobData.tags || [],
            benefits: jobData.benefits || [],
            showSalary: jobData.showSalary,
          });
        } else {
          throw new Error(responseData.message || "Failed to fetch job data");
        }
      } catch (error: any) {
        console.error("Error fetching job data:", error);

        // In development mode, use mock data if backend is not available
        if (process.env.NODE_ENV === "development") {
          setMockData();
        } else {
          notifications.show({
            title: "Error",
            message:
              error.response?.data?.message || "Failed to fetch job data",
            color: "red",
          });

          // Redirect back to manage jobs page if job not found
          navigate(URLS.employer.manageJobs);
        }
      } finally {
        setIsLoading(false);
      }
    };

    fetchJobData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [jobId]);

  const handleSubmit = async (values: FormValues) => {
    try {
      setIsSubmitting(true);

      // Transform form values to match backend API
      const jobData = {
        title: values.jobTitle,
        description: values.jobDescription,
        requirements: values.jobRequirements,
        jobType: values.jobType.toLowerCase(), // Backend expects lowercase values
        location: values.location.toLowerCase(), // Backend expects lowercase values
        category: values.jobCategory,
        tags: Array.isArray(values.jobTags) ? values.jobTags : [],
        minSalary: values.minSalary || undefined,
        maxSalary: values.maxSalary || undefined,
        currency: values.currency || undefined,
        showSalary: Boolean(values.showSalary),
        benefits: Array.isArray(values.benefits) ? values.benefits : [],
        applicationDeadline: values.applicationDeadline
          ? values.applicationDeadline instanceof Date
            ? values.applicationDeadline.toISOString()
            : new Date(values.applicationDeadline).toISOString()
          : undefined,
        requiredExperience: values.requiredExperience,
      };

      // In development mode, simulate a successful update
      if (process.env.NODE_ENV === "development") {
        try {
          // Try to call the API, but handle network errors gracefully
          const response = await employerApi.jobs.updateJob(jobId, jobData);
          const responseData = response.data as any;

          if (responseData.success) {
            notifications.show({
              title: "Success",
              message: "Job updated successfully",
              color: "green",
            });

            // Redirect to manage jobs page
            navigate(URLS.employer.manageJobs);
            return;
          }
        } catch (apiError: any) {
          console.warn("API error in development mode:", apiError);

          // Show development mode notification
          notifications.show({
            title: "Development Mode",
            message: "Job updated successfully (simulated)",
            color: "blue",
          });

          // Redirect to manage jobs page
          navigate(URLS.employer.manageJobs);
          return;
        }
      } else {
        // Production mode - normal flow
        const response = await employerApi.jobs.updateJob(jobId, jobData);
        const responseData = response.data as any;

        if (responseData.success) {
          notifications.show({
            title: "Success",
            message: "Job updated successfully",
            color: "green",
          });

          // Redirect to manage jobs page
          navigate(URLS.employer.manageJobs);
        } else {
          throw new Error(responseData.message || "Failed to update job");
        }
      }
    } catch (error: any) {
      console.error("Error updating job:", error);

      // In development mode, simulate success even on error
      if (
        process.env.NODE_ENV === "development" &&
        (error.message?.includes("Network Error") ||
          error.response?.status === 404 ||
          !error.response)
      ) {
        notifications.show({
          title: "Development Mode",
          message: "Job updated successfully (simulated)",
          color: "blue",
        });

        // Redirect to manage jobs page
        navigate(URLS.employer.manageJobs);
      } else {
        notifications.show({
          title: "Error",
          message: error.response?.data?.message || "Failed to update job",
          color: "red",
        });
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    form,
    handleSubmit,
    isLoading,
    isSubmitting,
    job,
  };
}
