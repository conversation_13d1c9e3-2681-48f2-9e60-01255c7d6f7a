import { <PERSON><PERSON>, Card, ThemeIcon, useMantineColorScheme } from "@mantine/core";
import {
  FaArrowRight,
  FaBriefcase,
  FaFileAlt,
  FaSearch,
  FaUserPlus,
} from "react-icons/fa";
import { Link } from "react-router";

const steps = [
  {
    id: 1,
    icon: FaUserPlus,
    title: "Create an Account",
    description:
      "Sign up as an employer or job seeker in just a few minutes. Fill in your details and preferences to get started.",
    color: "blue",
  },
  {
    id: 2,
    icon: FaFileAlt,
    title: "Complete Your Profile",
    description:
      "Add your resume, skills, and experience as a job seeker, or company details as an employer.",
    color: "green",
  },
  {
    id: 3,
    icon: FaSearch,
    title: "Search or Post Jobs",
    description:
      "Browse available positions or post job openings with detailed descriptions and requirements.",
    color: "purple",
  },
  {
    id: 4,
    icon: FaBriefcase,
    title: "Connect and Hire",
    description:
      "Apply to jobs or review applications, schedule interviews, and find the perfect match.",
    color: "orange",
  },
];

export default function HowItWorks() {
  const { colorScheme } = useMantineColorScheme();
  const isDark = colorScheme === "dark";
  return (
    <section className={`py-24 ${isDark ? "bg-gray-900" : ""}`}>
      <div className="container">
        <div className="mx-auto max-w-3xl text-center">
          <h2 className="text-primary-color mb-4 text-sm font-semibold tracking-wider uppercase">
            Simple Process
          </h2>
          <h3
            className={`mb-6 text-4xl font-bold tracking-tight sm:text-5xl ${
              isDark ? "text-white" : "text-gray-900"
            }`}
          >
            How JobNest Works
          </h3>
          <p
            className={`mb-12 text-lg ${
              isDark ? "text-gray-300" : "text-gray-600"
            }`}
          >
            Our streamlined process makes it easy for both employers and job
            seekers to connect and find the perfect match.
          </p>
        </div>

        <div className="relative">
          {/* Connecting line */}
          <div
            className={`absolute top-0 left-1/2 hidden h-full w-1 -translate-x-1/2 ${
              isDark ? "bg-gray-700" : "bg-gray-200"
            } md:block`}
          ></div>

          <div className="space-y-12 md:space-y-0">
            {steps.map(
              ({ id, icon: Icon, title, description, color }, index) => (
                <div key={id} className="relative">
                  <div
                    className={`md:grid md:grid-cols-2 md:gap-8 ${
                      index % 2 === 0 ? "" : "md:flex-row-reverse"
                    }`}
                  >
                    {/* Empty div for alignment on alternating sides */}
                    <div
                      className={`hidden md:block ${
                        index % 2 === 0 ? "md:col-start-2" : "md:col-start-1"
                      }`}
                    ></div>

                    <div
                      className={`relative ${
                        index % 2 === 0 ? "md:col-start-1" : "md:col-start-2"
                      }`}
                    >
                      {/* Step number indicator */}
                      <div
                        className={`absolute top-0 left-1/2 hidden h-12 w-12 -translate-x-1/2 -translate-y-1/2 rounded-full border-4 ${
                          isDark
                            ? "border-gray-900 bg-gray-800 text-gray-200"
                            : "border-white bg-gray-100 text-gray-700"
                        } text-center text-lg leading-loose font-bold md:block`}
                      >
                        {id}
                      </div>

                      <Card
                        padding="xl"
                        radius="md"
                        className={`relative mt-7 md:mx-8 ${
                          isDark ? "bg-gray-800" : ""
                        }`}
                      >
                        <div className="mb-4 flex items-center gap-4">
                          <ThemeIcon size={50} radius="md" color={color}>
                            <Icon size={24} />
                          </ThemeIcon>
                          <h4
                            className={`text-xl font-bold ${
                              isDark ? "text-white" : ""
                            }`}
                          >
                            {title}
                          </h4>
                        </div>
                        <p
                          className={`${
                            isDark ? "text-gray-300" : "text-gray-600"
                          }`}
                        >
                          {description}
                        </p>
                      </Card>
                    </div>
                  </div>
                </div>
              ),
            )}
          </div>
        </div>

        <div className="mt-16 text-center">
          <Button
            component={Link}
            to="/auth/register"
            size="lg"
            rightSection={<FaArrowRight size={14} />}
            className={`bg-primary-color hover:bg-primary-color/90 ${
              isDark ? "text-white" : ""
            }`}
          >
            Get Started Now
          </Button>
        </div>
      </div>
    </section>
  );
}
