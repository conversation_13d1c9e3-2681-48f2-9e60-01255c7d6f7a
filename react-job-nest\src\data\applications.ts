export interface Application {
  id: string;
  jobTitle: string;
  company: string;
  appliedDate: string;
  status:
    | "pending"
    | "reviewing"
    | "shortlisted"
    | "interview"
    | "offered"
    | "hired"
    | "rejected";
  jobType: string;
  location: string;
  companyLogo?: string;
}

export const mockApplications: Application[] = [
  {
    id: "1",
    jobTitle: "Senior Full Stack Developer",
    company: "TechCorp Solutions",
    appliedDate: "2024-01-15",
    status: "pending",
    jobType: "Full-time",
    location: "San Francisco, CA",
    companyLogo: "https://example.com/logo1.png",
  },
  {
    id: "2",
    jobTitle: "Frontend Developer",
    company: "Digital Innovations",
    appliedDate: "2024-01-10",
    status: "reviewing",
    jobType: "Remote",
    location: "New York, NY",
    companyLogo: "https://example.com/logo2.png",
  },
  {
    id: "3",
    jobTitle: "React Developer",
    company: "WebTech Inc",
    appliedDate: "2024-01-05",
    status: "hired",
    jobType: "Contract",
    location: "Austin, TX",
    companyLogo: "https://example.com/logo3.png",
  },
  {
    id: "4",
    jobTitle: "Software Engineer",
    company: "Innovation Labs",
    appliedDate: "2024-01-01",
    status: "rejected",
    jobType: "Full-time",
    location: "Seattle, WA",
    companyLogo: "https://example.com/logo4.png",
  },
];
