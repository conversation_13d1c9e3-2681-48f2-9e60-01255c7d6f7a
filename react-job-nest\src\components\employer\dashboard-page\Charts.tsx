import useDashboard from "@/hooks/employer/use-dashboard";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "@mantine/charts";
import { Card, Grid, Title } from "@mantine/core";

export default function Charts() {
  // Get dashboard data from hook
  const { stats } = useDashboard();

  // Use real data from API or fallback to defaults
  const jobStatusData = stats.jobStatus || [];
  const applicationTrendData = stats.applicationTrends || [];
  return (
    <div className="mb-8">
      <Title order={3} className="mb-4 text-xl font-semibold text-blue-800">
        Analytics & Insights
      </Title>

      <Grid gutter="md">
        <Grid.Col span={{ base: 12, lg: 4 }}>
          <Card withBorder radius="md" className="h-full shadow-sm">
            <Title order={4} className="mb-4 text-lg font-semibold">
              Job Status Distribution
            </Title>
            <PieChart
              data={jobStatusData}
              withTooltip
              withLabels
              size={250}
              mx="auto"
              withLabelsLine
              labelsType="percent"
              labelsPosition="outside"
            />
          </Card>
        </Grid.Col>

        <Grid.Col span={{ base: 12, lg: 8 }}>
          <Card withBorder radius="md" className="h-full shadow-sm">
            <Title order={4} className="mb-4 text-lg font-semibold">
              Application Trends
            </Title>
            <AreaChart
              h={250}
              data={applicationTrendData}
              dataKey="date"
              series={[{ name: "Applications", color: "blue.6" }]}
              curveType="natural"
              withLegend
              withTooltip
              gridAxis="xy"
              yAxisProps={{ width: 50 }}
            />
          </Card>
        </Grid.Col>

        <Grid.Col span={12}>
          <Card withBorder radius="md" className="shadow-sm">
            <Title order={4} className="mb-4 text-lg font-semibold">
              Candidate Progress
            </Title>
            <BarChart
              h={250}
              data={stats.candidateProgress || []}
              dataKey="label"
              series={[{ name: "value", color: "blue.6" }]}
              withLegend
              withTooltip
              tickLine="y"
              yAxisProps={{ width: 50 }}
            />
          </Card>
        </Grid.Col>
      </Grid>
    </div>
  );
}
