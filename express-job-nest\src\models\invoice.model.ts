import mongoose, { Document, Schema } from "mongoose";
import { PaymentStatus } from "../services/payment.service";

export interface IInvoice extends Document {
  user: mongoose.Types.ObjectId;
  subscription?: mongoose.Types.ObjectId;
  invoiceNumber: string;
  amount: number;
  currency: string;
  description: string;
  status: PaymentStatus;
  paymentMethod: string;
  transactionId?: string;
  paymentDate?: Date;
  dueDate: Date;
  items: {
    name: string;
    description?: string;
    quantity: number;
    unitPrice: number;
    total: number;
  }[];
  subtotal: number;
  tax?: number;
  discount?: number;
  total: number;
  billingDetails: {
    name: string;
    email: string;
    address?: string;
    city?: string;
    state?: string;
    country?: string;
    postalCode?: string;
  };
  notes?: string;
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

const invoiceSchema = new Schema<IInvoice>(
  {
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: [true, "User is required"],
    },
    subscription: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "UserSubscription",
    },
    invoiceNumber: {
      type: String,
      required: [true, "Invoice number is required"],
    },
    amount: {
      type: Number,
      required: [true, "Amount is required"],
      min: [0, "Amount cannot be negative"],
    },
    currency: {
      type: String,
      required: [true, "Currency is required"],
      default: "USD",
    },
    description: {
      type: String,
      required: [true, "Description is required"],
    },
    status: {
      type: String,
      required: [true, "Status is required"],
      enum: ["pending", "completed", "failed", "refunded"],
      default: "pending",
    },
    paymentMethod: {
      type: String,
      required: [true, "Payment method is required"],
    },
    transactionId: {
      type: String,
    },
    paymentDate: {
      type: Date,
    },
    dueDate: {
      type: Date,
      required: [true, "Due date is required"],
    },
    items: [
      {
        name: {
          type: String,
          required: [true, "Item name is required"],
        },
        description: {
          type: String,
        },
        quantity: {
          type: Number,
          required: [true, "Item quantity is required"],
          min: [1, "Quantity must be at least 1"],
        },
        unitPrice: {
          type: Number,
          required: [true, "Unit price is required"],
          min: [0, "Unit price cannot be negative"],
        },
        total: {
          type: Number,
          required: [true, "Item total is required"],
          min: [0, "Total cannot be negative"],
        },
      },
    ],
    subtotal: {
      type: Number,
      required: [true, "Subtotal is required"],
      min: [0, "Subtotal cannot be negative"],
    },
    tax: {
      type: Number,
      min: [0, "Tax cannot be negative"],
    },
    discount: {
      type: Number,
      min: [0, "Discount cannot be negative"],
    },
    total: {
      type: Number,
      required: [true, "Total is required"],
      min: [0, "Total cannot be negative"],
    },
    billingDetails: {
      name: {
        type: String,
        required: [true, "Billing name is required"],
      },
      email: {
        type: String,
        required: [true, "Billing email is required"],
        lowercase: true,
      },
      address: String,
      city: String,
      state: String,
      country: String,
      postalCode: String,
    },
    notes: {
      type: String,
    },
    metadata: {
      type: Schema.Types.Mixed,
    },
  },
  {
    timestamps: true, // Automatically add createdAt and updatedAt fields
  }
);

// Create indexes for better query performance
invoiceSchema.index({ user: 1 });
invoiceSchema.index({ invoiceNumber: 1 }, { unique: true });
invoiceSchema.index({ status: 1 });
invoiceSchema.index({ createdAt: -1 });

// Generate invoice number before saving
invoiceSchema.pre("save", async function (next) {
  if (this.isNew) {
    // Get the current date
    const now = new Date();
    const year = now.getFullYear().toString();
    const month = (now.getMonth() + 1).toString().padStart(2, "0");

    // Get the count of invoices for the current month
    const count = await mongoose.model("Invoice").countDocuments({
      createdAt: {
        $gte: new Date(`${year}-${month}-01`),
        $lt: new Date(now.getFullYear(), now.getMonth() + 1, 1),
      },
    });

    // Generate invoice number: INV-YYYYMM-XXXX
    this.invoiceNumber = `INV-${year}${month}-${(count + 1)
      .toString()
      .padStart(4, "0")}`;
  }
  next();
});

const Invoice = mongoose.model<IInvoice>("Invoice", invoiceSchema);

export default Invoice;
