import { body } from 'express-validator';

export const createApplicationValidator = [
  body('job')
    .notEmpty().withMessage('Job ID is required')
    .isMongoId().withMessage('Invalid job ID format'),
  
  body('resume')
    .notEmpty().withMessage('Resume is required')
    .isURL().withMessage('Resume must be a valid URL'),
  
  body('coverLetter')
    .optional()
    .isString().withMessage('Cover letter must be a string'),
  
  body('questionAnswers')
    .optional()
    .isArray().withMessage('Question answers must be an array'),
  
  body('questionAnswers.*.question')
    .optional()
    .isString().withMessage('Question must be a string'),
  
  body('questionAnswers.*.answer')
    .optional()
    .isString().withMessage('Answer must be a string'),
];

export const updateApplicationStatusValidator = [
  body('status')
    .notEmpty().withMessage('Status is required')
    .isIn(['pending', 'reviewing', 'shortlisted', 'interview', 'offered', 'hired', 'rejected'])
    .withMessage('Status must be one of: pending, reviewing, shortlisted, interview, offered, hired, rejected'),
  
  body('notes')
    .optional()
    .isString().withMessage('Notes must be a string'),
];
