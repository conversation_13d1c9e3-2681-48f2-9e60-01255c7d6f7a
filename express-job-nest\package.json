{"name": "express-job-nest", "version": "1.0.0", "main": "index.js", "scripts": {"dev": "nodemon index.ts", "start": "ts-node index.ts", "build": "tsc", "start:prod": "node dist/index.js", "seed:subscription-plans": "ts-node src/seeds/subscription-plans.seed.ts", "add-default-subscription": "node src/scripts/add-default-subscription.js", "list-collections": "node src/scripts/list-collections.js", "disable-subscription": "node src/scripts/disable-subscription-check.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"@types/cors": "^2.8.18", "@types/express": "^5.0.1", "@types/jsonwebtoken": "^9.0.9", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.11", "@types/nodemailer": "^6.4.17", "@types/uuid": "^9.0.8", "@types/validator": "^13.15.0", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "dependencies": {"@types/cookie-parser": "^1.4.8", "bcryptjs": "^3.0.2", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "express-validator": "^7.2.1", "jsonwebtoken": "^9.0.2", "mammoth": "^1.9.0", "mongoose": "^8.14.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.2", "nodemailer": "^7.0.3", "pdf-parse": "^1.1.1", "uuid": "^9.0.1", "validator": "^13.15.0", "zod": "^3.24.4"}}