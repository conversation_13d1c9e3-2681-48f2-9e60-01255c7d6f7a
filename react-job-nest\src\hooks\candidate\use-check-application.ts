import candidateApi from "@/services/candidate-api";
import { useEffect, useState } from "react";

interface UseCheckApplicationReturn {
  hasApplied: boolean;
  isChecking: boolean;
  error: string | null;
}

/**
 * Hook to check if the current candidate has already applied for a specific job
 * @param jobId The ID of the job to check
 * @returns Object containing hasApplied status, loading state, and error
 */
export default function useCheckApplication(
  jobId?: string,
): UseCheckApplicationReturn {
  const [hasApplied, setHasApplied] = useState<boolean>(false);
  const [isChecking, setIsChecking] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const checkApplication = async () => {
      if (!jobId) {
        setIsChecking(false);
        return;
      }

      try {
        setIsChecking(true);

        // Get all applications from the candidate
        const response = await candidateApi.applications.getMyApplications();

        if (response.data && response.data.applications) {
          // Check if any of the applications are for this job
          const hasAppliedForJob = response.data.applications.some(
            (application) => {
              // Handle both string and object job references
              if (typeof application.job === "string") {
                return application.job === jobId;
              } else if (application.job && application.job._id) {
                return application.job._id === jobId;
              }
              return false;
            },
          );

          setHasApplied(hasAppliedForJob);
        }

        setError(null);
      } catch (err: any) {
        console.error("Error checking application status:", err);
        setError(err.message || "Failed to check application status");

        // In development mode, we can simulate a response
        if (process.env.NODE_ENV === "development") {
          console.log("Using mock application check in development mode");
          setHasApplied(false);
          setError(null);
        }
      } finally {
        setIsChecking(false);
      }
    };

    checkApplication();
  }, [jobId]);

  return { hasApplied, isChecking, error };
}
