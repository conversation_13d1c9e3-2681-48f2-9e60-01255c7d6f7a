import {
  AccordionChevron,
  ActionIcon,
  Box,
  CheckIcon,
  CloseButton,
  Input,
  InputBase,
  Modal,
  Popover,
  ScrollArea,
  SimpleGrid,
  UnstyledButton,
  createSafeContext,
  createVarsResolver,
  factory,
  getFontSize,
  getRadius,
  getSize,
  getSpacing,
  useInputProps,
  useMantineTheme,
  useProps,
  useResolvedStylesApi,
  useStyles
} from "./chunk-FYGUKJOL.js";
import "./chunk-FYDILROA.js";
import {
  require_jsx_runtime
} from "./chunk-MJNCUEZK.js";
import {
  clamp,
  useClickOutside,
  useDidUpdate,
  useDisclosure,
  useId,
  useMergedRef,
  useUncontrolled
} from "./chunk-4Q6ZKMFQ.js";
import {
  require_react
} from "./chunk-UGC3UZ7L.js";
import {
  clsx_default
} from "./chunk-U7P2NEEE.js";
import {
  __commonJS,
  __toESM
} from "./chunk-G3PMV62Z.js";

// node_modules/dayjs/dayjs.min.js
var require_dayjs_min = __commonJS({
  "node_modules/dayjs/dayjs.min.js"(exports, module) {
    !function(t, e) {
      "object" == typeof exports && "undefined" != typeof module ? module.exports = e() : "function" == typeof define && define.amd ? define(e) : (t = "undefined" != typeof globalThis ? globalThis : t || self).dayjs = e();
    }(exports, function() {
      "use strict";
      var t = 1e3, e = 6e4, n = 36e5, r = "millisecond", i = "second", s = "minute", u = "hour", a = "day", o = "week", c = "month", f = "quarter", h = "year", d = "date", l = "Invalid Date", $ = /^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/, y = /\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g, M = { name: "en", weekdays: "Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"), months: "January_February_March_April_May_June_July_August_September_October_November_December".split("_"), ordinal: function(t2) {
        var e2 = ["th", "st", "nd", "rd"], n2 = t2 % 100;
        return "[" + t2 + (e2[(n2 - 20) % 10] || e2[n2] || e2[0]) + "]";
      } }, m = function(t2, e2, n2) {
        var r2 = String(t2);
        return !r2 || r2.length >= e2 ? t2 : "" + Array(e2 + 1 - r2.length).join(n2) + t2;
      }, v = { s: m, z: function(t2) {
        var e2 = -t2.utcOffset(), n2 = Math.abs(e2), r2 = Math.floor(n2 / 60), i2 = n2 % 60;
        return (e2 <= 0 ? "+" : "-") + m(r2, 2, "0") + ":" + m(i2, 2, "0");
      }, m: function t2(e2, n2) {
        if (e2.date() < n2.date()) return -t2(n2, e2);
        var r2 = 12 * (n2.year() - e2.year()) + (n2.month() - e2.month()), i2 = e2.clone().add(r2, c), s2 = n2 - i2 < 0, u2 = e2.clone().add(r2 + (s2 ? -1 : 1), c);
        return +(-(r2 + (n2 - i2) / (s2 ? i2 - u2 : u2 - i2)) || 0);
      }, a: function(t2) {
        return t2 < 0 ? Math.ceil(t2) || 0 : Math.floor(t2);
      }, p: function(t2) {
        return { M: c, y: h, w: o, d: a, D: d, h: u, m: s, s: i, ms: r, Q: f }[t2] || String(t2 || "").toLowerCase().replace(/s$/, "");
      }, u: function(t2) {
        return void 0 === t2;
      } }, g = "en", D = {};
      D[g] = M;
      var p = "$isDayjsObject", S = function(t2) {
        return t2 instanceof _ || !(!t2 || !t2[p]);
      }, w = function t2(e2, n2, r2) {
        var i2;
        if (!e2) return g;
        if ("string" == typeof e2) {
          var s2 = e2.toLowerCase();
          D[s2] && (i2 = s2), n2 && (D[s2] = n2, i2 = s2);
          var u2 = e2.split("-");
          if (!i2 && u2.length > 1) return t2(u2[0]);
        } else {
          var a2 = e2.name;
          D[a2] = e2, i2 = a2;
        }
        return !r2 && i2 && (g = i2), i2 || !r2 && g;
      }, O = function(t2, e2) {
        if (S(t2)) return t2.clone();
        var n2 = "object" == typeof e2 ? e2 : {};
        return n2.date = t2, n2.args = arguments, new _(n2);
      }, b = v;
      b.l = w, b.i = S, b.w = function(t2, e2) {
        return O(t2, { locale: e2.$L, utc: e2.$u, x: e2.$x, $offset: e2.$offset });
      };
      var _ = function() {
        function M2(t2) {
          this.$L = w(t2.locale, null, true), this.parse(t2), this.$x = this.$x || t2.x || {}, this[p] = true;
        }
        var m2 = M2.prototype;
        return m2.parse = function(t2) {
          this.$d = function(t3) {
            var e2 = t3.date, n2 = t3.utc;
            if (null === e2) return /* @__PURE__ */ new Date(NaN);
            if (b.u(e2)) return /* @__PURE__ */ new Date();
            if (e2 instanceof Date) return new Date(e2);
            if ("string" == typeof e2 && !/Z$/i.test(e2)) {
              var r2 = e2.match($);
              if (r2) {
                var i2 = r2[2] - 1 || 0, s2 = (r2[7] || "0").substring(0, 3);
                return n2 ? new Date(Date.UTC(r2[1], i2, r2[3] || 1, r2[4] || 0, r2[5] || 0, r2[6] || 0, s2)) : new Date(r2[1], i2, r2[3] || 1, r2[4] || 0, r2[5] || 0, r2[6] || 0, s2);
              }
            }
            return new Date(e2);
          }(t2), this.init();
        }, m2.init = function() {
          var t2 = this.$d;
          this.$y = t2.getFullYear(), this.$M = t2.getMonth(), this.$D = t2.getDate(), this.$W = t2.getDay(), this.$H = t2.getHours(), this.$m = t2.getMinutes(), this.$s = t2.getSeconds(), this.$ms = t2.getMilliseconds();
        }, m2.$utils = function() {
          return b;
        }, m2.isValid = function() {
          return !(this.$d.toString() === l);
        }, m2.isSame = function(t2, e2) {
          var n2 = O(t2);
          return this.startOf(e2) <= n2 && n2 <= this.endOf(e2);
        }, m2.isAfter = function(t2, e2) {
          return O(t2) < this.startOf(e2);
        }, m2.isBefore = function(t2, e2) {
          return this.endOf(e2) < O(t2);
        }, m2.$g = function(t2, e2, n2) {
          return b.u(t2) ? this[e2] : this.set(n2, t2);
        }, m2.unix = function() {
          return Math.floor(this.valueOf() / 1e3);
        }, m2.valueOf = function() {
          return this.$d.getTime();
        }, m2.startOf = function(t2, e2) {
          var n2 = this, r2 = !!b.u(e2) || e2, f2 = b.p(t2), l2 = function(t3, e3) {
            var i2 = b.w(n2.$u ? Date.UTC(n2.$y, e3, t3) : new Date(n2.$y, e3, t3), n2);
            return r2 ? i2 : i2.endOf(a);
          }, $2 = function(t3, e3) {
            return b.w(n2.toDate()[t3].apply(n2.toDate("s"), (r2 ? [0, 0, 0, 0] : [23, 59, 59, 999]).slice(e3)), n2);
          }, y2 = this.$W, M3 = this.$M, m3 = this.$D, v2 = "set" + (this.$u ? "UTC" : "");
          switch (f2) {
            case h:
              return r2 ? l2(1, 0) : l2(31, 11);
            case c:
              return r2 ? l2(1, M3) : l2(0, M3 + 1);
            case o:
              var g2 = this.$locale().weekStart || 0, D2 = (y2 < g2 ? y2 + 7 : y2) - g2;
              return l2(r2 ? m3 - D2 : m3 + (6 - D2), M3);
            case a:
            case d:
              return $2(v2 + "Hours", 0);
            case u:
              return $2(v2 + "Minutes", 1);
            case s:
              return $2(v2 + "Seconds", 2);
            case i:
              return $2(v2 + "Milliseconds", 3);
            default:
              return this.clone();
          }
        }, m2.endOf = function(t2) {
          return this.startOf(t2, false);
        }, m2.$set = function(t2, e2) {
          var n2, o2 = b.p(t2), f2 = "set" + (this.$u ? "UTC" : ""), l2 = (n2 = {}, n2[a] = f2 + "Date", n2[d] = f2 + "Date", n2[c] = f2 + "Month", n2[h] = f2 + "FullYear", n2[u] = f2 + "Hours", n2[s] = f2 + "Minutes", n2[i] = f2 + "Seconds", n2[r] = f2 + "Milliseconds", n2)[o2], $2 = o2 === a ? this.$D + (e2 - this.$W) : e2;
          if (o2 === c || o2 === h) {
            var y2 = this.clone().set(d, 1);
            y2.$d[l2]($2), y2.init(), this.$d = y2.set(d, Math.min(this.$D, y2.daysInMonth())).$d;
          } else l2 && this.$d[l2]($2);
          return this.init(), this;
        }, m2.set = function(t2, e2) {
          return this.clone().$set(t2, e2);
        }, m2.get = function(t2) {
          return this[b.p(t2)]();
        }, m2.add = function(r2, f2) {
          var d2, l2 = this;
          r2 = Number(r2);
          var $2 = b.p(f2), y2 = function(t2) {
            var e2 = O(l2);
            return b.w(e2.date(e2.date() + Math.round(t2 * r2)), l2);
          };
          if ($2 === c) return this.set(c, this.$M + r2);
          if ($2 === h) return this.set(h, this.$y + r2);
          if ($2 === a) return y2(1);
          if ($2 === o) return y2(7);
          var M3 = (d2 = {}, d2[s] = e, d2[u] = n, d2[i] = t, d2)[$2] || 1, m3 = this.$d.getTime() + r2 * M3;
          return b.w(m3, this);
        }, m2.subtract = function(t2, e2) {
          return this.add(-1 * t2, e2);
        }, m2.format = function(t2) {
          var e2 = this, n2 = this.$locale();
          if (!this.isValid()) return n2.invalidDate || l;
          var r2 = t2 || "YYYY-MM-DDTHH:mm:ssZ", i2 = b.z(this), s2 = this.$H, u2 = this.$m, a2 = this.$M, o2 = n2.weekdays, c2 = n2.months, f2 = n2.meridiem, h2 = function(t3, n3, i3, s3) {
            return t3 && (t3[n3] || t3(e2, r2)) || i3[n3].slice(0, s3);
          }, d2 = function(t3) {
            return b.s(s2 % 12 || 12, t3, "0");
          }, $2 = f2 || function(t3, e3, n3) {
            var r3 = t3 < 12 ? "AM" : "PM";
            return n3 ? r3.toLowerCase() : r3;
          };
          return r2.replace(y, function(t3, r3) {
            return r3 || function(t4) {
              switch (t4) {
                case "YY":
                  return String(e2.$y).slice(-2);
                case "YYYY":
                  return b.s(e2.$y, 4, "0");
                case "M":
                  return a2 + 1;
                case "MM":
                  return b.s(a2 + 1, 2, "0");
                case "MMM":
                  return h2(n2.monthsShort, a2, c2, 3);
                case "MMMM":
                  return h2(c2, a2);
                case "D":
                  return e2.$D;
                case "DD":
                  return b.s(e2.$D, 2, "0");
                case "d":
                  return String(e2.$W);
                case "dd":
                  return h2(n2.weekdaysMin, e2.$W, o2, 2);
                case "ddd":
                  return h2(n2.weekdaysShort, e2.$W, o2, 3);
                case "dddd":
                  return o2[e2.$W];
                case "H":
                  return String(s2);
                case "HH":
                  return b.s(s2, 2, "0");
                case "h":
                  return d2(1);
                case "hh":
                  return d2(2);
                case "a":
                  return $2(s2, u2, true);
                case "A":
                  return $2(s2, u2, false);
                case "m":
                  return String(u2);
                case "mm":
                  return b.s(u2, 2, "0");
                case "s":
                  return String(e2.$s);
                case "ss":
                  return b.s(e2.$s, 2, "0");
                case "SSS":
                  return b.s(e2.$ms, 3, "0");
                case "Z":
                  return i2;
              }
              return null;
            }(t3) || i2.replace(":", "");
          });
        }, m2.utcOffset = function() {
          return 15 * -Math.round(this.$d.getTimezoneOffset() / 15);
        }, m2.diff = function(r2, d2, l2) {
          var $2, y2 = this, M3 = b.p(d2), m3 = O(r2), v2 = (m3.utcOffset() - this.utcOffset()) * e, g2 = this - m3, D2 = function() {
            return b.m(y2, m3);
          };
          switch (M3) {
            case h:
              $2 = D2() / 12;
              break;
            case c:
              $2 = D2();
              break;
            case f:
              $2 = D2() / 3;
              break;
            case o:
              $2 = (g2 - v2) / 6048e5;
              break;
            case a:
              $2 = (g2 - v2) / 864e5;
              break;
            case u:
              $2 = g2 / n;
              break;
            case s:
              $2 = g2 / e;
              break;
            case i:
              $2 = g2 / t;
              break;
            default:
              $2 = g2;
          }
          return l2 ? $2 : b.a($2);
        }, m2.daysInMonth = function() {
          return this.endOf(c).$D;
        }, m2.$locale = function() {
          return D[this.$L];
        }, m2.locale = function(t2, e2) {
          if (!t2) return this.$L;
          var n2 = this.clone(), r2 = w(t2, e2, true);
          return r2 && (n2.$L = r2), n2;
        }, m2.clone = function() {
          return b.w(this.$d, this);
        }, m2.toDate = function() {
          return new Date(this.valueOf());
        }, m2.toJSON = function() {
          return this.isValid() ? this.toISOString() : null;
        }, m2.toISOString = function() {
          return this.$d.toISOString();
        }, m2.toString = function() {
          return this.$d.toUTCString();
        }, M2;
      }(), k = _.prototype;
      return O.prototype = k, [["$ms", r], ["$s", i], ["$m", s], ["$H", u], ["$W", a], ["$M", c], ["$y", h], ["$D", d]].forEach(function(t2) {
        k[t2[1]] = function(e2) {
          return this.$g(e2, t2[0], t2[1]);
        };
      }), O.extend = function(t2, e2) {
        return t2.$i || (t2(e2, _, O), t2.$i = true), O;
      }, O.locale = w, O.isDayjs = S, O.unix = function(t2) {
        return O(1e3 * t2);
      }, O.en = D[g], O.Ls = D, O.p = {}, O;
    });
  }
});

// node_modules/dayjs/plugin/isoWeek.js
var require_isoWeek = __commonJS({
  "node_modules/dayjs/plugin/isoWeek.js"(exports, module) {
    !function(e, t) {
      "object" == typeof exports && "undefined" != typeof module ? module.exports = t() : "function" == typeof define && define.amd ? define(t) : (e = "undefined" != typeof globalThis ? globalThis : e || self).dayjs_plugin_isoWeek = t();
    }(exports, function() {
      "use strict";
      var e = "day";
      return function(t, i, s) {
        var a = function(t2) {
          return t2.add(4 - t2.isoWeekday(), e);
        }, d = i.prototype;
        d.isoWeekYear = function() {
          return a(this).year();
        }, d.isoWeek = function(t2) {
          if (!this.$utils().u(t2)) return this.add(7 * (t2 - this.isoWeek()), e);
          var i2, d2, n2, o, r = a(this), u = (i2 = this.isoWeekYear(), d2 = this.$u, n2 = (d2 ? s.utc : s)().year(i2).startOf("year"), o = 4 - n2.isoWeekday(), n2.isoWeekday() > 4 && (o += 7), n2.add(o, e));
          return r.diff(u, "week") + 1;
        }, d.isoWeekday = function(e2) {
          return this.$utils().u(e2) ? this.day() || 7 : this.day(this.day() % 7 ? e2 : e2 - 7);
        };
        var n = d.startOf;
        d.startOf = function(e2, t2) {
          var i2 = this.$utils(), s2 = !!i2.u(t2) || t2;
          return "isoweek" === i2.p(e2) ? s2 ? this.date(this.date() - (this.isoWeekday() - 1)).startOf("day") : this.date(this.date() - 1 - (this.isoWeekday() - 1) + 7).endOf("day") : n.bind(this)(e2, t2);
        };
      };
    });
  }
});

// node_modules/@mantine/dates/esm/utils/get-formatted-date/get-formatted-date.mjs
var import_dayjs = __toESM(require_dayjs_min(), 1);
function defaultDateFormatter({
  type,
  date,
  locale,
  format,
  labelSeparator
}) {
  const formatDate = (value) => (0, import_dayjs.default)(value).locale(locale).format(format);
  if (type === "default") {
    return date === null ? "" : formatDate(date);
  }
  if (type === "multiple") {
    return date.map(formatDate).join(", ");
  }
  if (type === "range" && Array.isArray(date)) {
    if (date[0] && date[1]) {
      return `${formatDate(date[0])} ${labelSeparator} ${formatDate(date[1])}`;
    }
    if (date[0]) {
      return `${formatDate(date[0])} ${labelSeparator} `;
    }
    return "";
  }
  return "";
}
function getFormattedDate({ formatter, ...others }) {
  return (formatter || defaultDateFormatter)(others);
}

// node_modules/@mantine/dates/esm/utils/handle-control-key-down/handle-control-key-down.mjs
function getNextIndex({ direction, levelIndex, rowIndex, cellIndex, size }) {
  switch (direction) {
    case "up":
      if (levelIndex === 0 && rowIndex === 0) {
        return null;
      }
      if (rowIndex === 0) {
        return {
          levelIndex: levelIndex - 1,
          rowIndex: cellIndex <= size[levelIndex - 1][size[levelIndex - 1].length - 1] - 1 ? size[levelIndex - 1].length - 1 : size[levelIndex - 1].length - 2,
          cellIndex
        };
      }
      return {
        levelIndex,
        rowIndex: rowIndex - 1,
        cellIndex
      };
    case "down":
      if (rowIndex === size[levelIndex].length - 1) {
        return {
          levelIndex: levelIndex + 1,
          rowIndex: 0,
          cellIndex
        };
      }
      if (rowIndex === size[levelIndex].length - 2 && cellIndex >= size[levelIndex][size[levelIndex].length - 1]) {
        return {
          levelIndex: levelIndex + 1,
          rowIndex: 0,
          cellIndex
        };
      }
      return {
        levelIndex,
        rowIndex: rowIndex + 1,
        cellIndex
      };
    case "left":
      if (levelIndex === 0 && rowIndex === 0 && cellIndex === 0) {
        return null;
      }
      if (rowIndex === 0 && cellIndex === 0) {
        return {
          levelIndex: levelIndex - 1,
          rowIndex: size[levelIndex - 1].length - 1,
          cellIndex: size[levelIndex - 1][size[levelIndex - 1].length - 1] - 1
        };
      }
      if (cellIndex === 0) {
        return {
          levelIndex,
          rowIndex: rowIndex - 1,
          cellIndex: size[levelIndex][rowIndex - 1] - 1
        };
      }
      return {
        levelIndex,
        rowIndex,
        cellIndex: cellIndex - 1
      };
    case "right":
      if (rowIndex === size[levelIndex].length - 1 && cellIndex === size[levelIndex][rowIndex] - 1) {
        return {
          levelIndex: levelIndex + 1,
          rowIndex: 0,
          cellIndex: 0
        };
      }
      if (cellIndex === size[levelIndex][rowIndex] - 1) {
        return {
          levelIndex,
          rowIndex: rowIndex + 1,
          cellIndex: 0
        };
      }
      return {
        levelIndex,
        rowIndex,
        cellIndex: cellIndex + 1
      };
    default:
      return { levelIndex, rowIndex, cellIndex };
  }
}
function focusOnNextFocusableControl({
  controlsRef,
  direction,
  levelIndex,
  rowIndex,
  cellIndex,
  size
}) {
  var _a, _b, _c;
  const nextIndex = getNextIndex({ direction, size, rowIndex, cellIndex, levelIndex });
  if (!nextIndex) {
    return;
  }
  const controlToFocus = (_c = (_b = (_a = controlsRef.current) == null ? void 0 : _a[nextIndex.levelIndex]) == null ? void 0 : _b[nextIndex.rowIndex]) == null ? void 0 : _c[nextIndex.cellIndex];
  if (!controlToFocus) {
    return;
  }
  if (controlToFocus.disabled || controlToFocus.getAttribute("data-hidden") || controlToFocus.getAttribute("data-outside")) {
    focusOnNextFocusableControl({
      controlsRef,
      direction,
      levelIndex: nextIndex.levelIndex,
      cellIndex: nextIndex.cellIndex,
      rowIndex: nextIndex.rowIndex,
      size
    });
  } else {
    controlToFocus.focus();
  }
}
function getDirection(key) {
  switch (key) {
    case "ArrowDown":
      return "down";
    case "ArrowUp":
      return "up";
    case "ArrowRight":
      return "right";
    case "ArrowLeft":
      return "left";
    default:
      return null;
  }
}
function getControlsSize(controlsRef) {
  var _a;
  return (_a = controlsRef.current) == null ? void 0 : _a.map((column) => column.map((row) => row.length));
}
function handleControlKeyDown({
  controlsRef,
  levelIndex,
  rowIndex,
  cellIndex,
  event
}) {
  const direction = getDirection(event.key);
  if (direction) {
    event.preventDefault();
    const size = getControlsSize(controlsRef);
    focusOnNextFocusableControl({
      controlsRef,
      direction,
      levelIndex,
      rowIndex,
      cellIndex,
      size
    });
  }
}

// node_modules/@mantine/dates/esm/utils/assign-time/assign-time.mjs
var import_dayjs2 = __toESM(require_dayjs_min(), 1);
function assignTime(dateValue, timeString) {
  let date = dateValue ? (0, import_dayjs2.default)(dateValue) : (0, import_dayjs2.default)();
  if (timeString === "") {
    return date.format("YYYY-MM-DD HH:mm:ss");
  }
  const [hours, minutes, seconds = 0] = timeString.split(":").map(Number);
  date = date.set("hour", hours);
  date = date.set("minute", minutes);
  date = date.set("second", seconds);
  date = date.set("millisecond", 0);
  return date.format("YYYY-MM-DD HH:mm:ss");
}

// node_modules/@mantine/dates/esm/utils/get-default-clamped-date/get-default-clamped-date.mjs
var import_dayjs4 = __toESM(require_dayjs_min(), 1);

// node_modules/@mantine/dates/esm/utils/to-date-string/to-date-string.mjs
var import_dayjs3 = __toESM(require_dayjs_min(), 1);
function toDateString(value) {
  return value == null ? value : (0, import_dayjs3.default)(value).format("YYYY-MM-DD");
}
function toDateTimeString(value) {
  return value == null ? value : (0, import_dayjs3.default)(value).format("YYYY-MM-DD HH:mm:ss");
}

// node_modules/@mantine/dates/esm/utils/get-default-clamped-date/get-default-clamped-date.mjs
function getDefaultClampedDate({
  minDate,
  maxDate
}) {
  const today = (0, import_dayjs4.default)();
  if (!minDate && !maxDate) {
    return toDateString(today);
  }
  if (minDate && (0, import_dayjs4.default)(today).isBefore(minDate)) {
    return toDateString(minDate);
  }
  if (maxDate && (0, import_dayjs4.default)(today).isAfter(maxDate)) {
    return toDateString(maxDate);
  }
  return toDateString(today);
}

// node_modules/@mantine/dates/esm/utils/clamp-date/clamp-date.mjs
var import_dayjs5 = __toESM(require_dayjs_min(), 1);
function clampDate(minDate, maxDate, date) {
  if (!minDate && !maxDate) {
    return toDateTimeString(date);
  }
  if (minDate && (0, import_dayjs5.default)(date).isBefore(minDate)) {
    return toDateTimeString(minDate);
  }
  if (maxDate && (0, import_dayjs5.default)(date).isAfter(maxDate)) {
    return toDateTimeString(maxDate);
  }
  return toDateTimeString(date);
}

// node_modules/@mantine/dates/esm/components/DatesProvider/DatesProvider.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_react = __toESM(require_react(), 1);
var DATES_PROVIDER_DEFAULT_SETTINGS = {
  locale: "en",
  firstDayOfWeek: 1,
  weekendDays: [0, 6],
  labelSeparator: "–",
  consistentWeeks: false
};
var DatesProviderContext = (0, import_react.createContext)(DATES_PROVIDER_DEFAULT_SETTINGS);
function DatesProvider({ settings, children }) {
  return (0, import_jsx_runtime.jsx)(DatesProviderContext.Provider, { value: { ...DATES_PROVIDER_DEFAULT_SETTINGS, ...settings }, children });
}

// node_modules/@mantine/dates/esm/components/DatesProvider/use-dates-context.mjs
var import_react2 = __toESM(require_react(), 1);
function useDatesContext() {
  const ctx = (0, import_react2.useContext)(DatesProviderContext);
  const getLocale = (0, import_react2.useCallback)((input) => input || ctx.locale, [ctx.locale]);
  const getFirstDayOfWeek = (0, import_react2.useCallback)(
    (input) => typeof input === "number" ? input : ctx.firstDayOfWeek,
    [ctx.firstDayOfWeek]
  );
  const getWeekendDays = (0, import_react2.useCallback)(
    (input) => Array.isArray(input) ? input : ctx.weekendDays,
    [ctx.weekendDays]
  );
  const getLabelSeparator = (0, import_react2.useCallback)(
    (input) => typeof input === "string" ? input : ctx.labelSeparator,
    [ctx.labelSeparator]
  );
  return {
    ...ctx,
    getLocale,
    getFirstDayOfWeek,
    getWeekendDays,
    getLabelSeparator
  };
}

// node_modules/@mantine/dates/esm/components/HiddenDatesInput/HiddenDatesInput.mjs
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_dayjs6 = __toESM(require_dayjs_min(), 1);
function formatValue({ value, type, withTime }) {
  const formatter = withTime ? toDateTimeString : toDateString;
  if (type === "range" && Array.isArray(value)) {
    const startDate = formatter(value[0]);
    const endDate = formatter(value[1]);
    if (!startDate) {
      return "";
    }
    if (!endDate) {
      return `${startDate} –`;
    }
    return `${startDate} – ${endDate}`;
  }
  if (type === "multiple" && Array.isArray(value)) {
    return value.filter(Boolean).join(", ");
  }
  if (!Array.isArray(value) && value) {
    return formatter(value);
  }
  return "";
}
function HiddenDatesInput({
  value,
  type,
  name,
  form,
  withTime = false
}) {
  return (0, import_jsx_runtime2.jsx)("input", { type: "hidden", value: formatValue({ value, type, withTime }), name, form });
}
HiddenDatesInput.displayName = "@mantine/dates/HiddenDatesInput";

// node_modules/@mantine/dates/esm/components/TimeInput/TimeInput.mjs
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);

// node_modules/@mantine/dates/esm/components/TimeInput/TimeInput.module.css.mjs
var classes = { "input": "m_468e7eda" };

// node_modules/@mantine/dates/esm/components/TimeInput/TimeInput.mjs
var defaultProps = {};
var TimeInput = factory((_props, ref) => {
  const props = useProps("TimeInput", defaultProps, _props);
  const {
    classNames,
    styles,
    unstyled,
    vars,
    withSeconds,
    minTime,
    maxTime,
    value,
    onChange,
    ...others
  } = props;
  const { resolvedClassNames, resolvedStyles } = useResolvedStylesApi({
    classNames,
    styles,
    props
  });
  const checkIfTimeLimitExceeded = (val) => {
    if (minTime !== void 0 || maxTime !== void 0) {
      const [hours, minutes, seconds] = val.split(":").map(Number);
      if (minTime) {
        const [minHours, minMinutes, minSeconds] = minTime.split(":").map(Number);
        if (hours < minHours || hours === minHours && minutes < minMinutes || withSeconds && hours === minHours && minutes === minMinutes && seconds < minSeconds) {
          return -1;
        }
      }
      if (maxTime) {
        const [maxHours, maxMinutes, maxSeconds] = maxTime.split(":").map(Number);
        if (hours > maxHours || hours === maxHours && minutes > maxMinutes || withSeconds && hours === maxHours && minutes === maxMinutes && seconds > maxSeconds) {
          return 1;
        }
      }
    }
    return 0;
  };
  const onTimeBlur = (event) => {
    var _a, _b, _c;
    (_a = props.onBlur) == null ? void 0 : _a.call(props, event);
    if (minTime !== void 0 || maxTime !== void 0) {
      const val = event.currentTarget.value;
      if (val) {
        const check = checkIfTimeLimitExceeded(val);
        if (check === 1) {
          event.currentTarget.value = maxTime;
          (_b = props.onChange) == null ? void 0 : _b.call(props, event);
        } else if (check === -1) {
          event.currentTarget.value = minTime;
          (_c = props.onChange) == null ? void 0 : _c.call(props, event);
        }
      }
    }
  };
  return (0, import_jsx_runtime3.jsx)(
    InputBase,
    {
      classNames: { ...resolvedClassNames, input: clsx_default(classes.input, resolvedClassNames == null ? void 0 : resolvedClassNames.input) },
      styles: resolvedStyles,
      unstyled,
      ref,
      value,
      ...others,
      step: withSeconds ? 1 : 60,
      onChange,
      onBlur: onTimeBlur,
      type: "time",
      __staticSelector: "TimeInput"
    }
  );
});
TimeInput.classes = InputBase.classes;
TimeInput.displayName = "@mantine/dates/TimeInput";

// node_modules/@mantine/dates/esm/components/TimePicker/TimePicker.mjs
var import_jsx_runtime13 = __toESM(require_jsx_runtime(), 1);
var import_react7 = __toESM(require_react(), 1);

// node_modules/@mantine/dates/esm/components/SpinInput/SpinInput.mjs
var import_jsx_runtime4 = __toESM(require_jsx_runtime(), 1);
var import_react3 = __toESM(require_react(), 1);

// node_modules/@mantine/dates/esm/components/TimePicker/utils/pad-time/pad-time.mjs
function padTime(value) {
  return value < 10 ? `0${value}` : `${value}`;
}

// node_modules/@mantine/dates/esm/components/SpinInput/SpinInput.mjs
var getMaxDigit = (max) => Number(max.toFixed(0)[0]);
var SpinInput = (0, import_react3.forwardRef)(
  ({
    value,
    min,
    max,
    onChange,
    focusable,
    step,
    onNextInput,
    onPreviousInput,
    onFocus,
    readOnly,
    ...others
  }, ref) => {
    const maxDigit = getMaxDigit(max);
    const handleChange = (value2) => {
      if (readOnly) {
        return;
      }
      const clearValue = value2.replace(/\D/g, "");
      if (clearValue !== "") {
        const parsedValue = clamp(parseInt(clearValue, 10), min, max);
        onChange(parsedValue);
        if (parsedValue > maxDigit) {
          onNextInput == null ? void 0 : onNextInput();
        }
      }
    };
    const handleKeyDown = (event) => {
      if (readOnly) {
        return;
      }
      if (event.key === "0" || event.key === "Num0") {
        if (value === 0) {
          event.preventDefault();
          onNextInput == null ? void 0 : onNextInput();
        }
      }
      if (event.key === "Home") {
        event.preventDefault();
        onChange(min);
      }
      if (event.key === "End") {
        event.preventDefault();
        onChange(max);
      }
      if (event.key === "Backspace" || event.key === "Delete") {
        event.preventDefault();
        if (value !== null) {
          onChange(null);
        } else {
          onPreviousInput == null ? void 0 : onPreviousInput();
        }
      }
      if (event.key === "ArrowRight") {
        event.preventDefault();
        onNextInput == null ? void 0 : onNextInput();
      }
      if (event.key === "ArrowLeft") {
        event.preventDefault();
        onPreviousInput == null ? void 0 : onPreviousInput();
      }
      if (event.key === "ArrowUp") {
        event.preventDefault();
        const newValue = value === null ? min : clamp(value + step, min, max);
        onChange(newValue);
      }
      if (event.key === "ArrowDown") {
        event.preventDefault();
        const newValue = value === null ? max : clamp(value - step, min, max);
        onChange(newValue);
      }
    };
    return (0, import_jsx_runtime4.jsx)(
      "input",
      {
        ref,
        type: "text",
        role: "spinbutton",
        "aria-valuemin": min,
        "aria-valuemax": max,
        "aria-valuenow": value === null ? 0 : value,
        "data-empty": value === null || void 0,
        inputMode: "numeric",
        placeholder: "--",
        value: value === null ? "" : padTime(value),
        onChange: (event) => handleChange(event.currentTarget.value),
        onKeyDown: handleKeyDown,
        onFocus: (event) => {
          event.currentTarget.select();
          onFocus == null ? void 0 : onFocus(event);
        },
        onClick: (event) => {
          event.stopPropagation();
          event.currentTarget.select();
        },
        onMouseDown: (event) => event.stopPropagation(),
        ...others
      }
    );
  }
);
SpinInput.displayName = "@mantine/dates/SpinInput";

// node_modules/@mantine/dates/esm/components/TimePicker/AmPmInput/AmPmInput.mjs
var import_jsx_runtime5 = __toESM(require_jsx_runtime(), 1);
var import_react4 = __toESM(require_react(), 1);

// node_modules/@mantine/dates/esm/components/TimePicker/TimePicker.context.mjs
var [TimePickerProvider, useTimePickerContext] = createSafeContext(
  "TimeInput component was not found in the component tree"
);

// node_modules/@mantine/dates/esm/components/TimePicker/AmPmInput/AmPmInput.mjs
var AmPmInput = (0, import_react4.forwardRef)(
  ({
    labels,
    value,
    onChange,
    className,
    style,
    onPreviousInput,
    readOnly,
    onMouseDown,
    onTouchStart,
    inputType,
    ...others
  }, ref) => {
    const ctx = useTimePickerContext();
    const handleKeyDown = (event) => {
      if (readOnly) {
        return;
      }
      if (event.key === "Home") {
        event.preventDefault();
        onChange(labels.am);
      }
      if (event.key === "End") {
        event.preventDefault();
        onChange(labels.pm);
      }
      if (event.key === "Backspace" || event.key === "Delete") {
        event.preventDefault();
        if (value === null) {
          onPreviousInput == null ? void 0 : onPreviousInput();
        } else {
          onChange(null);
        }
      }
      if (event.key === "ArrowLeft") {
        event.preventDefault();
        onPreviousInput == null ? void 0 : onPreviousInput();
      }
      if (event.key === "ArrowUp" || event.key === "ArrowDown") {
        event.preventDefault();
        onChange(value === labels.am ? labels.pm : labels.am);
      }
      if (event.code === "KeyA") {
        event.preventDefault();
        onChange(labels.am);
      }
      if (event.code === "KeyP") {
        event.preventDefault();
        onChange(labels.pm);
      }
    };
    if (inputType === "input") {
      return (0, import_jsx_runtime5.jsx)(
        "input",
        {
          ...ctx.getStyles("field", { className, style }),
          ref,
          value: value || "--",
          onChange: (event) => !readOnly && onChange(event.target.value || null),
          onClick: (event) => event.stopPropagation(),
          onKeyDown: handleKeyDown,
          onMouseDown: (event) => {
            event.stopPropagation();
            onMouseDown == null ? void 0 : onMouseDown(event);
          },
          "data-am-pm": true,
          ...others
        }
      );
    }
    return (0, import_jsx_runtime5.jsxs)(
      "select",
      {
        ...ctx.getStyles("field", { className, style }),
        ref,
        value: value || "",
        onChange: (event) => !readOnly && onChange(event.target.value || null),
        onClick: (event) => event.stopPropagation(),
        onKeyDown: handleKeyDown,
        onMouseDown: (event) => {
          event.stopPropagation();
          onMouseDown == null ? void 0 : onMouseDown(event);
        },
        "data-am-pm": true,
        ...others,
        children: [
          (0, import_jsx_runtime5.jsx)("option", { value: "", children: "--" }),
          (0, import_jsx_runtime5.jsx)("option", { value: labels.am, children: labels.am }),
          (0, import_jsx_runtime5.jsx)("option", { value: labels.pm, children: labels.pm })
        ]
      }
    );
  }
);
AmPmInput.displayName = "@mantine/dates/AmPmInput";

// node_modules/@mantine/dates/esm/components/TimePicker/TimeControlsList/AmPmControlsList.mjs
var import_jsx_runtime7 = __toESM(require_jsx_runtime(), 1);

// node_modules/@mantine/dates/esm/components/TimePicker/TimeControlsList/TimeControl.mjs
var import_jsx_runtime6 = __toESM(require_jsx_runtime(), 1);
function TimeControl({ value, active, onSelect }) {
  const ctx = useTimePickerContext();
  return (0, import_jsx_runtime6.jsx)(
    UnstyledButton,
    {
      mod: { active },
      onClick: () => onSelect(value),
      onMouseDown: (event) => event.preventDefault(),
      "data-value": value,
      tabIndex: -1,
      ...ctx.getStyles("control"),
      children: typeof value === "number" ? padTime(value) : value
    }
  );
}
TimeControl.displayName = "@mantine/dates/TimeControl";

// node_modules/@mantine/dates/esm/components/TimePicker/TimeControlsList/AmPmControlsList.mjs
function AmPmControlsList({ labels, value, onSelect }) {
  const ctx = useTimePickerContext();
  const controls = [labels.am, labels.pm].map((control) => (0, import_jsx_runtime7.jsx)(TimeControl, { value: control, active: value === control, onSelect }, control));
  return (0, import_jsx_runtime7.jsx)("div", { ...ctx.getStyles("controlsList"), children: controls });
}
AmPmControlsList.displayName = "@mantine/dates/AmPmControlsList";

// node_modules/@mantine/dates/esm/components/TimePicker/TimeControlsList/TimeControlsList.mjs
var import_jsx_runtime8 = __toESM(require_jsx_runtime(), 1);
var import_react5 = __toESM(require_react(), 1);
function isElementVisibleInScrollContainer(element, container) {
  if (!element || !container) {
    return false;
  }
  const elementRect = element.getBoundingClientRect();
  const containerRect = container.getBoundingClientRect();
  const isVisible = elementRect.top >= containerRect.top && elementRect.bottom <= containerRect.bottom && elementRect.left >= containerRect.left && elementRect.right <= containerRect.right;
  return isVisible;
}
function getValuesRange(min, max, step) {
  const range = [];
  for (let i = min; i <= max; i += step) {
    range.push(i);
  }
  return range;
}
function TimeControlsList({ min, max, step, value, onSelect }) {
  const ctx = useTimePickerContext();
  const ref = (0, import_react5.useRef)(null);
  const range = getValuesRange(min, max, step);
  const controls = range.map((control) => (0, import_jsx_runtime8.jsx)(TimeControl, { value: control, active: value === control, onSelect }, control));
  (0, import_react5.useEffect)(() => {
    var _a;
    if (value) {
      const target = (_a = ref.current) == null ? void 0 : _a.querySelector(`[data-value="${value}"]`);
      if (!isElementVisibleInScrollContainer(target, ref.current)) {
        target == null ? void 0 : target.scrollIntoView({ block: "nearest" });
      }
    }
  }, [value]);
  return (0, import_jsx_runtime8.jsx)(
    ScrollArea,
    {
      h: ctx.maxDropdownContentHeight,
      type: "never",
      viewportRef: ref,
      ...ctx.getStyles("scrollarea"),
      ...ctx.scrollAreaProps,
      children: (0, import_jsx_runtime8.jsx)("div", { ...ctx.getStyles("controlsList"), children: controls })
    }
  );
}
TimeControlsList.displayName = "@mantine/dates/TimeControlsList";

// node_modules/@mantine/dates/esm/components/TimePicker/TimePresets/TimePresets.mjs
var import_jsx_runtime12 = __toESM(require_jsx_runtime(), 1);

// node_modules/@mantine/dates/esm/components/TimePicker/utils/split-time-string/split-time-string.mjs
function splitTimeString(timeString) {
  const [hours = null, minutes = null, seconds = null] = timeString.split(":").map(Number);
  return { hours, minutes, seconds };
}

// node_modules/@mantine/dates/esm/components/TimePicker/utils/is-same-time/is-same-time.mjs
function isSameTime({ time, compare, withSeconds }) {
  const timeParts = splitTimeString(time);
  const compareParts = splitTimeString(compare);
  if (withSeconds) {
    return timeParts.hours === compareParts.hours && timeParts.minutes === compareParts.minutes && timeParts.seconds === compareParts.seconds;
  }
  return timeParts.hours === compareParts.hours && timeParts.minutes === compareParts.minutes;
}

// node_modules/@mantine/dates/esm/components/TimePicker/TimePresets/TimePresetControl.mjs
var import_jsx_runtime10 = __toESM(require_jsx_runtime(), 1);

// node_modules/@mantine/dates/esm/components/TimeValue/TimeValue.mjs
var import_jsx_runtime9 = __toESM(require_jsx_runtime(), 1);

// node_modules/@mantine/dates/esm/components/TimeValue/get-formatted-time/get-formatted-time.mjs
function getTimeFromDate(date, withSeconds) {
  return `${date.getHours()}:${date.getMinutes()}${withSeconds ? `:${date.getSeconds()}` : ""}`;
}
function getFormattedTime({
  value,
  format,
  amPmLabels,
  withSeconds
}) {
  const splitted = splitTimeString(
    typeof value === "string" ? value : getTimeFromDate(value, withSeconds)
  );
  if (splitted.hours === null || splitted.minutes === null) {
    return null;
  }
  if (format === "24h") {
    return `${padTime(splitted.hours)}:${padTime(splitted.minutes)}${withSeconds ? `:${padTime(splitted.seconds || 0)}` : ""}`;
  }
  const isPm = splitted.hours >= 12;
  const hours = splitted.hours % 12 === 0 ? 12 : splitted.hours % 12;
  return `${hours}:${padTime(splitted.minutes)}${withSeconds ? `:${padTime(splitted.seconds || 0)}` : ""} ${isPm ? amPmLabels.pm : amPmLabels.am}`;
}

// node_modules/@mantine/dates/esm/components/TimeValue/TimeValue.mjs
function TimeValue({
  value,
  format = "24h",
  amPmLabels = { am: "AM", pm: "PM" },
  withSeconds = false
}) {
  return (0, import_jsx_runtime9.jsx)(import_jsx_runtime9.Fragment, { children: getFormattedTime({ value, format, amPmLabels, withSeconds }) });
}
TimeValue.displayName = "@mantine/dates/TimeValue";

// node_modules/@mantine/dates/esm/components/TimePicker/TimePresets/TimePresetControl.mjs
function TimePresetControl({
  value,
  active,
  onChange,
  format,
  amPmLabels,
  withSeconds
}) {
  const ctx = useTimePickerContext();
  return (0, import_jsx_runtime10.jsx)(
    UnstyledButton,
    {
      mod: { active },
      onClick: () => onChange(value),
      ...ctx.getStyles("presetControl"),
      children: (0, import_jsx_runtime10.jsx)(TimeValue, { withSeconds, value, format, amPmLabels })
    }
  );
}
TimePresetControl.displayName = "@mantine/dates/TimePresetControl";

// node_modules/@mantine/dates/esm/components/TimePicker/TimePresets/TimePresetGroup.mjs
var import_jsx_runtime11 = __toESM(require_jsx_runtime(), 1);
function TimePresetGroup({
  value,
  data,
  onChange,
  format,
  amPmLabels,
  withSeconds
}) {
  const ctx = useTimePickerContext();
  const items = data.values.map((item) => (0, import_jsx_runtime11.jsx)(
    TimePresetControl,
    {
      value: item,
      format,
      amPmLabels,
      withSeconds,
      active: isSameTime({ time: item, compare: value, withSeconds }),
      onChange
    },
    item
  ));
  return (0, import_jsx_runtime11.jsxs)("div", { ...ctx.getStyles("presetsGroup"), children: [
    (0, import_jsx_runtime11.jsx)("div", { ...ctx.getStyles("presetsGroupLabel"), children: data.label }),
    (0, import_jsx_runtime11.jsx)(SimpleGrid, { cols: withSeconds ? 2 : 3, spacing: 4, children: items })
  ] });
}
TimePresetGroup.displayName = "@mantine/dates/TimePresetGroup";

// node_modules/@mantine/dates/esm/components/TimePicker/TimePresets/TimePresets.mjs
function TimePresets({
  presets,
  format,
  amPmLabels,
  withSeconds,
  value,
  onChange
}) {
  const ctx = useTimePickerContext();
  if (presets.length === 0) {
    return null;
  }
  if (typeof presets[0] === "string") {
    const items = presets.map((item) => (0, import_jsx_runtime12.jsx)(
      TimePresetControl,
      {
        value: item,
        format,
        amPmLabels,
        withSeconds,
        active: isSameTime({ time: item, compare: value, withSeconds }),
        onChange
      },
      item
    ));
    return (0, import_jsx_runtime12.jsx)(
      ScrollArea.Autosize,
      {
        mah: ctx.maxDropdownContentHeight,
        type: "never",
        ...ctx.getStyles("scrollarea"),
        ...ctx.scrollAreaProps,
        children: (0, import_jsx_runtime12.jsx)("div", { ...ctx.getStyles("presetsRoot"), children: (0, import_jsx_runtime12.jsx)(SimpleGrid, { cols: withSeconds ? 2 : 3, spacing: 4, children: items }) })
      }
    );
  }
  const groups = presets.map((group, index) => (0, import_jsx_runtime12.jsx)(
    TimePresetGroup,
    {
      data: group,
      value,
      format,
      amPmLabels,
      withSeconds,
      onChange
    },
    index
  ));
  return (0, import_jsx_runtime12.jsx)(
    ScrollArea.Autosize,
    {
      mah: ctx.maxDropdownContentHeight,
      type: "never",
      ...ctx.getStyles("scrollarea"),
      ...ctx.scrollAreaProps,
      children: (0, import_jsx_runtime12.jsx)("div", { ...ctx.getStyles("presetsRoot"), children: groups })
    }
  );
}
TimePresets.displayName = "@mantine/dates/TimePresets";

// node_modules/@mantine/dates/esm/components/TimePicker/use-time-picker.mjs
var import_react6 = __toESM(require_react(), 1);

// node_modules/@mantine/dates/esm/components/TimePicker/utils/time-to-seconds/time-to-seconds.mjs
function timeToSeconds(timeStr) {
  const [hours = 0, minutes = 0, seconds = 0] = timeStr.split(":").map(Number);
  return hours * 3600 + minutes * 60 + seconds;
}
function secondsToTime(seconds) {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor(seconds % 3600 / 60);
  const secs = seconds % 60;
  return {
    timeString: `${padTime(hours)}:${padTime(minutes)}:${padTime(secs)}`,
    hours,
    minutes,
    seconds: secs
  };
}

// node_modules/@mantine/dates/esm/components/TimePicker/utils/clamp-time/clamp-time.mjs
function clampTime(time, min, max) {
  const timeInSeconds = timeToSeconds(time);
  const minInSeconds = timeToSeconds(min);
  const maxInSeconds = timeToSeconds(max);
  const clampedSeconds = Math.max(minInSeconds, Math.min(timeInSeconds, maxInSeconds));
  return secondsToTime(clampedSeconds);
}

// node_modules/@mantine/dates/esm/components/TimePicker/utils/get-parsed-time/get-parsed-time.mjs
function convertTimeTo12HourFormat({
  hours,
  minutes,
  seconds,
  amPmLabels
}) {
  if (hours === null) {
    return { hours: null, minutes: null, seconds: null, amPm: null };
  }
  const amPm = hours >= 12 ? amPmLabels.pm : amPmLabels.am;
  const hour12 = hours % 12 === 0 ? 12 : hours % 12;
  return {
    hours: hour12,
    minutes: typeof minutes === "number" ? minutes : null,
    seconds: typeof seconds === "number" ? seconds : null,
    amPm
  };
}
function getParsedTime({ time, format, amPmLabels }) {
  if (time === "") {
    return { hours: null, minutes: null, seconds: null, amPm: null };
  }
  const { hours, minutes, seconds } = splitTimeString(time);
  const parsed = { hours, minutes, seconds };
  if (format === "12h") {
    return convertTimeTo12HourFormat({ ...parsed, amPmLabels });
  }
  return { ...parsed, amPm: null };
}

// node_modules/@mantine/dates/esm/components/TimePicker/utils/get-time-string/get-time-string.mjs
function convertTo24HourFormat({
  hours,
  minutes,
  seconds,
  amPm,
  amPmLabels,
  withSeconds
}) {
  let _hours = hours;
  if (amPm === amPmLabels.pm && hours !== 12) {
    _hours += 12;
  } else if (amPm === amPmLabels.am && hours === 12) {
    _hours = 0;
  }
  return `${padTime(_hours)}:${padTime(minutes)}${withSeconds ? `:${padTime(seconds || 0)}` : ""}`;
}
function getTimeString({
  hours,
  minutes,
  seconds,
  format,
  withSeconds,
  amPm,
  amPmLabels
}) {
  if (hours === null || minutes === null) {
    return { valid: false, value: "" };
  }
  if (withSeconds && seconds === null) {
    return { valid: false, value: "" };
  }
  if (format === "24h") {
    const value = `${padTime(hours)}:${padTime(minutes)}${withSeconds ? `:${padTime(seconds)}` : ""}`;
    return { valid: true, value };
  }
  if (amPm === null) {
    return { valid: false, value: "" };
  }
  return {
    valid: true,
    value: convertTo24HourFormat({ hours, minutes, seconds, amPm, amPmLabels, withSeconds })
  };
}

// node_modules/@mantine/dates/esm/components/TimePicker/use-time-picker.mjs
function useTimePicker({
  value,
  defaultValue,
  onChange,
  format,
  amPmLabels,
  withSeconds = false,
  min,
  max,
  clearable,
  readOnly,
  disabled,
  pasteSplit
}) {
  const parsedTime = getParsedTime({
    time: value || defaultValue || "",
    amPmLabels,
    format
  });
  const acceptChange = (0, import_react6.useRef)(true);
  const [hours, setHours] = (0, import_react6.useState)(parsedTime.hours);
  const [minutes, setMinutes] = (0, import_react6.useState)(parsedTime.minutes);
  const [seconds, setSeconds] = (0, import_react6.useState)(parsedTime.seconds);
  const [amPm, setAmPm] = (0, import_react6.useState)(parsedTime.amPm);
  const isClearable = clearable && !readOnly && !disabled && (hours !== null || minutes !== null || seconds !== null || amPm !== null);
  const hoursRef = (0, import_react6.useRef)(null);
  const minutesRef = (0, import_react6.useRef)(null);
  const secondsRef = (0, import_react6.useRef)(null);
  const amPmRef = (0, import_react6.useRef)(null);
  const focus = (field) => {
    var _a, _b, _c, _d;
    if (field === "hours") {
      (_a = hoursRef.current) == null ? void 0 : _a.focus();
    }
    if (field === "minutes") {
      (_b = minutesRef.current) == null ? void 0 : _b.focus();
    }
    if (field === "seconds") {
      (_c = secondsRef.current) == null ? void 0 : _c.focus();
    }
    if (field === "amPm") {
      (_d = amPmRef.current) == null ? void 0 : _d.focus();
    }
  };
  const handleTimeChange = (field, val) => {
    const computedValue = { hours, minutes, seconds, amPm, [field]: val };
    const timeString = getTimeString({ ...computedValue, format, withSeconds, amPmLabels });
    if (timeString.valid) {
      acceptChange.current = false;
      const clamped = clampTime(timeString.value, min || "00:00:00", max || "23:59:59");
      const converted = format === "12h" ? convertTimeTo12HourFormat({
        hours: clamped.hours,
        minutes: clamped.minutes,
        seconds: clamped.seconds,
        amPmLabels
      }) : clamped;
      setHours(converted.hours);
      setMinutes(converted.minutes);
      setSeconds(converted.seconds);
      onChange == null ? void 0 : onChange(clamped.timeString);
    } else {
      acceptChange.current = false;
      if (typeof value === "string" && value !== "") {
        onChange == null ? void 0 : onChange("");
      }
    }
  };
  const setTimeString = (timeString) => {
    acceptChange.current = false;
    const parsedTime2 = getParsedTime({ time: timeString, amPmLabels, format });
    setHours(parsedTime2.hours);
    setMinutes(parsedTime2.minutes);
    setSeconds(parsedTime2.seconds);
    setAmPm(parsedTime2.amPm);
    onChange == null ? void 0 : onChange(timeString);
  };
  const onHoursChange = (value2) => {
    setHours(value2);
    handleTimeChange("hours", value2);
    focus("hours");
  };
  const onMinutesChange = (value2) => {
    setMinutes(value2);
    handleTimeChange("minutes", value2);
    focus("minutes");
  };
  const onSecondsChange = (value2) => {
    setSeconds(value2);
    handleTimeChange("seconds", value2);
    focus("seconds");
  };
  const onAmPmChange = (value2) => {
    setAmPm(value2);
    handleTimeChange("amPm", value2);
    focus("amPm");
  };
  const clear = () => {
    acceptChange.current = false;
    setHours(null);
    setMinutes(null);
    setSeconds(null);
    setAmPm(null);
    onChange == null ? void 0 : onChange("");
    focus("hours");
  };
  const onPaste = (event) => {
    event.preventDefault();
    const pastedValue = event.clipboardData.getData("text");
    const parsedTime2 = (pasteSplit || getParsedTime)({ time: pastedValue, format, amPmLabels });
    const timeString = getTimeString({ ...parsedTime2, format, withSeconds, amPmLabels });
    if (timeString.valid) {
      acceptChange.current = false;
      const clamped = clampTime(timeString.value, min || "00:00:00", max || "23:59:59");
      onChange == null ? void 0 : onChange(clamped.timeString);
      setHours(parsedTime2.hours);
      setMinutes(parsedTime2.minutes);
      setSeconds(parsedTime2.seconds);
      setAmPm(parsedTime2.amPm);
    }
  };
  const hiddenInputValue = getTimeString({
    hours,
    minutes,
    seconds,
    format,
    withSeconds,
    amPm,
    amPmLabels
  });
  (0, import_react6.useEffect)(() => {
    if (acceptChange.current && typeof value === "string") {
      const parsedTime2 = getParsedTime({ time: value || "", amPmLabels, format });
      setHours(parsedTime2.hours);
      setMinutes(parsedTime2.minutes);
      setSeconds(parsedTime2.seconds);
      setAmPm(parsedTime2.amPm);
    }
    acceptChange.current = true;
  }, [value]);
  return {
    refs: { hours: hoursRef, minutes: minutesRef, seconds: secondsRef, amPm: amPmRef },
    values: { hours, minutes, seconds, amPm },
    setHours: onHoursChange,
    setMinutes: onMinutesChange,
    setSeconds: onSecondsChange,
    setAmPm: onAmPmChange,
    focus,
    clear,
    onPaste,
    setTimeString,
    isClearable,
    hiddenInputValue: hiddenInputValue.value
  };
}

// node_modules/@mantine/dates/esm/components/TimePicker/TimePicker.module.css.mjs
var classes2 = { "fieldsRoot": "m_7a8f1e6d", "fieldsGroup": "m_d6bb0a54", "controlsList": "m_b97ecb26", "controlsListGroup": "m_31fe42f9", "dropdown": "m_9c4817c3", "control": "m_154c536b", "presetControl": "m_7be09d0c", "presetsGroup": "m_7d00001d", "presetsGroupLabel": "m_d8d918d7", "field": "m_6b43ba88" };

// node_modules/@mantine/dates/esm/components/TimePicker/TimePicker.mjs
var defaultProps2 = {
  hoursStep: 1,
  minutesStep: 1,
  secondsStep: 1,
  format: "24h",
  amPmLabels: { am: "AM", pm: "PM" },
  withDropdown: false,
  pasteSplit: getParsedTime,
  maxDropdownContentHeight: 200
};
var varsResolver = createVarsResolver((_theme, { size }) => ({
  dropdown: {
    "--control-font-size": getFontSize(size)
  }
}));
var TimePicker = factory((_props, ref) => {
  const props = useProps("TimePicker", defaultProps2, _props);
  const {
    classNames,
    className,
    style,
    styles,
    unstyled,
    vars,
    onClick,
    format,
    value,
    defaultValue,
    onChange,
    hoursStep,
    minutesStep,
    secondsStep,
    withSeconds,
    hoursInputLabel,
    minutesInputLabel,
    secondsInputLabel,
    amPmInputLabel,
    amPmLabels,
    clearable,
    onMouseDown,
    onFocusCapture,
    onBlurCapture,
    min,
    max,
    popoverProps,
    withDropdown,
    rightSection,
    onFocus,
    onBlur,
    clearButtonProps,
    hoursInputProps,
    minutesInputProps,
    secondsInputProps,
    amPmSelectProps,
    readOnly,
    disabled,
    size,
    name,
    form,
    hiddenInputProps,
    labelProps,
    pasteSplit,
    hoursRef,
    minutesRef,
    secondsRef,
    amPmRef,
    presets,
    maxDropdownContentHeight,
    scrollAreaProps,
    ...others
  } = props;
  const { resolvedClassNames, resolvedStyles } = useResolvedStylesApi({
    classNames,
    styles,
    props
  });
  const getStyles = useStyles({
    name: "TimePicker",
    classes: classes2,
    props,
    className,
    style,
    classNames,
    styles,
    unstyled,
    vars,
    varsResolver
  });
  const controller = useTimePicker({
    value,
    defaultValue,
    onChange,
    format,
    amPmLabels,
    withSeconds,
    min,
    max,
    clearable,
    disabled,
    readOnly,
    pasteSplit
  });
  const _hoursRef = useMergedRef(controller.refs.hours, hoursRef);
  const _minutesRef = useMergedRef(controller.refs.minutes, minutesRef);
  const _secondsRef = useMergedRef(controller.refs.seconds, secondsRef);
  const _amPmRef = useMergedRef(controller.refs.amPm, amPmRef);
  const hoursInputId = useId();
  const hasFocusRef = (0, import_react7.useRef)(false);
  const [dropdownOpened, setDropdownOpened] = (0, import_react7.useState)(false);
  const handleFocus = (event) => {
    if (!hasFocusRef.current) {
      hasFocusRef.current = true;
      onFocus == null ? void 0 : onFocus(event);
    }
  };
  const handleBlur = (event) => {
    if (!event.currentTarget.contains(event.relatedTarget)) {
      hasFocusRef.current = false;
      onBlur == null ? void 0 : onBlur(event);
    }
  };
  return (0, import_jsx_runtime13.jsx)(
    TimePickerProvider,
    {
      value: { getStyles, scrollAreaProps, maxDropdownContentHeight },
      children: (0, import_jsx_runtime13.jsxs)(
        Popover,
        {
          opened: withDropdown && !readOnly && dropdownOpened,
          transitionProps: { duration: 0 },
          position: "bottom-start",
          withRoles: false,
          ...popoverProps,
          children: [
            (0, import_jsx_runtime13.jsx)(Popover.Target, { children: (0, import_jsx_runtime13.jsxs)(
              InputBase,
              {
                component: "div",
                size,
                disabled,
                ref,
                onClick: (event) => {
                  onClick == null ? void 0 : onClick(event);
                  controller.focus("hours");
                },
                onMouseDown: (event) => {
                  event.preventDefault();
                  onMouseDown == null ? void 0 : onMouseDown(event);
                },
                onFocusCapture: (event) => {
                  setDropdownOpened(true);
                  onFocusCapture == null ? void 0 : onFocusCapture(event);
                },
                onBlurCapture: (event) => {
                  setDropdownOpened(false);
                  onBlurCapture == null ? void 0 : onBlurCapture(event);
                },
                rightSection: rightSection || controller.isClearable && (0, import_jsx_runtime13.jsx)(
                  CloseButton,
                  {
                    ...clearButtonProps,
                    size,
                    onClick: (event) => {
                      var _a;
                      controller.clear();
                      (_a = clearButtonProps == null ? void 0 : clearButtonProps.onClick) == null ? void 0 : _a.call(clearButtonProps, event);
                    },
                    onMouseDown: (event) => {
                      var _a;
                      event.preventDefault();
                      (_a = clearButtonProps == null ? void 0 : clearButtonProps.onMouseDown) == null ? void 0 : _a.call(clearButtonProps, event);
                    }
                  }
                ),
                labelProps: { htmlFor: hoursInputId, ...labelProps },
                style,
                className,
                classNames: resolvedClassNames,
                styles: resolvedStyles,
                __staticSelector: "TimePicker",
                ...others,
                children: [
                  (0, import_jsx_runtime13.jsx)("div", { ...getStyles("fieldsRoot"), dir: "ltr", children: (0, import_jsx_runtime13.jsxs)("div", { ...getStyles("fieldsGroup"), onBlur: handleBlur, children: [
                    (0, import_jsx_runtime13.jsx)(
                      SpinInput,
                      {
                        id: hoursInputId,
                        ...hoursInputProps,
                        ...getStyles("field", {
                          className: hoursInputProps == null ? void 0 : hoursInputProps.className,
                          style: hoursInputProps == null ? void 0 : hoursInputProps.style
                        }),
                        value: controller.values.hours,
                        onChange: controller.setHours,
                        onNextInput: () => controller.focus("minutes"),
                        min: format === "12h" ? 1 : 0,
                        max: format === "12h" ? 12 : 23,
                        focusable: true,
                        step: hoursStep,
                        ref: _hoursRef,
                        "aria-label": hoursInputLabel,
                        readOnly,
                        disabled,
                        onPaste: controller.onPaste,
                        onFocus: (event) => {
                          var _a;
                          handleFocus(event);
                          (_a = hoursInputProps == null ? void 0 : hoursInputProps.onFocus) == null ? void 0 : _a.call(hoursInputProps, event);
                        }
                      }
                    ),
                    (0, import_jsx_runtime13.jsx)("span", { children: ":" }),
                    (0, import_jsx_runtime13.jsx)(
                      SpinInput,
                      {
                        ...minutesInputProps,
                        ...getStyles("field", {
                          className: minutesInputProps == null ? void 0 : minutesInputProps.className,
                          style: minutesInputProps == null ? void 0 : minutesInputProps.style
                        }),
                        value: controller.values.minutes,
                        onChange: controller.setMinutes,
                        min: 0,
                        max: 59,
                        focusable: true,
                        step: minutesStep,
                        ref: _minutesRef,
                        onPreviousInput: () => controller.focus("hours"),
                        onNextInput: () => withSeconds ? controller.focus("seconds") : controller.focus("amPm"),
                        "aria-label": minutesInputLabel,
                        tabIndex: -1,
                        readOnly,
                        disabled,
                        onPaste: controller.onPaste,
                        onFocus: (event) => {
                          var _a;
                          handleFocus(event);
                          (_a = minutesInputProps == null ? void 0 : minutesInputProps.onFocus) == null ? void 0 : _a.call(minutesInputProps, event);
                        }
                      }
                    ),
                    withSeconds && (0, import_jsx_runtime13.jsxs)(import_jsx_runtime13.Fragment, { children: [
                      (0, import_jsx_runtime13.jsx)("span", { children: ":" }),
                      (0, import_jsx_runtime13.jsx)(
                        SpinInput,
                        {
                          ...secondsInputProps,
                          ...getStyles("field", {
                            className: secondsInputProps == null ? void 0 : secondsInputProps.className,
                            style: secondsInputProps == null ? void 0 : secondsInputProps.style
                          }),
                          value: controller.values.seconds,
                          onChange: controller.setSeconds,
                          min: 0,
                          max: 59,
                          focusable: true,
                          step: secondsStep,
                          ref: _secondsRef,
                          onPreviousInput: () => controller.focus("minutes"),
                          onNextInput: () => controller.focus("amPm"),
                          "aria-label": secondsInputLabel,
                          tabIndex: -1,
                          readOnly,
                          disabled,
                          onPaste: controller.onPaste,
                          onFocus: (event) => {
                            var _a;
                            handleFocus(event);
                            (_a = secondsInputProps == null ? void 0 : secondsInputProps.onFocus) == null ? void 0 : _a.call(secondsInputProps, event);
                          }
                        }
                      )
                    ] }),
                    format === "12h" && (0, import_jsx_runtime13.jsx)(
                      AmPmInput,
                      {
                        ...amPmSelectProps,
                        inputType: withDropdown ? "input" : "select",
                        labels: amPmLabels,
                        value: controller.values.amPm,
                        onChange: controller.setAmPm,
                        ref: _amPmRef,
                        "aria-label": amPmInputLabel,
                        onPreviousInput: () => withSeconds ? controller.focus("seconds") : controller.focus("minutes"),
                        readOnly,
                        disabled,
                        tabIndex: -1,
                        onPaste: controller.onPaste,
                        onFocus: (event) => {
                          var _a;
                          handleFocus(event);
                          (_a = amPmSelectProps == null ? void 0 : amPmSelectProps.onFocus) == null ? void 0 : _a.call(amPmSelectProps, event);
                        }
                      }
                    )
                  ] }) }),
                  (0, import_jsx_runtime13.jsx)(
                    "input",
                    {
                      type: "hidden",
                      name,
                      form,
                      value: controller.hiddenInputValue,
                      ...hiddenInputProps
                    }
                  )
                ]
              }
            ) }),
            (0, import_jsx_runtime13.jsx)(
              Popover.Dropdown,
              {
                ...getStyles("dropdown"),
                onMouseDown: (event) => event.preventDefault(),
                children: presets ? (0, import_jsx_runtime13.jsx)(
                  TimePresets,
                  {
                    value: controller.hiddenInputValue,
                    onChange: controller.setTimeString,
                    format,
                    presets,
                    amPmLabels,
                    withSeconds: withSeconds || false
                  }
                ) : (0, import_jsx_runtime13.jsxs)("div", { ...getStyles("controlsListGroup"), children: [
                  (0, import_jsx_runtime13.jsx)(
                    TimeControlsList,
                    {
                      min: format === "12h" ? 1 : 0,
                      max: format === "12h" ? 12 : 23,
                      step: hoursStep,
                      value: controller.values.hours,
                      onSelect: controller.setHours
                    }
                  ),
                  (0, import_jsx_runtime13.jsx)(
                    TimeControlsList,
                    {
                      min: 0,
                      max: 59,
                      step: minutesStep,
                      value: controller.values.minutes,
                      onSelect: controller.setMinutes
                    }
                  ),
                  withSeconds && (0, import_jsx_runtime13.jsx)(
                    TimeControlsList,
                    {
                      min: 0,
                      max: 59,
                      step: secondsStep,
                      value: controller.values.seconds,
                      onSelect: controller.setSeconds
                    }
                  ),
                  format === "12h" && (0, import_jsx_runtime13.jsx)(
                    AmPmControlsList,
                    {
                      labels: amPmLabels,
                      value: controller.values.amPm,
                      onSelect: controller.setAmPm
                    }
                  )
                ] })
              }
            )
          ]
        }
      )
    }
  );
});
TimePicker.displayName = "@mantine/dates/TimePicker";
TimePicker.classes = classes2;

// node_modules/@mantine/dates/esm/components/TimePicker/utils/get-time-range/get-time-range.mjs
function getTimeRange({ startTime, endTime, interval }) {
  const timeRange = [];
  const startInSeconds = timeToSeconds(startTime);
  const endInSeconds = timeToSeconds(endTime);
  const intervalInSeconds = timeToSeconds(interval);
  for (let current = startInSeconds; current <= endInSeconds; current += intervalInSeconds) {
    timeRange.push(secondsToTime(current).timeString);
  }
  return timeRange;
}

// node_modules/@mantine/dates/esm/components/Day/Day.mjs
var import_jsx_runtime14 = __toESM(require_jsx_runtime(), 1);
var import_dayjs7 = __toESM(require_dayjs_min(), 1);

// node_modules/@mantine/dates/esm/components/Day/Day.module.css.mjs
var classes3 = { "day": "m_396ce5cb" };

// node_modules/@mantine/dates/esm/components/Day/Day.mjs
var defaultProps3 = {};
var varsResolver2 = createVarsResolver((_, { size }) => ({
  day: {
    "--day-size": getSize(size, "day-size")
  }
}));
var Day = factory((_props, ref) => {
  const props = useProps("Day", defaultProps3, _props);
  const {
    classNames,
    className,
    style,
    styles,
    unstyled,
    vars,
    date,
    disabled,
    __staticSelector,
    weekend,
    outside,
    selected,
    renderDay,
    inRange,
    firstInRange,
    lastInRange,
    hidden,
    static: isStatic,
    highlightToday,
    ...others
  } = props;
  const getStyles = useStyles({
    name: __staticSelector || "Day",
    classes: classes3,
    props,
    className,
    style,
    classNames,
    styles,
    unstyled,
    vars,
    varsResolver: varsResolver2,
    rootSelector: "day"
  });
  return (0, import_jsx_runtime14.jsx)(
    UnstyledButton,
    {
      ...getStyles("day", { style: hidden ? { display: "none" } : void 0 }),
      component: isStatic ? "div" : "button",
      ref,
      disabled,
      "data-today": (0, import_dayjs7.default)(date).isSame(/* @__PURE__ */ new Date(), "day") || void 0,
      "data-hidden": hidden || void 0,
      "data-highlight-today": highlightToday || void 0,
      "data-disabled": disabled || void 0,
      "data-weekend": !disabled && !outside && weekend || void 0,
      "data-outside": !disabled && outside || void 0,
      "data-selected": !disabled && selected || void 0,
      "data-in-range": inRange && !disabled || void 0,
      "data-first-in-range": firstInRange && !disabled || void 0,
      "data-last-in-range": lastInRange && !disabled || void 0,
      "data-static": isStatic || void 0,
      unstyled,
      ...others,
      children: (renderDay == null ? void 0 : renderDay(date)) || (0, import_dayjs7.default)(date).date()
    }
  );
});
Day.classes = classes3;
Day.displayName = "@mantine/dates/Day";

// node_modules/@mantine/dates/esm/components/WeekdaysRow/WeekdaysRow.mjs
var import_jsx_runtime15 = __toESM(require_jsx_runtime(), 1);

// node_modules/@mantine/dates/esm/components/WeekdaysRow/get-weekdays-names/get-weekdays-names.mjs
var import_dayjs8 = __toESM(require_dayjs_min(), 1);
function getWeekdayNames({
  locale,
  format = "dd",
  firstDayOfWeek = 1
}) {
  const baseDate = (0, import_dayjs8.default)().day(firstDayOfWeek);
  const labels = [];
  for (let i = 0; i < 7; i += 1) {
    if (typeof format === "string") {
      labels.push((0, import_dayjs8.default)(baseDate).add(i, "days").locale(locale).format(format));
    } else {
      labels.push(format((0, import_dayjs8.default)(baseDate).add(i, "days").format("YYYY-MM-DD")));
    }
  }
  return labels;
}

// node_modules/@mantine/dates/esm/components/WeekdaysRow/WeekdaysRow.module.css.mjs
var classes4 = { "weekday": "m_18a3eca" };

// node_modules/@mantine/dates/esm/components/WeekdaysRow/WeekdaysRow.mjs
var defaultProps4 = {};
var varsResolver3 = createVarsResolver((_, { size }) => ({
  weekdaysRow: {
    "--wr-fz": getFontSize(size),
    "--wr-spacing": getSpacing(size)
  }
}));
var WeekdaysRow = factory((_props, ref) => {
  const props = useProps("WeekdaysRow", defaultProps4, _props);
  const {
    classNames,
    className,
    style,
    styles,
    unstyled,
    vars,
    locale,
    firstDayOfWeek,
    weekdayFormat,
    cellComponent: CellComponent = "th",
    __staticSelector,
    withWeekNumbers,
    ...others
  } = props;
  const getStyles = useStyles({
    name: __staticSelector || "WeekdaysRow",
    classes: classes4,
    props,
    className,
    style,
    classNames,
    styles,
    unstyled,
    vars,
    varsResolver: varsResolver3,
    rootSelector: "weekdaysRow"
  });
  const ctx = useDatesContext();
  const weekdays = getWeekdayNames({
    locale: ctx.getLocale(locale),
    format: weekdayFormat,
    firstDayOfWeek: ctx.getFirstDayOfWeek(firstDayOfWeek)
  }).map((weekday, index) => (0, import_jsx_runtime15.jsx)(CellComponent, { ...getStyles("weekday"), children: weekday }, index));
  return (0, import_jsx_runtime15.jsxs)(Box, { component: "tr", ref, ...getStyles("weekdaysRow"), ...others, children: [
    withWeekNumbers && (0, import_jsx_runtime15.jsx)(CellComponent, { ...getStyles("weekday"), children: "#" }),
    weekdays
  ] });
});
WeekdaysRow.classes = classes4;
WeekdaysRow.displayName = "@mantine/dates/WeekdaysRow";

// node_modules/@mantine/dates/esm/components/Month/get-end-of-week/get-end-of-week.mjs
var import_dayjs9 = __toESM(require_dayjs_min(), 1);
function getEndOfWeek(date, firstDayOfWeek = 1) {
  let value = (0, import_dayjs9.default)(date);
  const lastDayOfWeek = firstDayOfWeek === 0 ? 6 : firstDayOfWeek - 1;
  while (value.day() !== lastDayOfWeek) {
    value = value.add(1, "day");
  }
  return value.format("YYYY-MM-DD");
}

// node_modules/@mantine/dates/esm/components/Month/get-start-of-week/get-start-of-week.mjs
var import_dayjs10 = __toESM(require_dayjs_min(), 1);
function getStartOfWeek(date, firstDayOfWeek = 1) {
  let value = (0, import_dayjs10.default)(date);
  while (value.day() !== firstDayOfWeek) {
    value = value.subtract(1, "day");
  }
  return value.format("YYYY-MM-DD");
}

// node_modules/@mantine/dates/esm/components/Month/get-month-days/get-month-days.mjs
var import_dayjs11 = __toESM(require_dayjs_min(), 1);
function getMonthDays({
  month,
  firstDayOfWeek = 1,
  consistentWeeks
}) {
  const day = (0, import_dayjs11.default)(month).subtract((0, import_dayjs11.default)(month).date() - 1, "day");
  const start = (0, import_dayjs11.default)(day.format("YYYY-M-D"));
  const startOfMonth = start.format("YYYY-MM-DD");
  const endOfMonth = start.add(+start.daysInMonth() - 1, "day").format("YYYY-MM-DD");
  const endDate = getEndOfWeek(endOfMonth, firstDayOfWeek);
  const weeks = [];
  let date = (0, import_dayjs11.default)(getStartOfWeek(startOfMonth, firstDayOfWeek));
  while ((0, import_dayjs11.default)(date).isBefore(endDate, "day")) {
    const days = [];
    for (let i = 0; i < 7; i += 1) {
      days.push(date.format("YYYY-MM-DD"));
      date = date.add(1, "day");
    }
    weeks.push(days);
  }
  if (consistentWeeks && weeks.length < 6) {
    const lastWeek = weeks[weeks.length - 1];
    const lastDay = lastWeek[lastWeek.length - 1];
    let nextDay = (0, import_dayjs11.default)(lastDay).add(1, "day");
    while (weeks.length < 6) {
      const days = [];
      for (let i = 0; i < 7; i += 1) {
        days.push(nextDay.format("YYYY-MM-DD"));
        nextDay = nextDay.add(1, "day");
      }
      weeks.push(days);
    }
  }
  return weeks;
}

// node_modules/@mantine/dates/esm/components/Month/is-same-month/is-same-month.mjs
var import_dayjs12 = __toESM(require_dayjs_min(), 1);
function isSameMonth(date, comparison) {
  return (0, import_dayjs12.default)(date).format("YYYY-MM") === (0, import_dayjs12.default)(comparison).format("YYYY-MM");
}

// node_modules/@mantine/dates/esm/components/Month/Month.mjs
var import_jsx_runtime16 = __toESM(require_jsx_runtime(), 1);
var import_dayjs17 = __toESM(require_dayjs_min(), 1);

// node_modules/@mantine/dates/esm/components/Month/get-date-in-tab-order/get-date-in-tab-order.mjs
var import_dayjs15 = __toESM(require_dayjs_min(), 1);

// node_modules/@mantine/dates/esm/components/Month/is-after-min-date/is-after-min-date.mjs
var import_dayjs13 = __toESM(require_dayjs_min(), 1);
function isAfterMinDate(date, minDate) {
  return minDate ? (0, import_dayjs13.default)(date).isAfter((0, import_dayjs13.default)(minDate).subtract(1, "day"), "day") : true;
}

// node_modules/@mantine/dates/esm/components/Month/is-before-max-date/is-before-max-date.mjs
var import_dayjs14 = __toESM(require_dayjs_min(), 1);
function isBeforeMaxDate(date, maxDate) {
  return maxDate ? (0, import_dayjs14.default)(date).isBefore((0, import_dayjs14.default)(maxDate).add(1, "day"), "day") : true;
}

// node_modules/@mantine/dates/esm/components/Month/get-date-in-tab-order/get-date-in-tab-order.mjs
function getDateInTabOrder({
  dates,
  minDate,
  maxDate,
  getDayProps,
  excludeDate,
  hideOutsideDates,
  month
}) {
  const enabledDates = dates.flat().filter(
    (date) => {
      var _a;
      return isBeforeMaxDate(date, maxDate) && isAfterMinDate(date, minDate) && !(excludeDate == null ? void 0 : excludeDate(date)) && !((_a = getDayProps == null ? void 0 : getDayProps(date)) == null ? void 0 : _a.disabled) && (!hideOutsideDates || isSameMonth(date, month));
    }
  );
  const selectedDate = enabledDates.find((date) => {
    var _a;
    return (_a = getDayProps == null ? void 0 : getDayProps(date)) == null ? void 0 : _a.selected;
  });
  if (selectedDate) {
    return selectedDate;
  }
  const currentDate = enabledDates.find((date) => (0, import_dayjs15.default)().isSame(date, "date"));
  if (currentDate) {
    return currentDate;
  }
  return enabledDates[0];
}

// node_modules/@mantine/dates/esm/components/Month/get-week-number/get-week-number.mjs
var import_dayjs16 = __toESM(require_dayjs_min(), 1);
var import_isoWeek = __toESM(require_isoWeek(), 1);
import_dayjs16.default.extend(import_isoWeek.default);
function getWeekNumber(week) {
  const monday = week.find((date) => (0, import_dayjs16.default)(date).day() === 1);
  return (0, import_dayjs16.default)(monday).isoWeek();
}

// node_modules/@mantine/dates/esm/components/Month/Month.module.css.mjs
var classes5 = { "month": "m_cc9820d3", "monthCell": "m_8f457cd5", "weekNumber": "m_6cff9dea" };

// node_modules/@mantine/dates/esm/components/Month/Month.mjs
var defaultProps5 = {
  withCellSpacing: true
};
var varsResolver4 = createVarsResolver((_, { size }) => ({
  weekNumber: {
    "--wn-fz": getFontSize(size),
    "--wn-size": getSize(size, "wn-size")
  }
}));
var Month = factory((_props, ref) => {
  const props = useProps("Month", defaultProps5, _props);
  const {
    classNames,
    className,
    style,
    styles,
    unstyled,
    vars,
    __staticSelector,
    locale,
    firstDayOfWeek,
    weekdayFormat,
    month,
    weekendDays,
    getDayProps,
    excludeDate,
    minDate,
    maxDate,
    renderDay,
    hideOutsideDates,
    hideWeekdays,
    getDayAriaLabel,
    static: isStatic,
    __getDayRef,
    __onDayKeyDown,
    __onDayClick,
    __onDayMouseEnter,
    __preventFocus,
    __stopPropagation,
    withCellSpacing,
    size,
    highlightToday,
    withWeekNumbers,
    ...others
  } = props;
  const getStyles = useStyles({
    name: __staticSelector || "Month",
    classes: classes5,
    props,
    className,
    style,
    classNames,
    styles,
    unstyled,
    vars,
    varsResolver: varsResolver4,
    rootSelector: "month"
  });
  const ctx = useDatesContext();
  const dates = getMonthDays({
    month,
    firstDayOfWeek: ctx.getFirstDayOfWeek(firstDayOfWeek),
    consistentWeeks: ctx.consistentWeeks
  });
  const dateInTabOrder = getDateInTabOrder({
    dates,
    minDate: toDateString(minDate),
    maxDate: toDateString(maxDate),
    getDayProps,
    excludeDate,
    hideOutsideDates,
    month
  });
  const { resolvedClassNames, resolvedStyles } = useResolvedStylesApi({
    classNames,
    styles,
    props
  });
  const rows = dates.map((row, rowIndex) => {
    const cells = row.map((date, cellIndex) => {
      const outside = !isSameMonth(date, month);
      const ariaLabel = (getDayAriaLabel == null ? void 0 : getDayAriaLabel(date)) || (0, import_dayjs17.default)(date).locale(locale || ctx.locale).format("D MMMM YYYY");
      const dayProps = getDayProps == null ? void 0 : getDayProps(date);
      const isDateInTabOrder = (0, import_dayjs17.default)(date).isSame(dateInTabOrder, "date");
      return (0, import_jsx_runtime16.jsx)(
        "td",
        {
          ...getStyles("monthCell"),
          "data-with-spacing": withCellSpacing || void 0,
          children: (0, import_jsx_runtime16.jsx)(
            Day,
            {
              __staticSelector: __staticSelector || "Month",
              classNames: resolvedClassNames,
              styles: resolvedStyles,
              unstyled,
              "data-mantine-stop-propagation": __stopPropagation || void 0,
              highlightToday,
              renderDay,
              date,
              size,
              weekend: ctx.getWeekendDays(weekendDays).includes((0, import_dayjs17.default)(date).get("day")),
              outside,
              hidden: hideOutsideDates ? outside : false,
              "aria-label": ariaLabel,
              static: isStatic,
              disabled: (excludeDate == null ? void 0 : excludeDate(date)) || !isBeforeMaxDate(date, toDateString(maxDate)) || !isAfterMinDate(date, toDateString(minDate)),
              ref: (node) => __getDayRef == null ? void 0 : __getDayRef(rowIndex, cellIndex, node),
              ...dayProps,
              onKeyDown: (event) => {
                var _a;
                (_a = dayProps == null ? void 0 : dayProps.onKeyDown) == null ? void 0 : _a.call(dayProps, event);
                __onDayKeyDown == null ? void 0 : __onDayKeyDown(event, { rowIndex, cellIndex, date });
              },
              onMouseEnter: (event) => {
                var _a;
                (_a = dayProps == null ? void 0 : dayProps.onMouseEnter) == null ? void 0 : _a.call(dayProps, event);
                __onDayMouseEnter == null ? void 0 : __onDayMouseEnter(event, date);
              },
              onClick: (event) => {
                var _a;
                (_a = dayProps == null ? void 0 : dayProps.onClick) == null ? void 0 : _a.call(dayProps, event);
                __onDayClick == null ? void 0 : __onDayClick(event, date);
              },
              onMouseDown: (event) => {
                var _a;
                (_a = dayProps == null ? void 0 : dayProps.onMouseDown) == null ? void 0 : _a.call(dayProps, event);
                __preventFocus && event.preventDefault();
              },
              tabIndex: __preventFocus || !isDateInTabOrder ? -1 : 0
            }
          )
        },
        date.toString()
      );
    });
    return (0, import_jsx_runtime16.jsxs)("tr", { ...getStyles("monthRow"), children: [
      withWeekNumbers && (0, import_jsx_runtime16.jsx)("td", { ...getStyles("weekNumber"), children: getWeekNumber(row) }),
      cells
    ] }, rowIndex);
  });
  return (0, import_jsx_runtime16.jsxs)(Box, { component: "table", ...getStyles("month"), size, ref, ...others, children: [
    !hideWeekdays && (0, import_jsx_runtime16.jsx)("thead", { ...getStyles("monthThead"), children: (0, import_jsx_runtime16.jsx)(
      WeekdaysRow,
      {
        __staticSelector: __staticSelector || "Month",
        locale,
        firstDayOfWeek,
        weekdayFormat,
        size,
        classNames: resolvedClassNames,
        styles: resolvedStyles,
        unstyled,
        withWeekNumbers
      }
    ) }),
    (0, import_jsx_runtime16.jsx)("tbody", { ...getStyles("monthTbody"), children: rows })
  ] });
});
Month.classes = classes5;
Month.displayName = "@mantine/dates/Month";

// node_modules/@mantine/dates/esm/components/PickerControl/PickerControl.mjs
var import_jsx_runtime17 = __toESM(require_jsx_runtime(), 1);

// node_modules/@mantine/dates/esm/components/PickerControl/PickerControl.module.css.mjs
var classes6 = { "pickerControl": "m_dc6a3c71" };

// node_modules/@mantine/dates/esm/components/PickerControl/PickerControl.mjs
var defaultProps6 = {};
var varsResolver5 = createVarsResolver((_, { size }) => ({
  pickerControl: {
    "--dpc-fz": getFontSize(size),
    "--dpc-size": getSize(size, "dpc-size")
  }
}));
var PickerControl = factory((_props, ref) => {
  const props = useProps("PickerControl", defaultProps6, _props);
  const {
    classNames,
    className,
    style,
    styles,
    unstyled,
    vars,
    firstInRange,
    lastInRange,
    inRange,
    __staticSelector,
    selected,
    disabled,
    ...others
  } = props;
  const getStyles = useStyles({
    name: __staticSelector || "PickerControl",
    classes: classes6,
    props,
    className,
    style,
    classNames,
    styles,
    unstyled,
    vars,
    varsResolver: varsResolver5,
    rootSelector: "pickerControl"
  });
  return (0, import_jsx_runtime17.jsx)(
    UnstyledButton,
    {
      ...getStyles("pickerControl"),
      ref,
      unstyled,
      "data-picker-control": true,
      "data-selected": selected && !disabled || void 0,
      "data-disabled": disabled || void 0,
      "data-in-range": inRange && !disabled && !selected || void 0,
      "data-first-in-range": firstInRange && !disabled || void 0,
      "data-last-in-range": lastInRange && !disabled || void 0,
      disabled,
      ...others
    }
  );
});
PickerControl.classes = classes6;
PickerControl.displayName = "@mantine/dates/PickerControl";

// node_modules/@mantine/dates/esm/components/YearsList/YearsList.mjs
var import_jsx_runtime18 = __toESM(require_jsx_runtime(), 1);
var import_dayjs21 = __toESM(require_dayjs_min(), 1);

// node_modules/@mantine/dates/esm/components/YearsList/get-year-in-tab-order/get-year-in-tab-order.mjs
var import_dayjs19 = __toESM(require_dayjs_min(), 1);

// node_modules/@mantine/dates/esm/components/YearsList/is-year-disabled/is-year-disabled.mjs
var import_dayjs18 = __toESM(require_dayjs_min(), 1);
function isYearDisabled({ year, minDate, maxDate }) {
  if (!minDate && !maxDate) {
    return false;
  }
  if (minDate && (0, import_dayjs18.default)(year).isBefore(minDate, "year")) {
    return true;
  }
  if (maxDate && (0, import_dayjs18.default)(year).isAfter(maxDate, "year")) {
    return true;
  }
  return false;
}

// node_modules/@mantine/dates/esm/components/YearsList/get-year-in-tab-order/get-year-in-tab-order.mjs
function getYearInTabOrder({
  years,
  minDate,
  maxDate,
  getYearControlProps
}) {
  const enabledYears = years.flat().filter(
    (year) => {
      var _a;
      return !isYearDisabled({ year, minDate, maxDate }) && !((_a = getYearControlProps == null ? void 0 : getYearControlProps(year)) == null ? void 0 : _a.disabled);
    }
  );
  const selectedYear = enabledYears.find((year) => {
    var _a;
    return (_a = getYearControlProps == null ? void 0 : getYearControlProps(year)) == null ? void 0 : _a.selected;
  });
  if (selectedYear) {
    return selectedYear;
  }
  const currentYear = enabledYears.find((year) => (0, import_dayjs19.default)().isSame(year, "year"));
  if (currentYear) {
    return currentYear;
  }
  return enabledYears[0];
}

// node_modules/@mantine/dates/esm/components/YearsList/get-years-data/get-years-data.mjs
var import_dayjs20 = __toESM(require_dayjs_min(), 1);
function getYearsData(decade) {
  const year = (0, import_dayjs20.default)(decade).year();
  const rounded = year - year % 10;
  let currentYearIndex = 0;
  const results = [[], [], [], []];
  for (let i = 0; i < 4; i += 1) {
    const max = i === 3 ? 1 : 3;
    for (let j = 0; j < max; j += 1) {
      results[i].push((0, import_dayjs20.default)(new Date(rounded + currentYearIndex, 0)).format("YYYY-MM-DD"));
      currentYearIndex += 1;
    }
  }
  return results;
}

// node_modules/@mantine/dates/esm/components/YearsList/YearsList.module.css.mjs
var classes7 = { "yearsList": "m_9206547b", "yearsListCell": "m_c5a19c7d" };

// node_modules/@mantine/dates/esm/components/YearsList/YearsList.mjs
var defaultProps7 = {
  yearsListFormat: "YYYY",
  withCellSpacing: true
};
var YearsList = factory((_props, ref) => {
  const props = useProps("YearsList", defaultProps7, _props);
  const {
    classNames,
    className,
    style,
    styles,
    unstyled,
    vars,
    decade,
    yearsListFormat,
    locale,
    minDate,
    maxDate,
    getYearControlProps,
    __staticSelector,
    __getControlRef,
    __onControlKeyDown,
    __onControlClick,
    __onControlMouseEnter,
    __preventFocus,
    __stopPropagation,
    withCellSpacing,
    size,
    ...others
  } = props;
  const getStyles = useStyles({
    name: __staticSelector || "YearsList",
    classes: classes7,
    props,
    className,
    style,
    classNames,
    styles,
    unstyled,
    vars,
    rootSelector: "yearsList"
  });
  const ctx = useDatesContext();
  const years = getYearsData(decade);
  const yearInTabOrder = getYearInTabOrder({
    years,
    minDate,
    maxDate,
    getYearControlProps
  });
  const rows = years.map((yearsRow, rowIndex) => {
    const cells = yearsRow.map((year, cellIndex) => {
      const controlProps = getYearControlProps == null ? void 0 : getYearControlProps(year);
      const isYearInTabOrder = (0, import_dayjs21.default)(year).isSame(yearInTabOrder, "year");
      return (0, import_jsx_runtime18.jsx)(
        "td",
        {
          ...getStyles("yearsListCell"),
          "data-with-spacing": withCellSpacing || void 0,
          children: (0, import_jsx_runtime18.jsx)(
            PickerControl,
            {
              ...getStyles("yearsListControl"),
              size,
              unstyled,
              "data-mantine-stop-propagation": __stopPropagation || void 0,
              disabled: isYearDisabled({ year, minDate, maxDate }),
              ref: (node) => __getControlRef == null ? void 0 : __getControlRef(rowIndex, cellIndex, node),
              ...controlProps,
              onKeyDown: (event) => {
                var _a;
                (_a = controlProps == null ? void 0 : controlProps.onKeyDown) == null ? void 0 : _a.call(controlProps, event);
                __onControlKeyDown == null ? void 0 : __onControlKeyDown(event, { rowIndex, cellIndex, date: year });
              },
              onClick: (event) => {
                var _a;
                (_a = controlProps == null ? void 0 : controlProps.onClick) == null ? void 0 : _a.call(controlProps, event);
                __onControlClick == null ? void 0 : __onControlClick(event, year);
              },
              onMouseEnter: (event) => {
                var _a;
                (_a = controlProps == null ? void 0 : controlProps.onMouseEnter) == null ? void 0 : _a.call(controlProps, event);
                __onControlMouseEnter == null ? void 0 : __onControlMouseEnter(event, year);
              },
              onMouseDown: (event) => {
                var _a;
                (_a = controlProps == null ? void 0 : controlProps.onMouseDown) == null ? void 0 : _a.call(controlProps, event);
                __preventFocus && event.preventDefault();
              },
              tabIndex: __preventFocus || !isYearInTabOrder ? -1 : 0,
              children: (0, import_dayjs21.default)(year).locale(ctx.getLocale(locale)).format(yearsListFormat)
            }
          )
        },
        cellIndex
      );
    });
    return (0, import_jsx_runtime18.jsx)("tr", { ...getStyles("yearsListRow"), children: cells }, rowIndex);
  });
  return (0, import_jsx_runtime18.jsx)(Box, { component: "table", ref, size, ...getStyles("yearsList"), ...others, children: (0, import_jsx_runtime18.jsx)("tbody", { children: rows }) });
});
YearsList.classes = classes7;
YearsList.displayName = "@mantine/dates/YearsList";

// node_modules/@mantine/dates/esm/components/MonthsList/MonthsList.mjs
var import_jsx_runtime19 = __toESM(require_jsx_runtime(), 1);
var import_dayjs25 = __toESM(require_dayjs_min(), 1);

// node_modules/@mantine/dates/esm/components/MonthsList/get-month-in-tab-order/get-month-in-tab-order.mjs
var import_dayjs23 = __toESM(require_dayjs_min(), 1);

// node_modules/@mantine/dates/esm/components/MonthsList/is-month-disabled/is-month-disabled.mjs
var import_dayjs22 = __toESM(require_dayjs_min(), 1);
function isMonthDisabled({ month, minDate, maxDate }) {
  if (!minDate && !maxDate) {
    return false;
  }
  if (minDate && (0, import_dayjs22.default)(month).isBefore(minDate, "month")) {
    return true;
  }
  if (maxDate && (0, import_dayjs22.default)(month).isAfter(maxDate, "month")) {
    return true;
  }
  return false;
}

// node_modules/@mantine/dates/esm/components/MonthsList/get-month-in-tab-order/get-month-in-tab-order.mjs
function getMonthInTabOrder({
  months,
  minDate,
  maxDate,
  getMonthControlProps
}) {
  const enabledMonths = months.flat().filter(
    (month) => {
      var _a;
      return !isMonthDisabled({ month, minDate, maxDate }) && !((_a = getMonthControlProps == null ? void 0 : getMonthControlProps(month)) == null ? void 0 : _a.disabled);
    }
  );
  const selectedMonth = enabledMonths.find((month) => {
    var _a;
    return (_a = getMonthControlProps == null ? void 0 : getMonthControlProps(month)) == null ? void 0 : _a.selected;
  });
  if (selectedMonth) {
    return selectedMonth;
  }
  const currentMonth = enabledMonths.find((month) => (0, import_dayjs23.default)().isSame(month, "month"));
  if (currentMonth) {
    return currentMonth;
  }
  return enabledMonths[0];
}

// node_modules/@mantine/dates/esm/components/MonthsList/get-months-data/get-months-data.mjs
var import_dayjs24 = __toESM(require_dayjs_min(), 1);
function getMonthsData(year) {
  const startOfYear = (0, import_dayjs24.default)(year).startOf("year").toDate();
  const results = [[], [], [], []];
  let currentMonthIndex = 0;
  for (let i = 0; i < 4; i += 1) {
    for (let j = 0; j < 3; j += 1) {
      results[i].push((0, import_dayjs24.default)(startOfYear).add(currentMonthIndex, "months").format("YYYY-MM-DD"));
      currentMonthIndex += 1;
    }
  }
  return results;
}

// node_modules/@mantine/dates/esm/components/MonthsList/MonthsList.module.css.mjs
var classes8 = { "monthsList": "m_2a6c32d", "monthsListCell": "m_fe27622f" };

// node_modules/@mantine/dates/esm/components/MonthsList/MonthsList.mjs
var defaultProps8 = {
  monthsListFormat: "MMM",
  withCellSpacing: true
};
var MonthsList = factory((_props, ref) => {
  const props = useProps("MonthsList", defaultProps8, _props);
  const {
    classNames,
    className,
    style,
    styles,
    unstyled,
    vars,
    __staticSelector,
    year,
    monthsListFormat,
    locale,
    minDate,
    maxDate,
    getMonthControlProps,
    __getControlRef,
    __onControlKeyDown,
    __onControlClick,
    __onControlMouseEnter,
    __preventFocus,
    __stopPropagation,
    withCellSpacing,
    size,
    ...others
  } = props;
  const getStyles = useStyles({
    name: __staticSelector || "MonthsList",
    classes: classes8,
    props,
    className,
    style,
    classNames,
    styles,
    unstyled,
    vars,
    rootSelector: "monthsList"
  });
  const ctx = useDatesContext();
  const months = getMonthsData(year);
  const monthInTabOrder = getMonthInTabOrder({
    months,
    minDate: toDateString(minDate),
    maxDate: toDateString(maxDate),
    getMonthControlProps
  });
  const rows = months.map((monthsRow, rowIndex) => {
    const cells = monthsRow.map((month, cellIndex) => {
      const controlProps = getMonthControlProps == null ? void 0 : getMonthControlProps(month);
      const isMonthInTabOrder = (0, import_dayjs25.default)(month).isSame(monthInTabOrder, "month");
      return (0, import_jsx_runtime19.jsx)(
        "td",
        {
          ...getStyles("monthsListCell"),
          "data-with-spacing": withCellSpacing || void 0,
          children: (0, import_jsx_runtime19.jsx)(
            PickerControl,
            {
              ...getStyles("monthsListControl"),
              size,
              unstyled,
              __staticSelector: __staticSelector || "MonthsList",
              "data-mantine-stop-propagation": __stopPropagation || void 0,
              disabled: isMonthDisabled({
                month,
                minDate: toDateString(minDate),
                maxDate: toDateString(maxDate)
              }),
              ref: (node) => __getControlRef == null ? void 0 : __getControlRef(rowIndex, cellIndex, node),
              ...controlProps,
              onKeyDown: (event) => {
                var _a;
                (_a = controlProps == null ? void 0 : controlProps.onKeyDown) == null ? void 0 : _a.call(controlProps, event);
                __onControlKeyDown == null ? void 0 : __onControlKeyDown(event, { rowIndex, cellIndex, date: month });
              },
              onClick: (event) => {
                var _a;
                (_a = controlProps == null ? void 0 : controlProps.onClick) == null ? void 0 : _a.call(controlProps, event);
                __onControlClick == null ? void 0 : __onControlClick(event, month);
              },
              onMouseEnter: (event) => {
                var _a;
                (_a = controlProps == null ? void 0 : controlProps.onMouseEnter) == null ? void 0 : _a.call(controlProps, event);
                __onControlMouseEnter == null ? void 0 : __onControlMouseEnter(event, month);
              },
              onMouseDown: (event) => {
                var _a;
                (_a = controlProps == null ? void 0 : controlProps.onMouseDown) == null ? void 0 : _a.call(controlProps, event);
                __preventFocus && event.preventDefault();
              },
              tabIndex: __preventFocus || !isMonthInTabOrder ? -1 : 0,
              children: (0, import_dayjs25.default)(month).locale(ctx.getLocale(locale)).format(monthsListFormat)
            }
          )
        },
        cellIndex
      );
    });
    return (0, import_jsx_runtime19.jsx)("tr", { ...getStyles("monthsListRow"), children: cells }, rowIndex);
  });
  return (0, import_jsx_runtime19.jsx)(Box, { component: "table", ref, size, ...getStyles("monthsList"), ...others, children: (0, import_jsx_runtime19.jsx)("tbody", { children: rows }) });
});
MonthsList.classes = classes8;
MonthsList.displayName = "@mantine/dates/MonthsList";

// node_modules/@mantine/dates/esm/components/CalendarHeader/CalendarHeader.mjs
var import_jsx_runtime20 = __toESM(require_jsx_runtime(), 1);

// node_modules/@mantine/dates/esm/components/CalendarHeader/CalendarHeader.module.css.mjs
var classes9 = { "calendarHeader": "m_730a79ed", "calendarHeaderLevel": "m_f6645d97", "calendarHeaderControl": "m_2351eeb0", "calendarHeaderControlIcon": "m_367dc749" };

// node_modules/@mantine/dates/esm/components/CalendarHeader/CalendarHeader.mjs
var defaultProps9 = {
  nextDisabled: false,
  previousDisabled: false,
  hasNextLevel: true,
  withNext: true,
  withPrevious: true
};
var varsResolver6 = createVarsResolver((_, { size }) => ({
  calendarHeader: {
    "--dch-control-size": getSize(size, "dch-control-size"),
    "--dch-fz": getFontSize(size)
  }
}));
var CalendarHeader = factory((_props, ref) => {
  const props = useProps("CalendarHeader", defaultProps9, _props);
  const {
    classNames,
    className,
    style,
    styles,
    unstyled,
    vars,
    nextIcon,
    previousIcon,
    nextLabel,
    previousLabel,
    onNext,
    onPrevious,
    onLevelClick,
    label,
    nextDisabled,
    previousDisabled,
    hasNextLevel,
    levelControlAriaLabel,
    withNext,
    withPrevious,
    __staticSelector,
    __preventFocus,
    __stopPropagation,
    ...others
  } = props;
  const getStyles = useStyles({
    name: __staticSelector || "CalendarHeader",
    classes: classes9,
    props,
    className,
    style,
    classNames,
    styles,
    unstyled,
    vars,
    varsResolver: varsResolver6,
    rootSelector: "calendarHeader"
  });
  const preventFocus = __preventFocus ? (event) => event.preventDefault() : void 0;
  return (0, import_jsx_runtime20.jsxs)(Box, { ...getStyles("calendarHeader"), ref, ...others, children: [
    withPrevious && (0, import_jsx_runtime20.jsx)(
      UnstyledButton,
      {
        ...getStyles("calendarHeaderControl"),
        "data-direction": "previous",
        "aria-label": previousLabel,
        onClick: onPrevious,
        unstyled,
        onMouseDown: preventFocus,
        disabled: previousDisabled,
        "data-disabled": previousDisabled || void 0,
        tabIndex: __preventFocus || previousDisabled ? -1 : 0,
        "data-mantine-stop-propagation": __stopPropagation || void 0,
        children: previousIcon || (0, import_jsx_runtime20.jsx)(
          AccordionChevron,
          {
            ...getStyles("calendarHeaderControlIcon"),
            "data-direction": "previous",
            size: "45%"
          }
        )
      }
    ),
    (0, import_jsx_runtime20.jsx)(
      UnstyledButton,
      {
        component: hasNextLevel ? "button" : "div",
        ...getStyles("calendarHeaderLevel"),
        onClick: hasNextLevel ? onLevelClick : void 0,
        unstyled,
        onMouseDown: hasNextLevel ? preventFocus : void 0,
        disabled: !hasNextLevel,
        "data-static": !hasNextLevel || void 0,
        "aria-label": levelControlAriaLabel,
        tabIndex: __preventFocus || !hasNextLevel ? -1 : 0,
        "data-mantine-stop-propagation": __stopPropagation || void 0,
        children: label
      }
    ),
    withNext && (0, import_jsx_runtime20.jsx)(
      UnstyledButton,
      {
        ...getStyles("calendarHeaderControl"),
        "data-direction": "next",
        "aria-label": nextLabel,
        onClick: onNext,
        unstyled,
        onMouseDown: preventFocus,
        disabled: nextDisabled,
        "data-disabled": nextDisabled || void 0,
        tabIndex: __preventFocus || nextDisabled ? -1 : 0,
        "data-mantine-stop-propagation": __stopPropagation || void 0,
        children: nextIcon || (0, import_jsx_runtime20.jsx)(
          AccordionChevron,
          {
            ...getStyles("calendarHeaderControlIcon"),
            "data-direction": "next",
            size: "45%"
          }
        )
      }
    )
  ] });
});
CalendarHeader.classes = classes9;
CalendarHeader.displayName = "@mantine/dates/CalendarHeader";

// node_modules/@mantine/dates/esm/components/DecadeLevel/DecadeLevel.mjs
var import_jsx_runtime21 = __toESM(require_jsx_runtime(), 1);
var import_dayjs26 = __toESM(require_dayjs_min(), 1);

// node_modules/@mantine/dates/esm/components/DecadeLevel/get-decade-range/get-decade-range.mjs
function getDecadeRange(decade) {
  const years = getYearsData(decade);
  return [years[0][0], years[3][0]];
}

// node_modules/@mantine/dates/esm/components/DecadeLevel/DecadeLevel.mjs
var defaultProps10 = {
  decadeLabelFormat: "YYYY"
};
var DecadeLevel = factory((_props, ref) => {
  const props = useProps("DecadeLevel", defaultProps10, _props);
  const {
    // YearsList settings
    decade,
    locale,
    minDate,
    maxDate,
    yearsListFormat,
    getYearControlProps,
    __getControlRef,
    __onControlKeyDown,
    __onControlClick,
    __onControlMouseEnter,
    withCellSpacing,
    // CalendarHeader settings
    __preventFocus,
    nextIcon,
    previousIcon,
    nextLabel,
    previousLabel,
    onNext,
    onPrevious,
    nextDisabled,
    previousDisabled,
    levelControlAriaLabel,
    withNext,
    withPrevious,
    // Other props
    decadeLabelFormat,
    classNames,
    styles,
    unstyled,
    __staticSelector,
    __stopPropagation,
    size,
    ...others
  } = props;
  const ctx = useDatesContext();
  const [startOfDecade, endOfDecade] = getDecadeRange(decade);
  const stylesApiProps = {
    __staticSelector: __staticSelector || "DecadeLevel",
    classNames,
    styles,
    unstyled,
    size
  };
  const _nextDisabled = typeof nextDisabled === "boolean" ? nextDisabled : maxDate ? !(0, import_dayjs26.default)(endOfDecade).endOf("year").isBefore(maxDate) : false;
  const _previousDisabled = typeof previousDisabled === "boolean" ? previousDisabled : minDate ? !(0, import_dayjs26.default)(startOfDecade).startOf("year").isAfter(minDate) : false;
  const formatDecade = (date, format) => (0, import_dayjs26.default)(date).locale(locale || ctx.locale).format(format);
  return (0, import_jsx_runtime21.jsxs)(Box, { "data-decade-level": true, size, ref, ...others, children: [
    (0, import_jsx_runtime21.jsx)(
      CalendarHeader,
      {
        label: typeof decadeLabelFormat === "function" ? decadeLabelFormat(startOfDecade, endOfDecade) : `${formatDecade(startOfDecade, decadeLabelFormat)} – ${formatDecade(
          endOfDecade,
          decadeLabelFormat
        )}`,
        __preventFocus,
        __stopPropagation,
        nextIcon,
        previousIcon,
        nextLabel,
        previousLabel,
        onNext,
        onPrevious,
        nextDisabled: _nextDisabled,
        previousDisabled: _previousDisabled,
        hasNextLevel: false,
        levelControlAriaLabel,
        withNext,
        withPrevious,
        ...stylesApiProps
      }
    ),
    (0, import_jsx_runtime21.jsx)(
      YearsList,
      {
        decade,
        locale,
        minDate,
        maxDate,
        yearsListFormat,
        getYearControlProps,
        __getControlRef,
        __onControlKeyDown,
        __onControlClick,
        __onControlMouseEnter,
        __preventFocus,
        __stopPropagation,
        withCellSpacing,
        ...stylesApiProps
      }
    )
  ] });
});
DecadeLevel.classes = { ...YearsList.classes, ...CalendarHeader.classes };
DecadeLevel.displayName = "@mantine/dates/DecadeLevel";

// node_modules/@mantine/dates/esm/components/YearLevel/YearLevel.mjs
var import_jsx_runtime22 = __toESM(require_jsx_runtime(), 1);
var import_dayjs27 = __toESM(require_dayjs_min(), 1);
var defaultProps11 = {
  yearLabelFormat: "YYYY"
};
var YearLevel = factory((_props, ref) => {
  const props = useProps("YearLevel", defaultProps11, _props);
  const {
    // MonthsList settings
    year,
    locale,
    minDate,
    maxDate,
    monthsListFormat,
    getMonthControlProps,
    __getControlRef,
    __onControlKeyDown,
    __onControlClick,
    __onControlMouseEnter,
    withCellSpacing,
    // CalendarHeader settings
    __preventFocus,
    nextIcon,
    previousIcon,
    nextLabel,
    previousLabel,
    onNext,
    onPrevious,
    onLevelClick,
    nextDisabled,
    previousDisabled,
    hasNextLevel,
    levelControlAriaLabel,
    withNext,
    withPrevious,
    // Other props
    yearLabelFormat,
    __staticSelector,
    __stopPropagation,
    size,
    classNames,
    styles,
    unstyled,
    ...others
  } = props;
  const ctx = useDatesContext();
  const stylesApiProps = {
    __staticSelector: __staticSelector || "YearLevel",
    classNames,
    styles,
    unstyled,
    size
  };
  const _nextDisabled = typeof nextDisabled === "boolean" ? nextDisabled : maxDate ? !(0, import_dayjs27.default)(year).endOf("year").isBefore(maxDate) : false;
  const _previousDisabled = typeof previousDisabled === "boolean" ? previousDisabled : minDate ? !(0, import_dayjs27.default)(year).startOf("year").isAfter(minDate) : false;
  return (0, import_jsx_runtime22.jsxs)(Box, { "data-year-level": true, size, ref, ...others, children: [
    (0, import_jsx_runtime22.jsx)(
      CalendarHeader,
      {
        label: typeof yearLabelFormat === "function" ? yearLabelFormat(year) : (0, import_dayjs27.default)(year).locale(locale || ctx.locale).format(yearLabelFormat),
        __preventFocus,
        __stopPropagation,
        nextIcon,
        previousIcon,
        nextLabel,
        previousLabel,
        onNext,
        onPrevious,
        onLevelClick,
        nextDisabled: _nextDisabled,
        previousDisabled: _previousDisabled,
        hasNextLevel,
        levelControlAriaLabel,
        withNext,
        withPrevious,
        ...stylesApiProps
      }
    ),
    (0, import_jsx_runtime22.jsx)(
      MonthsList,
      {
        year,
        locale,
        minDate,
        maxDate,
        monthsListFormat,
        getMonthControlProps,
        __getControlRef,
        __onControlKeyDown,
        __onControlClick,
        __onControlMouseEnter,
        __preventFocus,
        __stopPropagation,
        withCellSpacing,
        ...stylesApiProps
      }
    )
  ] });
});
YearLevel.classes = { ...CalendarHeader.classes, ...MonthsList.classes };
YearLevel.displayName = "@mantine/dates/YearLevel";

// node_modules/@mantine/dates/esm/components/MonthLevel/MonthLevel.mjs
var import_jsx_runtime23 = __toESM(require_jsx_runtime(), 1);
var import_dayjs28 = __toESM(require_dayjs_min(), 1);
var defaultProps12 = {
  monthLabelFormat: "MMMM YYYY"
};
var MonthLevel = factory((_props, ref) => {
  const props = useProps("MonthLevel", defaultProps12, _props);
  const {
    // Month settings
    month,
    locale,
    firstDayOfWeek,
    weekdayFormat,
    weekendDays,
    getDayProps,
    excludeDate,
    minDate,
    maxDate,
    renderDay,
    hideOutsideDates,
    hideWeekdays,
    getDayAriaLabel,
    __getDayRef,
    __onDayKeyDown,
    __onDayClick,
    __onDayMouseEnter,
    withCellSpacing,
    highlightToday,
    withWeekNumbers,
    // CalendarHeader settings
    __preventFocus,
    __stopPropagation,
    nextIcon,
    previousIcon,
    nextLabel,
    previousLabel,
    onNext,
    onPrevious,
    onLevelClick,
    nextDisabled,
    previousDisabled,
    hasNextLevel,
    levelControlAriaLabel,
    withNext,
    withPrevious,
    // Other props
    monthLabelFormat,
    classNames,
    styles,
    unstyled,
    __staticSelector,
    size,
    static: isStatic,
    ...others
  } = props;
  const ctx = useDatesContext();
  const stylesApiProps = {
    __staticSelector: __staticSelector || "MonthLevel",
    classNames,
    styles,
    unstyled,
    size
  };
  const _nextDisabled = typeof nextDisabled === "boolean" ? nextDisabled : maxDate ? !(0, import_dayjs28.default)(month).endOf("month").isBefore(maxDate) : false;
  const _previousDisabled = typeof previousDisabled === "boolean" ? previousDisabled : minDate ? !(0, import_dayjs28.default)(month).startOf("month").isAfter(minDate) : false;
  return (0, import_jsx_runtime23.jsxs)(Box, { "data-month-level": true, size, ref, ...others, children: [
    (0, import_jsx_runtime23.jsx)(
      CalendarHeader,
      {
        label: typeof monthLabelFormat === "function" ? monthLabelFormat(month) : (0, import_dayjs28.default)(month).locale(locale || ctx.locale).format(monthLabelFormat),
        __preventFocus,
        __stopPropagation,
        nextIcon,
        previousIcon,
        nextLabel,
        previousLabel,
        onNext,
        onPrevious,
        onLevelClick,
        nextDisabled: _nextDisabled,
        previousDisabled: _previousDisabled,
        hasNextLevel,
        levelControlAriaLabel,
        withNext,
        withPrevious,
        ...stylesApiProps
      }
    ),
    (0, import_jsx_runtime23.jsx)(
      Month,
      {
        month,
        locale,
        firstDayOfWeek,
        weekdayFormat,
        weekendDays,
        getDayProps,
        excludeDate,
        minDate,
        maxDate,
        renderDay,
        hideOutsideDates,
        hideWeekdays,
        getDayAriaLabel,
        __getDayRef,
        __onDayKeyDown,
        __onDayClick,
        __onDayMouseEnter,
        __preventFocus,
        __stopPropagation,
        static: isStatic,
        withCellSpacing,
        highlightToday,
        withWeekNumbers,
        ...stylesApiProps
      }
    )
  ] });
});
MonthLevel.classes = { ...Month.classes, ...CalendarHeader.classes };
MonthLevel.displayName = "@mantine/dates/MonthLevel";

// node_modules/@mantine/dates/esm/components/LevelsGroup/LevelsGroup.mjs
var import_jsx_runtime24 = __toESM(require_jsx_runtime(), 1);

// node_modules/@mantine/dates/esm/components/LevelsGroup/LevelsGroup.module.css.mjs
var classes10 = { "levelsGroup": "m_30b26e33" };

// node_modules/@mantine/dates/esm/components/LevelsGroup/LevelsGroup.mjs
var defaultProps13 = {};
var LevelsGroup = factory((_props, ref) => {
  const props = useProps("LevelsGroup", defaultProps13, _props);
  const { classNames, className, style, styles, unstyled, vars, __staticSelector, ...others } = props;
  const getStyles = useStyles({
    name: __staticSelector || "LevelsGroup",
    classes: classes10,
    props,
    className,
    style,
    classNames,
    styles,
    unstyled,
    vars,
    rootSelector: "levelsGroup"
  });
  return (0, import_jsx_runtime24.jsx)(Box, { ref, ...getStyles("levelsGroup"), ...others });
});
LevelsGroup.classes = classes10;
LevelsGroup.displayName = "@mantine/dates/LevelsGroup";

// node_modules/@mantine/dates/esm/components/DecadeLevelGroup/DecadeLevelGroup.mjs
var import_jsx_runtime25 = __toESM(require_jsx_runtime(), 1);
var import_dayjs29 = __toESM(require_dayjs_min(), 1);
var import_react8 = __toESM(require_react(), 1);
var defaultProps14 = {
  numberOfColumns: 1
};
var DecadeLevelGroup = factory((_props, ref) => {
  const props = useProps("DecadeLevelGroup", defaultProps14, _props);
  const {
    // DecadeLevel settings
    decade,
    locale,
    minDate,
    maxDate,
    yearsListFormat,
    getYearControlProps,
    __onControlClick,
    __onControlMouseEnter,
    withCellSpacing,
    // CalendarHeader settings
    __preventFocus,
    nextIcon,
    previousIcon,
    nextLabel,
    previousLabel,
    onNext,
    onPrevious,
    nextDisabled,
    previousDisabled,
    // Other settings
    classNames,
    styles,
    unstyled,
    __staticSelector,
    __stopPropagation,
    numberOfColumns,
    levelControlAriaLabel,
    decadeLabelFormat,
    size,
    vars,
    ...others
  } = props;
  const controlsRef = (0, import_react8.useRef)([]);
  const decades = Array(numberOfColumns).fill(0).map((_, decadeIndex) => {
    const currentDecade = (0, import_dayjs29.default)(decade).add(decadeIndex * 10, "years").format("YYYY-MM-DD");
    return (0, import_jsx_runtime25.jsx)(
      DecadeLevel,
      {
        size,
        yearsListFormat,
        decade: currentDecade,
        withNext: decadeIndex === numberOfColumns - 1,
        withPrevious: decadeIndex === 0,
        decadeLabelFormat,
        __onControlClick,
        __onControlMouseEnter,
        __onControlKeyDown: (event, payload) => handleControlKeyDown({
          levelIndex: decadeIndex,
          rowIndex: payload.rowIndex,
          cellIndex: payload.cellIndex,
          event,
          controlsRef
        }),
        __getControlRef: (rowIndex, cellIndex, node) => {
          if (!Array.isArray(controlsRef.current[decadeIndex])) {
            controlsRef.current[decadeIndex] = [];
          }
          if (!Array.isArray(controlsRef.current[decadeIndex][rowIndex])) {
            controlsRef.current[decadeIndex][rowIndex] = [];
          }
          controlsRef.current[decadeIndex][rowIndex][cellIndex] = node;
        },
        levelControlAriaLabel: typeof levelControlAriaLabel === "function" ? levelControlAriaLabel(currentDecade) : levelControlAriaLabel,
        locale,
        minDate,
        maxDate,
        __preventFocus,
        __stopPropagation,
        nextIcon,
        previousIcon,
        nextLabel,
        previousLabel,
        onNext,
        onPrevious,
        nextDisabled,
        previousDisabled,
        getYearControlProps,
        __staticSelector: __staticSelector || "DecadeLevelGroup",
        classNames,
        styles,
        unstyled,
        withCellSpacing
      },
      decadeIndex
    );
  });
  return (0, import_jsx_runtime25.jsx)(
    LevelsGroup,
    {
      classNames,
      styles,
      __staticSelector: __staticSelector || "DecadeLevelGroup",
      ref,
      size,
      unstyled,
      ...others,
      children: decades
    }
  );
});
DecadeLevelGroup.classes = { ...LevelsGroup.classes, ...DecadeLevel.classes };
DecadeLevelGroup.displayName = "@mantine/dates/DecadeLevelGroup";

// node_modules/@mantine/dates/esm/components/YearLevelGroup/YearLevelGroup.mjs
var import_jsx_runtime26 = __toESM(require_jsx_runtime(), 1);
var import_dayjs30 = __toESM(require_dayjs_min(), 1);
var import_react9 = __toESM(require_react(), 1);
var defaultProps15 = {
  numberOfColumns: 1
};
var YearLevelGroup = factory((_props, ref) => {
  const props = useProps("YearLevelGroup", defaultProps15, _props);
  const {
    // YearLevel settings
    year,
    locale,
    minDate,
    maxDate,
    monthsListFormat,
    getMonthControlProps,
    __onControlClick,
    __onControlMouseEnter,
    withCellSpacing,
    // CalendarHeader settings
    __preventFocus,
    nextIcon,
    previousIcon,
    nextLabel,
    previousLabel,
    onNext,
    onPrevious,
    onLevelClick,
    nextDisabled,
    previousDisabled,
    hasNextLevel,
    // Other settings
    classNames,
    styles,
    unstyled,
    __staticSelector,
    __stopPropagation,
    numberOfColumns,
    levelControlAriaLabel,
    yearLabelFormat,
    size,
    vars,
    ...others
  } = props;
  const controlsRef = (0, import_react9.useRef)([]);
  const years = Array(numberOfColumns).fill(0).map((_, yearIndex) => {
    const currentYear = (0, import_dayjs30.default)(year).add(yearIndex, "years").format("YYYY-MM-DD");
    return (0, import_jsx_runtime26.jsx)(
      YearLevel,
      {
        size,
        monthsListFormat,
        year: currentYear,
        withNext: yearIndex === numberOfColumns - 1,
        withPrevious: yearIndex === 0,
        yearLabelFormat,
        __stopPropagation,
        __onControlClick,
        __onControlMouseEnter,
        __onControlKeyDown: (event, payload) => handleControlKeyDown({
          levelIndex: yearIndex,
          rowIndex: payload.rowIndex,
          cellIndex: payload.cellIndex,
          event,
          controlsRef
        }),
        __getControlRef: (rowIndex, cellIndex, node) => {
          if (!Array.isArray(controlsRef.current[yearIndex])) {
            controlsRef.current[yearIndex] = [];
          }
          if (!Array.isArray(controlsRef.current[yearIndex][rowIndex])) {
            controlsRef.current[yearIndex][rowIndex] = [];
          }
          controlsRef.current[yearIndex][rowIndex][cellIndex] = node;
        },
        levelControlAriaLabel: typeof levelControlAriaLabel === "function" ? levelControlAriaLabel(currentYear) : levelControlAriaLabel,
        locale,
        minDate,
        maxDate,
        __preventFocus,
        nextIcon,
        previousIcon,
        nextLabel,
        previousLabel,
        onNext,
        onPrevious,
        onLevelClick,
        nextDisabled,
        previousDisabled,
        hasNextLevel,
        getMonthControlProps,
        classNames,
        styles,
        unstyled,
        __staticSelector: __staticSelector || "YearLevelGroup",
        withCellSpacing
      },
      yearIndex
    );
  });
  return (0, import_jsx_runtime26.jsx)(
    LevelsGroup,
    {
      classNames,
      styles,
      __staticSelector: __staticSelector || "YearLevelGroup",
      ref,
      size,
      unstyled,
      ...others,
      children: years
    }
  );
});
YearLevelGroup.classes = { ...YearLevel.classes, ...LevelsGroup.classes };
YearLevelGroup.displayName = "@mantine/dates/YearLevelGroup";

// node_modules/@mantine/dates/esm/components/MonthLevelGroup/MonthLevelGroup.mjs
var import_jsx_runtime27 = __toESM(require_jsx_runtime(), 1);
var import_dayjs31 = __toESM(require_dayjs_min(), 1);
var import_react10 = __toESM(require_react(), 1);
var defaultProps16 = {
  numberOfColumns: 1
};
var MonthLevelGroup = factory((_props, ref) => {
  const props = useProps("MonthLevelGroup", defaultProps16, _props);
  const {
    // Month settings
    month,
    locale,
    firstDayOfWeek,
    weekdayFormat,
    weekendDays,
    getDayProps,
    excludeDate,
    minDate,
    maxDate,
    renderDay,
    hideOutsideDates,
    hideWeekdays,
    getDayAriaLabel,
    __onDayClick,
    __onDayMouseEnter,
    withCellSpacing,
    highlightToday,
    withWeekNumbers,
    // CalendarHeader settings
    __preventFocus,
    nextIcon,
    previousIcon,
    nextLabel,
    previousLabel,
    onNext,
    onPrevious,
    onLevelClick,
    nextDisabled,
    previousDisabled,
    hasNextLevel,
    // Other settings
    classNames,
    styles,
    unstyled,
    numberOfColumns,
    levelControlAriaLabel,
    monthLabelFormat,
    __staticSelector,
    __stopPropagation,
    size,
    static: isStatic,
    vars,
    ...others
  } = props;
  const daysRefs = (0, import_react10.useRef)([]);
  const months = Array(numberOfColumns).fill(0).map((_, monthIndex) => {
    const currentMonth = (0, import_dayjs31.default)(month).add(monthIndex, "months").format("YYYY-MM-DD");
    return (0, import_jsx_runtime27.jsx)(
      MonthLevel,
      {
        month: currentMonth,
        withNext: monthIndex === numberOfColumns - 1,
        withPrevious: monthIndex === 0,
        monthLabelFormat,
        __stopPropagation,
        __onDayClick,
        __onDayMouseEnter,
        __onDayKeyDown: (event, payload) => handleControlKeyDown({
          levelIndex: monthIndex,
          rowIndex: payload.rowIndex,
          cellIndex: payload.cellIndex,
          event,
          controlsRef: daysRefs
        }),
        __getDayRef: (rowIndex, cellIndex, node) => {
          if (!Array.isArray(daysRefs.current[monthIndex])) {
            daysRefs.current[monthIndex] = [];
          }
          if (!Array.isArray(daysRefs.current[monthIndex][rowIndex])) {
            daysRefs.current[monthIndex][rowIndex] = [];
          }
          daysRefs.current[monthIndex][rowIndex][cellIndex] = node;
        },
        levelControlAriaLabel: typeof levelControlAriaLabel === "function" ? levelControlAriaLabel(currentMonth) : levelControlAriaLabel,
        locale,
        firstDayOfWeek,
        weekdayFormat,
        weekendDays,
        getDayProps,
        excludeDate,
        minDate,
        maxDate,
        renderDay,
        hideOutsideDates,
        hideWeekdays,
        getDayAriaLabel,
        __preventFocus,
        nextIcon,
        previousIcon,
        nextLabel,
        previousLabel,
        onNext,
        onPrevious,
        onLevelClick,
        nextDisabled,
        previousDisabled,
        hasNextLevel,
        classNames,
        styles,
        unstyled,
        __staticSelector: __staticSelector || "MonthLevelGroup",
        size,
        static: isStatic,
        withCellSpacing,
        highlightToday,
        withWeekNumbers
      },
      monthIndex
    );
  });
  return (0, import_jsx_runtime27.jsx)(
    LevelsGroup,
    {
      classNames,
      styles,
      __staticSelector: __staticSelector || "MonthLevelGroup",
      ref,
      size,
      ...others,
      children: months
    }
  );
});
MonthLevelGroup.classes = { ...LevelsGroup.classes, ...MonthLevel.classes };
MonthLevelGroup.displayName = "@mantine/dates/MonthLevelGroup";

// node_modules/@mantine/dates/esm/components/PickerInputBase/PickerInputBase.mjs
var import_jsx_runtime28 = __toESM(require_jsx_runtime(), 1);

// node_modules/@mantine/dates/esm/components/PickerInputBase/PickerInputBase.module.css.mjs
var classes11 = { "input": "m_6fa5e2aa" };

// node_modules/@mantine/dates/esm/components/PickerInputBase/PickerInputBase.mjs
var defaultProps17 = {};
var PickerInputBase = factory((_props, ref) => {
  const {
    inputProps,
    wrapperProps,
    placeholder,
    classNames,
    styles,
    unstyled,
    popoverProps,
    modalProps,
    dropdownType,
    children,
    formattedValue,
    dropdownHandlers,
    dropdownOpened,
    onClick,
    clearable,
    onClear,
    clearButtonProps,
    rightSection,
    shouldClear,
    readOnly,
    disabled,
    value,
    name,
    form,
    type,
    onDropdownClose,
    withTime,
    ...others
  } = useInputProps("PickerInputBase", defaultProps17, _props);
  const clearButton = (0, import_jsx_runtime28.jsx)(Input.ClearButton, { onClick: onClear, unstyled, ...clearButtonProps });
  const handleClose = () => {
    const isInvalidRangeValue = type === "range" && Array.isArray(value) && value[0] && !value[1];
    if (isInvalidRangeValue) {
      onClear();
    }
    dropdownHandlers.close();
    onDropdownClose == null ? void 0 : onDropdownClose();
  };
  return (0, import_jsx_runtime28.jsxs)(import_jsx_runtime28.Fragment, { children: [
    dropdownType === "modal" && !readOnly && (0, import_jsx_runtime28.jsx)(
      Modal,
      {
        opened: dropdownOpened,
        onClose: handleClose,
        withCloseButton: false,
        size: "auto",
        "data-dates-modal": true,
        unstyled,
        ...modalProps,
        children
      }
    ),
    (0, import_jsx_runtime28.jsx)(Input.Wrapper, { ...wrapperProps, children: (0, import_jsx_runtime28.jsxs)(
      Popover,
      {
        position: "bottom-start",
        opened: dropdownOpened,
        trapFocus: true,
        returnFocus: false,
        unstyled,
        ...popoverProps,
        disabled: (popoverProps == null ? void 0 : popoverProps.disabled) || dropdownType === "modal" || readOnly,
        onChange: (_opened) => {
          var _a;
          if (!_opened) {
            (_a = popoverProps == null ? void 0 : popoverProps.onClose) == null ? void 0 : _a.call(popoverProps);
            handleClose();
          }
        },
        children: [
          (0, import_jsx_runtime28.jsx)(Popover.Target, { children: (0, import_jsx_runtime28.jsx)(
            Input,
            {
              "data-dates-input": true,
              "data-read-only": readOnly || void 0,
              disabled,
              component: "button",
              type: "button",
              multiline: true,
              onClick: (event) => {
                onClick == null ? void 0 : onClick(event);
                dropdownHandlers.toggle();
              },
              __clearSection: clearButton,
              __clearable: clearable && shouldClear && !readOnly && !disabled,
              rightSection,
              ...inputProps,
              ref,
              classNames: { ...classNames, input: clsx_default(classes11.input, classNames == null ? void 0 : classNames.input) },
              ...others,
              children: formattedValue || (0, import_jsx_runtime28.jsx)(
                Input.Placeholder,
                {
                  error: inputProps.error,
                  unstyled,
                  className: classNames == null ? void 0 : classNames.placeholder,
                  style: styles == null ? void 0 : styles.placeholder,
                  children: placeholder
                }
              )
            }
          ) }),
          (0, import_jsx_runtime28.jsx)(Popover.Dropdown, { "data-dates-dropdown": true, children })
        ]
      }
    ) }),
    (0, import_jsx_runtime28.jsx)(HiddenDatesInput, { value, name, form, type, withTime })
  ] });
});
PickerInputBase.classes = classes11;
PickerInputBase.displayName = "@mantine/dates/PickerInputBase";

// node_modules/@mantine/dates/esm/components/Calendar/Calendar.mjs
var import_jsx_runtime29 = __toESM(require_jsx_runtime(), 1);
var import_dayjs33 = __toESM(require_dayjs_min(), 1);
var import_react12 = __toESM(require_react(), 1);

// node_modules/@mantine/dates/esm/hooks/use-uncontrolled-dates/use-uncontrolled-dates.mjs
var import_react11 = __toESM(require_react(), 1);
var import_dayjs32 = __toESM(require_dayjs_min(), 1);
var getEmptyValue = (type) => type === "range" ? [null, null] : type === "multiple" ? [] : null;
var convertDatesValue = (value, withTime) => {
  const converter = withTime ? toDateTimeString : toDateString;
  return Array.isArray(value) ? value.map(converter) : converter(value);
};
function useUncontrolledDates({
  type,
  value,
  defaultValue,
  onChange,
  withTime = false
}) {
  const storedType = (0, import_react11.useRef)(type);
  const [_value, _setValue, controlled] = useUncontrolled({
    value: convertDatesValue(value, withTime),
    defaultValue: convertDatesValue(defaultValue, withTime),
    finalValue: getEmptyValue(type),
    onChange
  });
  let _finalValue = _value;
  if (storedType.current !== type) {
    storedType.current = type;
    if (value === void 0) {
      _finalValue = defaultValue !== void 0 ? defaultValue : getEmptyValue(type);
      _setValue(_finalValue);
    }
  }
  return [_finalValue, _setValue, controlled];
}

// node_modules/@mantine/dates/esm/components/Calendar/clamp-level/clamp-level.mjs
function levelToNumber(level, fallback) {
  if (!level) {
    return fallback || 0;
  }
  return level === "month" ? 0 : level === "year" ? 1 : 2;
}
function levelNumberToLevel(levelNumber) {
  return levelNumber === 0 ? "month" : levelNumber === 1 ? "year" : "decade";
}
function clampLevel(level, minLevel, maxLevel) {
  return levelNumberToLevel(
    clamp(
      levelToNumber(level, 0),
      levelToNumber(minLevel, 0),
      levelToNumber(maxLevel, 2)
    )
  );
}

// node_modules/@mantine/dates/esm/components/Calendar/Calendar.mjs
var defaultProps18 = {
  maxLevel: "decade",
  minLevel: "month",
  __updateDateOnYearSelect: true,
  __updateDateOnMonthSelect: true
};
var Calendar = factory((_props, ref) => {
  const props = useProps("Calendar", defaultProps18, _props);
  const {
    // CalendarLevel props
    vars,
    maxLevel,
    minLevel,
    defaultLevel,
    level,
    onLevelChange,
    date,
    defaultDate,
    onDateChange,
    numberOfColumns,
    columnsToScroll,
    ariaLabels,
    nextLabel,
    previousLabel,
    onYearSelect,
    onMonthSelect,
    onYearMouseEnter,
    onMonthMouseEnter,
    __updateDateOnYearSelect,
    __updateDateOnMonthSelect,
    // MonthLevelGroup props
    firstDayOfWeek,
    weekdayFormat,
    weekendDays,
    getDayProps,
    excludeDate,
    renderDay,
    hideOutsideDates,
    hideWeekdays,
    getDayAriaLabel,
    monthLabelFormat,
    nextIcon,
    previousIcon,
    __onDayClick,
    __onDayMouseEnter,
    withCellSpacing,
    highlightToday,
    withWeekNumbers,
    // YearLevelGroup props
    monthsListFormat,
    getMonthControlProps,
    yearLabelFormat,
    // DecadeLevelGroup props
    yearsListFormat,
    getYearControlProps,
    decadeLabelFormat,
    // Other props
    classNames,
    styles,
    unstyled,
    minDate,
    maxDate,
    locale,
    __staticSelector,
    size,
    __preventFocus,
    __stopPropagation,
    onNextDecade,
    onPreviousDecade,
    onNextYear,
    onPreviousYear,
    onNextMonth,
    onPreviousMonth,
    static: isStatic,
    ...others
  } = props;
  const { resolvedClassNames, resolvedStyles } = useResolvedStylesApi({
    classNames,
    styles,
    props
  });
  const [_level, setLevel] = useUncontrolled({
    value: level ? clampLevel(level, minLevel, maxLevel) : void 0,
    defaultValue: defaultLevel ? clampLevel(defaultLevel, minLevel, maxLevel) : void 0,
    finalValue: clampLevel(void 0, minLevel, maxLevel),
    onChange: onLevelChange
  });
  const [_date, setDate] = useUncontrolledDates({
    type: "default",
    value: toDateString(date),
    defaultValue: toDateString(defaultDate),
    onChange: onDateChange
  });
  const stylesApiProps = {
    __staticSelector: __staticSelector || "Calendar",
    styles: resolvedStyles,
    classNames: resolvedClassNames,
    unstyled,
    size
  };
  const _columnsToScroll = columnsToScroll || numberOfColumns || 1;
  const now = /* @__PURE__ */ new Date();
  const fallbackDate = minDate && (0, import_dayjs33.default)(now).isAfter(minDate) ? minDate : (0, import_dayjs33.default)(now).format("YYYY-MM-DD");
  const currentDate = _date || fallbackDate;
  const handleNextMonth = () => {
    const nextDate = (0, import_dayjs33.default)(currentDate).add(_columnsToScroll, "month").format("YYYY-MM-DD");
    onNextMonth == null ? void 0 : onNextMonth(nextDate);
    setDate(nextDate);
  };
  const handlePreviousMonth = () => {
    const nextDate = (0, import_dayjs33.default)(currentDate).subtract(_columnsToScroll, "month").format("YYYY-MM-DD");
    onPreviousMonth == null ? void 0 : onPreviousMonth(nextDate);
    setDate(nextDate);
  };
  const handleNextYear = () => {
    const nextDate = (0, import_dayjs33.default)(currentDate).add(_columnsToScroll, "year").format("YYYY-MM-DD");
    onNextYear == null ? void 0 : onNextYear(nextDate);
    setDate(nextDate);
  };
  const handlePreviousYear = () => {
    const nextDate = (0, import_dayjs33.default)(currentDate).subtract(_columnsToScroll, "year").format("YYYY-MM-DD");
    onPreviousYear == null ? void 0 : onPreviousYear(nextDate);
    setDate(nextDate);
  };
  const handleNextDecade = () => {
    const nextDate = (0, import_dayjs33.default)(currentDate).add(10 * _columnsToScroll, "year").format("YYYY-MM-DD");
    onNextDecade == null ? void 0 : onNextDecade(nextDate);
    setDate(nextDate);
  };
  const handlePreviousDecade = () => {
    const nextDate = (0, import_dayjs33.default)(currentDate).subtract(10 * _columnsToScroll, "year").format("YYYY-MM-DD");
    onPreviousDecade == null ? void 0 : onPreviousDecade(nextDate);
    setDate(nextDate);
  };
  return (0, import_jsx_runtime29.jsxs)(Box, { ref, size, "data-calendar": true, ...others, children: [
    _level === "month" && (0, import_jsx_runtime29.jsx)(
      MonthLevelGroup,
      {
        month: currentDate,
        minDate,
        maxDate,
        firstDayOfWeek,
        weekdayFormat,
        weekendDays,
        getDayProps,
        excludeDate,
        renderDay,
        hideOutsideDates,
        hideWeekdays,
        getDayAriaLabel,
        onNext: handleNextMonth,
        onPrevious: handlePreviousMonth,
        hasNextLevel: maxLevel !== "month",
        onLevelClick: () => setLevel("year"),
        numberOfColumns,
        locale,
        levelControlAriaLabel: ariaLabels == null ? void 0 : ariaLabels.monthLevelControl,
        nextLabel: (ariaLabels == null ? void 0 : ariaLabels.nextMonth) ?? nextLabel,
        nextIcon,
        previousLabel: (ariaLabels == null ? void 0 : ariaLabels.previousMonth) ?? previousLabel,
        previousIcon,
        monthLabelFormat,
        __onDayClick,
        __onDayMouseEnter,
        __preventFocus,
        __stopPropagation,
        static: isStatic,
        withCellSpacing,
        highlightToday,
        withWeekNumbers,
        ...stylesApiProps
      }
    ),
    _level === "year" && (0, import_jsx_runtime29.jsx)(
      YearLevelGroup,
      {
        year: currentDate,
        numberOfColumns,
        minDate,
        maxDate,
        monthsListFormat,
        getMonthControlProps,
        locale,
        onNext: handleNextYear,
        onPrevious: handlePreviousYear,
        hasNextLevel: maxLevel !== "month" && maxLevel !== "year",
        onLevelClick: () => setLevel("decade"),
        levelControlAriaLabel: ariaLabels == null ? void 0 : ariaLabels.yearLevelControl,
        nextLabel: (ariaLabels == null ? void 0 : ariaLabels.nextYear) ?? nextLabel,
        nextIcon,
        previousLabel: (ariaLabels == null ? void 0 : ariaLabels.previousYear) ?? previousLabel,
        previousIcon,
        yearLabelFormat,
        __onControlMouseEnter: onMonthMouseEnter,
        __onControlClick: (_event, payload) => {
          __updateDateOnMonthSelect && setDate(payload);
          setLevel(clampLevel("month", minLevel, maxLevel));
          onMonthSelect == null ? void 0 : onMonthSelect(payload);
        },
        __preventFocus,
        __stopPropagation,
        withCellSpacing,
        ...stylesApiProps
      }
    ),
    _level === "decade" && (0, import_jsx_runtime29.jsx)(
      DecadeLevelGroup,
      {
        decade: currentDate,
        minDate,
        maxDate,
        yearsListFormat,
        getYearControlProps,
        locale,
        onNext: handleNextDecade,
        onPrevious: handlePreviousDecade,
        numberOfColumns,
        nextLabel: (ariaLabels == null ? void 0 : ariaLabels.nextDecade) ?? nextLabel,
        nextIcon,
        previousLabel: (ariaLabels == null ? void 0 : ariaLabels.previousDecade) ?? previousLabel,
        previousIcon,
        decadeLabelFormat,
        __onControlMouseEnter: onYearMouseEnter,
        __onControlClick: (_event, payload) => {
          __updateDateOnYearSelect && setDate(payload);
          setLevel(clampLevel("year", minLevel, maxLevel));
          onYearSelect == null ? void 0 : onYearSelect(payload);
        },
        __preventFocus,
        __stopPropagation,
        withCellSpacing,
        ...stylesApiProps
      }
    )
  ] });
});
Calendar.classes = {
  ...DecadeLevelGroup.classes,
  ...YearLevelGroup.classes,
  ...MonthLevelGroup.classes
};
Calendar.displayName = "@mantine/dates/Calendar";

// node_modules/@mantine/dates/esm/components/Calendar/pick-calendar-levels-props/pick-calendar-levels-props.mjs
function pickCalendarProps(props) {
  const {
    maxLevel,
    minLevel,
    defaultLevel,
    level,
    onLevelChange,
    nextIcon,
    previousIcon,
    date,
    defaultDate,
    onDateChange,
    numberOfColumns,
    columnsToScroll,
    ariaLabels,
    nextLabel,
    previousLabel,
    onYearSelect,
    onMonthSelect,
    onYearMouseEnter,
    onMonthMouseEnter,
    onNextMonth,
    onPreviousMonth,
    onNextYear,
    onPreviousYear,
    onNextDecade,
    onPreviousDecade,
    withCellSpacing,
    highlightToday,
    __updateDateOnYearSelect,
    __updateDateOnMonthSelect,
    withWeekNumbers,
    // MonthLevelGroup props
    firstDayOfWeek,
    weekdayFormat,
    weekendDays,
    getDayProps,
    excludeDate,
    renderDay,
    hideOutsideDates,
    hideWeekdays,
    getDayAriaLabel,
    monthLabelFormat,
    // YearLevelGroup props
    monthsListFormat,
    getMonthControlProps,
    yearLabelFormat,
    // DecadeLevelGroup props
    yearsListFormat,
    getYearControlProps,
    decadeLabelFormat,
    // External picker props
    allowSingleDateInRange,
    allowDeselect,
    // Other props
    minDate,
    maxDate,
    locale,
    ...others
  } = props;
  return {
    calendarProps: {
      maxLevel,
      minLevel,
      defaultLevel,
      level,
      onLevelChange,
      nextIcon,
      previousIcon,
      date,
      defaultDate,
      onDateChange,
      numberOfColumns,
      columnsToScroll,
      ariaLabels,
      nextLabel,
      previousLabel,
      onYearSelect,
      onMonthSelect,
      onYearMouseEnter,
      onMonthMouseEnter,
      onNextMonth,
      onPreviousMonth,
      onNextYear,
      onPreviousYear,
      onNextDecade,
      onPreviousDecade,
      withCellSpacing,
      highlightToday,
      __updateDateOnYearSelect,
      __updateDateOnMonthSelect,
      withWeekNumbers,
      // MonthLevelGroup props
      firstDayOfWeek,
      weekdayFormat,
      weekendDays,
      getDayProps,
      excludeDate,
      renderDay,
      hideOutsideDates,
      hideWeekdays,
      getDayAriaLabel,
      monthLabelFormat,
      // YearLevelGroup props
      monthsListFormat,
      getMonthControlProps,
      yearLabelFormat,
      // DecadeLevelGroup props
      yearsListFormat,
      getYearControlProps,
      decadeLabelFormat,
      // External picker props
      allowSingleDateInRange,
      allowDeselect,
      // Other props
      minDate,
      maxDate,
      locale
    },
    others
  };
}

// node_modules/@mantine/dates/esm/components/YearPicker/YearPicker.mjs
var import_jsx_runtime30 = __toESM(require_jsx_runtime(), 1);

// node_modules/@mantine/dates/esm/hooks/use-dates-state/use-dates-state.mjs
var import_dayjs35 = __toESM(require_dayjs_min(), 1);
var import_react13 = __toESM(require_react(), 1);

// node_modules/@mantine/dates/esm/hooks/use-dates-state/is-in-range/is-in-range.mjs
var import_dayjs34 = __toESM(require_dayjs_min(), 1);
function isInRange(date, range) {
  const _range = [...range].sort((a, b) => (0, import_dayjs34.default)(a).isAfter((0, import_dayjs34.default)(b)) ? 1 : -1);
  return (0, import_dayjs34.default)(_range[0]).startOf("day").subtract(1, "ms").isBefore(date) && (0, import_dayjs34.default)(_range[1]).endOf("day").add(1, "ms").isAfter(date);
}

// node_modules/@mantine/dates/esm/hooks/use-dates-state/use-dates-state.mjs
function useDatesState({
  type,
  level,
  value,
  defaultValue,
  onChange,
  allowSingleDateInRange,
  allowDeselect,
  onMouseLeave
}) {
  const [_value, setValue] = useUncontrolledDates({
    type,
    value,
    defaultValue,
    onChange
  });
  const [pickedDate, setPickedDate] = (0, import_react13.useState)(
    type === "range" ? _value[0] && !_value[1] ? _value[0] : null : null
  );
  const [hoveredDate, setHoveredDate] = (0, import_react13.useState)(null);
  const onDateChange = (date) => {
    if (type === "range") {
      if (pickedDate && !_value[1]) {
        if ((0, import_dayjs35.default)(date).isSame(pickedDate, level) && !allowSingleDateInRange) {
          setPickedDate(null);
          setHoveredDate(null);
          setValue([null, null]);
          return;
        }
        const result = [date, pickedDate];
        result.sort((a, b) => (0, import_dayjs35.default)(a).isAfter((0, import_dayjs35.default)(b)) ? 1 : -1);
        setValue(result);
        setHoveredDate(null);
        setPickedDate(null);
        return;
      }
      if (_value[0] && !_value[1] && (0, import_dayjs35.default)(date).isSame(_value[0], level) && !allowSingleDateInRange) {
        setPickedDate(null);
        setHoveredDate(null);
        setValue([null, null]);
        return;
      }
      setValue([date, null]);
      setHoveredDate(null);
      setPickedDate(date);
      return;
    }
    if (type === "multiple") {
      if (_value.some((selected) => (0, import_dayjs35.default)(selected).isSame(date, level))) {
        setValue(_value.filter((selected) => !(0, import_dayjs35.default)(selected).isSame(date, level)));
      } else {
        setValue([..._value, date]);
      }
      return;
    }
    if (_value && allowDeselect && (0, import_dayjs35.default)(date).isSame(_value, level)) {
      setValue(null);
    } else {
      setValue(date);
    }
  };
  const isDateInRange = (date) => {
    if (pickedDate && hoveredDate) {
      return isInRange(date, [hoveredDate, pickedDate]);
    }
    if (_value[0] && _value[1]) {
      return isInRange(date, _value);
    }
    return false;
  };
  const onRootMouseLeave = type === "range" ? (event) => {
    onMouseLeave == null ? void 0 : onMouseLeave(event);
    setHoveredDate(null);
  } : onMouseLeave;
  const isFirstInRange = (date) => {
    if (!_value[0]) {
      return false;
    }
    if ((0, import_dayjs35.default)(date).isSame(_value[0], level)) {
      return !(hoveredDate && (0, import_dayjs35.default)(hoveredDate).isBefore(_value[0]));
    }
    return false;
  };
  const isLastInRange = (date) => {
    if (_value[1]) {
      return (0, import_dayjs35.default)(date).isSame(_value[1], level);
    }
    if (!_value[0] || !hoveredDate) {
      return false;
    }
    return (0, import_dayjs35.default)(hoveredDate).isBefore(_value[0]) && (0, import_dayjs35.default)(date).isSame(_value[0], level);
  };
  const getControlProps = (date) => {
    if (type === "range") {
      return {
        selected: _value.some(
          (selection) => selection && (0, import_dayjs35.default)(selection).isSame(date, level)
        ),
        inRange: isDateInRange(date),
        firstInRange: isFirstInRange(date),
        lastInRange: isLastInRange(date),
        "data-autofocus": !!_value[0] && (0, import_dayjs35.default)(_value[0]).isSame(date, level) || void 0
      };
    }
    if (type === "multiple") {
      return {
        selected: _value.some(
          (selection) => selection && (0, import_dayjs35.default)(selection).isSame(date, level)
        ),
        "data-autofocus": !!_value[0] && (0, import_dayjs35.default)(_value[0]).isSame(date, level) || void 0
      };
    }
    const selected = (0, import_dayjs35.default)(_value).isSame(date, level);
    return { selected, "data-autofocus": selected || void 0 };
  };
  const onHoveredDateChange = type === "range" && pickedDate ? setHoveredDate : () => {
  };
  (0, import_react13.useEffect)(() => {
    if (type !== "range") {
      return;
    }
    if (_value[0] && !_value[1]) {
      setPickedDate(_value[0]);
    } else {
      const isNeitherSelected = _value[0] == null && _value[1] == null;
      const isBothSelected = _value[0] != null && _value[1] != null;
      if (isNeitherSelected || isBothSelected) {
        setPickedDate(null);
        setHoveredDate(null);
      }
    }
  }, [_value]);
  return {
    onDateChange,
    onRootMouseLeave,
    onHoveredDateChange,
    getControlProps,
    _value,
    setValue
  };
}

// node_modules/@mantine/dates/esm/components/YearPicker/YearPicker.mjs
var import_dayjs36 = __toESM(require_dayjs_min(), 1);
var import_react14 = __toESM(require_react(), 1);
var defaultProps19 = {
  type: "default"
};
var YearPicker = factory((_props, ref) => {
  const props = useProps("YearPicker", defaultProps19, _props);
  const {
    classNames,
    styles,
    vars,
    type,
    defaultValue,
    value,
    onChange,
    __staticSelector,
    getYearControlProps,
    allowSingleDateInRange,
    allowDeselect,
    onMouseLeave,
    onYearSelect,
    __updateDateOnYearSelect,
    ...others
  } = props;
  const { onDateChange, onRootMouseLeave, onHoveredDateChange, getControlProps } = useDatesState({
    type,
    level: "year",
    allowDeselect,
    allowSingleDateInRange,
    value,
    defaultValue,
    onChange,
    onMouseLeave
  });
  const { resolvedClassNames, resolvedStyles } = useResolvedStylesApi({
    classNames,
    styles,
    props
  });
  return (0, import_jsx_runtime30.jsx)(
    Calendar,
    {
      ref,
      minLevel: "decade",
      __updateDateOnYearSelect: __updateDateOnYearSelect ?? false,
      __staticSelector: __staticSelector || "YearPicker",
      onMouseLeave: onRootMouseLeave,
      onYearMouseEnter: (_event, date) => onHoveredDateChange(date),
      onYearSelect: (date) => {
        onDateChange(date);
        onYearSelect == null ? void 0 : onYearSelect(date);
      },
      getYearControlProps: (date) => ({
        ...getControlProps(date),
        ...getYearControlProps == null ? void 0 : getYearControlProps(date)
      }),
      classNames: resolvedClassNames,
      styles: resolvedStyles,
      ...others
    }
  );
});
YearPicker.classes = Calendar.classes;
YearPicker.displayName = "@mantine/dates/YearPicker";

// node_modules/@mantine/dates/esm/components/MonthPicker/MonthPicker.mjs
var import_jsx_runtime31 = __toESM(require_jsx_runtime(), 1);
var import_dayjs37 = __toESM(require_dayjs_min(), 1);
var import_react15 = __toESM(require_react(), 1);
var defaultProps20 = {
  type: "default"
};
var MonthPicker = factory((_props, ref) => {
  const props = useProps("MonthPicker", defaultProps20, _props);
  const {
    classNames,
    styles,
    vars,
    type,
    defaultValue,
    value,
    onChange,
    __staticSelector,
    getMonthControlProps,
    allowSingleDateInRange,
    allowDeselect,
    onMouseLeave,
    onMonthSelect,
    __updateDateOnMonthSelect,
    onLevelChange,
    ...others
  } = props;
  const { onDateChange, onRootMouseLeave, onHoveredDateChange, getControlProps } = useDatesState({
    type,
    level: "month",
    allowDeselect,
    allowSingleDateInRange,
    value,
    defaultValue,
    onChange,
    onMouseLeave
  });
  const { resolvedClassNames, resolvedStyles } = useResolvedStylesApi({
    classNames,
    styles,
    props
  });
  return (0, import_jsx_runtime31.jsx)(
    Calendar,
    {
      ref,
      minLevel: "year",
      __updateDateOnMonthSelect: __updateDateOnMonthSelect ?? false,
      __staticSelector: __staticSelector || "MonthPicker",
      onMouseLeave: onRootMouseLeave,
      onMonthMouseEnter: (_event, date) => onHoveredDateChange(date),
      onMonthSelect: (date) => {
        onDateChange(date);
        onMonthSelect == null ? void 0 : onMonthSelect(date);
      },
      getMonthControlProps: (date) => ({
        ...getControlProps(date),
        ...getMonthControlProps == null ? void 0 : getMonthControlProps(date)
      }),
      classNames: resolvedClassNames,
      styles: resolvedStyles,
      onLevelChange,
      ...others
    }
  );
});
MonthPicker.classes = Calendar.classes;
MonthPicker.displayName = "@mantine/dates/MonthPicker";

// node_modules/@mantine/dates/esm/components/DatePicker/DatePicker.mjs
var import_jsx_runtime32 = __toESM(require_jsx_runtime(), 1);
var import_dayjs38 = __toESM(require_dayjs_min(), 1);
var import_react16 = __toESM(require_react(), 1);
var defaultProps21 = {
  type: "default",
  defaultLevel: "month",
  numberOfColumns: 1
};
var DatePicker = factory((_props, ref) => {
  const props = useProps("DatePicker", defaultProps21, _props);
  const {
    classNames,
    styles,
    vars,
    type,
    defaultValue,
    value,
    onChange,
    __staticSelector,
    getDayProps,
    allowSingleDateInRange,
    allowDeselect,
    onMouseLeave,
    numberOfColumns,
    hideOutsideDates,
    __onDayMouseEnter,
    __onDayClick,
    ...others
  } = props;
  const { onDateChange, onRootMouseLeave, onHoveredDateChange, getControlProps } = useDatesState({
    type,
    level: "day",
    allowDeselect,
    allowSingleDateInRange,
    value,
    defaultValue,
    onChange,
    onMouseLeave
  });
  const { resolvedClassNames, resolvedStyles } = useResolvedStylesApi({
    classNames,
    styles,
    props
  });
  return (0, import_jsx_runtime32.jsx)(
    Calendar,
    {
      ref,
      minLevel: "month",
      classNames: resolvedClassNames,
      styles: resolvedStyles,
      __staticSelector: __staticSelector || "DatePicker",
      onMouseLeave: onRootMouseLeave,
      numberOfColumns,
      hideOutsideDates: hideOutsideDates ?? numberOfColumns !== 1,
      __onDayMouseEnter: (_event, date) => {
        onHoveredDateChange(date);
        __onDayMouseEnter == null ? void 0 : __onDayMouseEnter(_event, date);
      },
      __onDayClick: (_event, date) => {
        onDateChange(date);
        __onDayClick == null ? void 0 : __onDayClick(_event, date);
      },
      getDayProps: (date) => ({
        ...getControlProps(date),
        ...getDayProps == null ? void 0 : getDayProps(date)
      }),
      ...others
    }
  );
});
DatePicker.classes = Calendar.classes;
DatePicker.displayName = "@mantine/dates/DatePicker";

// node_modules/@mantine/dates/esm/components/DateInput/DateInput.mjs
var import_jsx_runtime33 = __toESM(require_jsx_runtime(), 1);
var import_dayjs41 = __toESM(require_dayjs_min(), 1);
var import_react17 = __toESM(require_react(), 1);

// node_modules/@mantine/dates/esm/components/DateInput/date-string-parser/date-string-parser.mjs
var import_dayjs39 = __toESM(require_dayjs_min(), 1);
function dateStringParser(dateString) {
  if (dateString === null) {
    return null;
  }
  const date = new Date(dateString);
  if (Number.isNaN(date.getTime()) || !dateString) {
    return null;
  }
  return (0, import_dayjs39.default)(date).format("YYYY-MM-DD");
}

// node_modules/@mantine/dates/esm/components/DateInput/is-date-valid/is-date-valid.mjs
var import_dayjs40 = __toESM(require_dayjs_min(), 1);
function isDateValid({ date, maxDate, minDate }) {
  if (date == null) {
    return false;
  }
  if (Number.isNaN(new Date(date).getTime())) {
    return false;
  }
  if (maxDate && (0, import_dayjs40.default)(date).isAfter(maxDate, "date")) {
    return false;
  }
  if (minDate && (0, import_dayjs40.default)(date).isBefore(minDate, "date")) {
    return false;
  }
  return true;
}

// node_modules/@mantine/dates/esm/components/DateInput/DateInput.mjs
var defaultProps22 = {
  valueFormat: "MMMM D, YYYY",
  fixOnBlur: true
};
var DateInput = factory((_props, ref) => {
  const props = useInputProps("DateInput", defaultProps22, _props);
  const {
    inputProps,
    wrapperProps,
    value,
    defaultValue,
    onChange,
    clearable,
    clearButtonProps,
    popoverProps,
    getDayProps,
    locale,
    valueFormat,
    dateParser,
    minDate,
    maxDate,
    fixOnBlur,
    onFocus,
    onBlur,
    onClick,
    readOnly,
    name,
    form,
    rightSection,
    unstyled,
    classNames,
    styles,
    allowDeselect,
    date,
    defaultDate,
    onDateChange,
    ...rest
  } = props;
  const _wrapperRef = (0, import_react17.useRef)(null);
  const _dropdownRef = (0, import_react17.useRef)(null);
  const [dropdownOpened, setDropdownOpened] = (0, import_react17.useState)(false);
  const { calendarProps, others } = pickCalendarProps(rest);
  const ctx = useDatesContext();
  const defaultDateParser = (val) => {
    const parsedDate = (0, import_dayjs41.default)(val, valueFormat, ctx.getLocale(locale)).toDate();
    return Number.isNaN(parsedDate.getTime()) ? dateStringParser(val) : (0, import_dayjs41.default)(parsedDate).format("YYYY-MM-DD");
  };
  const _dateParser = dateParser || defaultDateParser;
  const _allowDeselect = allowDeselect !== void 0 ? allowDeselect : clearable;
  const formatValue2 = (val) => val ? (0, import_dayjs41.default)(val).locale(ctx.getLocale(locale)).format(valueFormat) : "";
  const [_value, setValue, controlled] = useUncontrolledDates({
    type: "default",
    value,
    defaultValue,
    onChange
  });
  const [_date, setDate] = useUncontrolledDates({
    type: "default",
    value: date,
    defaultValue: defaultValue || defaultDate,
    onChange: onDateChange
  });
  (0, import_react17.useEffect)(() => {
    if (controlled && value !== null) {
      setDate(value);
    }
  }, [controlled, value]);
  const [inputValue, setInputValue] = (0, import_react17.useState)(formatValue2(_value));
  (0, import_react17.useEffect)(() => {
    setInputValue(formatValue2(_value));
  }, [ctx.getLocale(locale)]);
  const handleInputChange = (event) => {
    const val = event.currentTarget.value;
    setInputValue(val);
    setDropdownOpened(true);
    if (val.trim() === "" && clearable) {
      setValue(null);
    } else {
      const dateValue = _dateParser(val);
      if (isDateValid({ date: dateValue, minDate, maxDate })) {
        setValue(dateValue);
        setDate(dateValue);
      }
    }
  };
  const handleInputBlur = (event) => {
    onBlur == null ? void 0 : onBlur(event);
    setDropdownOpened(false);
    fixOnBlur && setInputValue(formatValue2(_value));
  };
  const handleInputFocus = (event) => {
    onFocus == null ? void 0 : onFocus(event);
    setDropdownOpened(true);
  };
  const handleInputClick = (event) => {
    onClick == null ? void 0 : onClick(event);
    setDropdownOpened(true);
  };
  const _getDayProps = (day) => ({
    ...getDayProps == null ? void 0 : getDayProps(day),
    selected: (0, import_dayjs41.default)(_value).isSame(day, "day"),
    onClick: (event) => {
      var _a, _b;
      (_b = getDayProps == null ? void 0 : (_a = getDayProps(day)).onClick) == null ? void 0 : _b.call(_a, event);
      const val = clearable && _allowDeselect ? (0, import_dayjs41.default)(_value).isSame(day, "day") ? null : day : day;
      setValue(val);
      !controlled && setInputValue(formatValue2(val));
      setDropdownOpened(false);
    }
  });
  const _rightSection = rightSection || (clearable && _value && !readOnly ? (0, import_jsx_runtime33.jsx)(
    CloseButton,
    {
      variant: "transparent",
      onMouseDown: (event) => event.preventDefault(),
      tabIndex: -1,
      onClick: () => {
        setValue(null);
        !controlled && setInputValue("");
        setDropdownOpened(false);
      },
      unstyled,
      size: inputProps.size || "sm",
      ...clearButtonProps
    }
  ) : null);
  useDidUpdate(() => {
    _value !== void 0 && !dropdownOpened && setInputValue(formatValue2(_value));
  }, [_value]);
  useClickOutside(() => setDropdownOpened(false), void 0, [
    _wrapperRef.current,
    _dropdownRef.current
  ]);
  return (0, import_jsx_runtime33.jsxs)(import_jsx_runtime33.Fragment, { children: [
    (0, import_jsx_runtime33.jsx)(Input.Wrapper, { ...wrapperProps, __staticSelector: "DateInput", ref: _wrapperRef, children: (0, import_jsx_runtime33.jsxs)(
      Popover,
      {
        opened: dropdownOpened,
        trapFocus: false,
        position: "bottom-start",
        disabled: readOnly,
        withRoles: false,
        unstyled,
        ...popoverProps,
        children: [
          (0, import_jsx_runtime33.jsx)(Popover.Target, { children: (0, import_jsx_runtime33.jsx)(
            Input,
            {
              "data-dates-input": true,
              "data-read-only": readOnly || void 0,
              autoComplete: "off",
              ref,
              value: inputValue,
              onChange: handleInputChange,
              onBlur: handleInputBlur,
              onFocus: handleInputFocus,
              onClick: handleInputClick,
              readOnly,
              rightSection: _rightSection,
              ...inputProps,
              ...others,
              __staticSelector: "DateInput"
            }
          ) }),
          (0, import_jsx_runtime33.jsx)(
            Popover.Dropdown,
            {
              onMouseDown: (event) => event.preventDefault(),
              "data-dates-dropdown": true,
              ref: _dropdownRef,
              children: (0, import_jsx_runtime33.jsx)(
                Calendar,
                {
                  __staticSelector: "DateInput",
                  ...calendarProps,
                  classNames,
                  styles,
                  unstyled,
                  __preventFocus: true,
                  minDate,
                  maxDate,
                  locale,
                  getDayProps: _getDayProps,
                  size: inputProps.size,
                  date: _date,
                  onDateChange: setDate
                }
              )
            }
          )
        ]
      }
    ) }),
    (0, import_jsx_runtime33.jsx)(HiddenDatesInput, { name, form, value: _value, type: "default" })
  ] });
});
DateInput.classes = { ...Input.classes, ...Calendar.classes };
DateInput.displayName = "@mantine/dates/DateInput";

// node_modules/@mantine/dates/esm/components/DateTimePicker/DateTimePicker.mjs
var import_jsx_runtime34 = __toESM(require_jsx_runtime(), 1);
var import_dayjs43 = __toESM(require_dayjs_min(), 1);
var import_react18 = __toESM(require_react(), 1);

// node_modules/@mantine/dates/esm/components/DateTimePicker/get-min-max-time/get-min-max-time.mjs
var import_dayjs42 = __toESM(require_dayjs_min(), 1);
function getMinTime({ minDate, value }) {
  const minTime = minDate ? (0, import_dayjs42.default)(minDate).format("HH:mm:ss") : null;
  return value && minDate && value === minDate ? minTime != null ? minTime : void 0 : void 0;
}
function getMaxTime({ maxDate, value }) {
  const maxTime = maxDate ? (0, import_dayjs42.default)(maxDate).format("HH:mm:ss") : null;
  return value && maxDate && value === maxDate ? maxTime != null ? maxTime : void 0 : void 0;
}

// node_modules/@mantine/dates/esm/components/DateTimePicker/DateTimePicker.module.css.mjs
var classes12 = { "timeWrapper": "m_208d2562", "timeInput": "m_62ee059" };

// node_modules/@mantine/dates/esm/components/DateTimePicker/DateTimePicker.mjs
var defaultProps23 = {
  dropdownType: "popover"
};
var DateTimePicker = factory((_props, ref) => {
  const props = useProps("DateTimePicker", defaultProps23, _props);
  const {
    value,
    defaultValue,
    onChange,
    valueFormat,
    locale,
    classNames,
    styles,
    unstyled,
    timePickerProps,
    submitButtonProps,
    withSeconds,
    level,
    defaultLevel,
    size,
    variant,
    dropdownType,
    vars,
    minDate,
    maxDate,
    ...rest
  } = props;
  const getStyles = useStyles({
    name: "DateTimePicker",
    classes: classes12,
    props,
    classNames,
    styles,
    unstyled,
    vars
  });
  const { resolvedClassNames, resolvedStyles } = useResolvedStylesApi({
    classNames,
    styles,
    props
  });
  const _valueFormat = valueFormat || (withSeconds ? "DD/MM/YYYY HH:mm:ss" : "DD/MM/YYYY HH:mm");
  const timePickerRef = (0, import_react18.useRef)(null);
  const timePickerRefMerged = useMergedRef(timePickerRef, timePickerProps == null ? void 0 : timePickerProps.hoursRef);
  const {
    calendarProps: { allowSingleDateInRange, ...calendarProps },
    others
  } = pickCalendarProps(rest);
  const ctx = useDatesContext();
  const [_value, setValue] = useUncontrolledDates({
    type: "default",
    value,
    defaultValue,
    onChange,
    withTime: true
  });
  const formatTime = (dateValue) => dateValue ? (0, import_dayjs43.default)(dateValue).format(withSeconds ? "HH:mm:ss" : "HH:mm") : "";
  const [timeValue, setTimeValue] = (0, import_react18.useState)(formatTime(_value));
  const [currentLevel, setCurrentLevel] = (0, import_react18.useState)(level || defaultLevel || "month");
  const [dropdownOpened, dropdownHandlers] = useDisclosure(false);
  const formattedValue = _value ? (0, import_dayjs43.default)(_value).locale(ctx.getLocale(locale)).format(_valueFormat) : "";
  const handleTimeChange = (timeString) => {
    var _a;
    (_a = timePickerProps == null ? void 0 : timePickerProps.onChange) == null ? void 0 : _a.call(timePickerProps, timeString);
    setTimeValue(timeString);
    if (timeString) {
      setValue(assignTime(_value, timeString));
    }
  };
  const handleDateChange = (date) => {
    var _a;
    if (date) {
      setValue(assignTime(clampDate(minDate, maxDate, date), timeValue));
    }
    (_a = timePickerRef.current) == null ? void 0 : _a.focus();
  };
  const handleTimeInputKeyDown = (event) => {
    if (event.key === "Enter") {
      event.preventDefault();
      dropdownHandlers.close();
    }
  };
  useDidUpdate(() => {
    if (!dropdownOpened) {
      setTimeValue(formatTime(_value));
    }
  }, [_value, dropdownOpened]);
  useDidUpdate(() => {
    if (dropdownOpened) {
      setCurrentLevel("month");
    }
  }, [dropdownOpened]);
  const __stopPropagation = dropdownType === "popover";
  const handleDropdownClose = () => {
    const clamped = clampDate(minDate, maxDate, _value);
    if (_value && _value !== clamped) {
      setValue(clampDate(minDate, maxDate, _value));
    }
  };
  return (0, import_jsx_runtime34.jsxs)(
    PickerInputBase,
    {
      formattedValue,
      dropdownOpened: !rest.disabled ? dropdownOpened : false,
      dropdownHandlers,
      classNames: resolvedClassNames,
      styles: resolvedStyles,
      unstyled,
      ref,
      onClear: () => setValue(null),
      shouldClear: !!_value,
      value: _value,
      size,
      variant,
      dropdownType,
      ...others,
      type: "default",
      __staticSelector: "DateTimePicker",
      onDropdownClose: handleDropdownClose,
      withTime: true,
      children: [
        (0, import_jsx_runtime34.jsx)(
          DatePicker,
          {
            ...calendarProps,
            maxDate,
            minDate,
            size,
            variant,
            type: "default",
            value: _value,
            defaultDate: _value,
            onChange: handleDateChange,
            locale,
            classNames: resolvedClassNames,
            styles: resolvedStyles,
            unstyled,
            __staticSelector: "DateTimePicker",
            __stopPropagation,
            level,
            defaultLevel,
            onLevelChange: (_level) => {
              var _a;
              setCurrentLevel(_level);
              (_a = calendarProps.onLevelChange) == null ? void 0 : _a.call(calendarProps, _level);
            }
          }
        ),
        currentLevel === "month" && (0, import_jsx_runtime34.jsxs)("div", { ...getStyles("timeWrapper"), children: [
          (0, import_jsx_runtime34.jsx)(
            TimePicker,
            {
              value: timeValue,
              withSeconds,
              unstyled,
              min: getMinTime({ minDate, value: _value }),
              max: getMaxTime({ maxDate, value: _value }),
              ...timePickerProps,
              ...getStyles("timeInput", {
                className: timePickerProps == null ? void 0 : timePickerProps.className,
                style: timePickerProps == null ? void 0 : timePickerProps.style
              }),
              onChange: handleTimeChange,
              onKeyDown: handleTimeInputKeyDown,
              size,
              "data-mantine-stop-propagation": __stopPropagation || void 0,
              hoursRef: timePickerRefMerged
            }
          ),
          (0, import_jsx_runtime34.jsx)(
            ActionIcon,
            {
              variant: "default",
              size: `input-${size || "sm"}`,
              ...getStyles("submitButton", {
                className: submitButtonProps == null ? void 0 : submitButtonProps.className,
                style: submitButtonProps == null ? void 0 : submitButtonProps.style
              }),
              unstyled,
              "data-mantine-stop-propagation": __stopPropagation || void 0,
              children: (0, import_jsx_runtime34.jsx)(CheckIcon, { size: "30%" }),
              ...submitButtonProps,
              onClick: (event) => {
                var _a;
                (_a = submitButtonProps == null ? void 0 : submitButtonProps.onClick) == null ? void 0 : _a.call(submitButtonProps, event);
                dropdownHandlers.close();
                handleDropdownClose();
              }
            }
          )
        ] })
      ]
    }
  );
});
DateTimePicker.classes = { ...classes12, ...PickerInputBase.classes, ...DatePicker.classes };
DateTimePicker.displayName = "@mantine/dates/DateTimePicker";

// node_modules/@mantine/dates/esm/components/YearPickerInput/YearPickerInput.mjs
var import_jsx_runtime35 = __toESM(require_jsx_runtime(), 1);
var import_dayjs45 = __toESM(require_dayjs_min(), 1);
var import_react19 = __toESM(require_react(), 1);

// node_modules/@mantine/dates/esm/hooks/use-dates-input/use-dates-input.mjs
var import_dayjs44 = __toESM(require_dayjs_min(), 1);
function useDatesInput({
  type,
  value,
  defaultValue,
  onChange,
  locale,
  format,
  closeOnChange,
  sortDates,
  labelSeparator,
  valueFormatter
}) {
  const ctx = useDatesContext();
  const [dropdownOpened, dropdownHandlers] = useDisclosure(false);
  const [_value, _setValue] = useUncontrolledDates({
    type,
    value,
    defaultValue,
    onChange
  });
  const formattedValue = getFormattedDate({
    type,
    date: _value,
    locale: ctx.getLocale(locale),
    format,
    labelSeparator: ctx.getLabelSeparator(labelSeparator),
    formatter: valueFormatter
  });
  const setValue = (val) => {
    if (closeOnChange) {
      if (type === "default") {
        dropdownHandlers.close();
      }
      if (type === "range" && val[0] && val[1]) {
        dropdownHandlers.close();
      }
    }
    if (sortDates && type === "multiple") {
      _setValue([...val].sort((a, b) => (0, import_dayjs44.default)(a).isAfter((0, import_dayjs44.default)(b)) ? 1 : -1));
    } else {
      _setValue(val);
    }
  };
  const onClear = () => setValue(type === "range" ? [null, null] : type === "multiple" ? [] : null);
  const shouldClear = type === "range" ? !!_value[0] : type === "multiple" ? _value.length > 0 : _value !== null;
  return {
    _value,
    setValue,
    onClear,
    shouldClear,
    formattedValue,
    dropdownOpened,
    dropdownHandlers
  };
}

// node_modules/@mantine/dates/esm/components/YearPickerInput/YearPickerInput.mjs
var defaultProps24 = {
  type: "default",
  valueFormat: "YYYY",
  closeOnChange: true,
  sortDates: true,
  dropdownType: "popover"
};
var YearPickerInput = factory(
  (_props, ref) => {
    const props = useProps("YearPickerInput", defaultProps24, _props);
    const {
      type,
      value,
      defaultValue,
      onChange,
      valueFormat,
      labelSeparator,
      locale,
      classNames,
      styles,
      unstyled,
      closeOnChange,
      size,
      variant,
      dropdownType,
      sortDates,
      minDate,
      maxDate,
      vars,
      valueFormatter,
      ...rest
    } = props;
    const { resolvedClassNames, resolvedStyles } = useResolvedStylesApi({
      classNames,
      styles,
      props
    });
    const { calendarProps, others } = pickCalendarProps(rest);
    const {
      _value,
      setValue,
      formattedValue,
      dropdownHandlers,
      dropdownOpened,
      onClear,
      shouldClear
    } = useDatesInput({
      type,
      value,
      defaultValue,
      onChange,
      locale,
      format: valueFormat,
      labelSeparator,
      closeOnChange,
      sortDates,
      valueFormatter
    });
    return (0, import_jsx_runtime35.jsx)(
      PickerInputBase,
      {
        formattedValue,
        dropdownOpened,
        dropdownHandlers,
        classNames: resolvedClassNames,
        styles: resolvedStyles,
        unstyled,
        ref,
        onClear,
        shouldClear,
        value: _value,
        size,
        variant,
        dropdownType,
        ...others,
        type,
        __staticSelector: "YearPickerInput",
        children: (0, import_jsx_runtime35.jsx)(
          YearPicker,
          {
            ...calendarProps,
            size,
            variant,
            type,
            value: _value,
            defaultDate: calendarProps.defaultDate || (Array.isArray(_value) ? _value[0] || getDefaultClampedDate({ maxDate, minDate }) : _value || getDefaultClampedDate({ maxDate, minDate })),
            onChange: setValue,
            locale,
            classNames: resolvedClassNames,
            styles: resolvedStyles,
            unstyled,
            __staticSelector: "YearPickerInput",
            __stopPropagation: dropdownType === "popover",
            minDate,
            maxDate
          }
        )
      }
    );
  }
);
YearPickerInput.classes = { ...PickerInputBase.classes, ...YearPicker.classes };
YearPickerInput.displayName = "@mantine/dates/YearPickerInput";

// node_modules/@mantine/dates/esm/components/MonthPickerInput/MonthPickerInput.mjs
var import_jsx_runtime36 = __toESM(require_jsx_runtime(), 1);
var import_dayjs46 = __toESM(require_dayjs_min(), 1);
var import_react20 = __toESM(require_react(), 1);
var defaultProps25 = {
  type: "default",
  valueFormat: "MMMM YYYY",
  closeOnChange: true,
  sortDates: true,
  dropdownType: "popover"
};
var MonthPickerInput = factory(
  (_props, ref) => {
    const props = useProps("MonthPickerInput", defaultProps25, _props);
    const {
      type,
      value,
      defaultValue,
      onChange,
      valueFormat,
      labelSeparator,
      locale,
      classNames,
      styles,
      unstyled,
      closeOnChange,
      size,
      variant,
      dropdownType,
      sortDates,
      minDate,
      maxDate,
      vars,
      valueFormatter,
      ...rest
    } = props;
    const { resolvedClassNames, resolvedStyles } = useResolvedStylesApi({
      classNames,
      styles,
      props
    });
    const { calendarProps, others } = pickCalendarProps(rest);
    const {
      _value,
      setValue,
      formattedValue,
      dropdownHandlers,
      dropdownOpened,
      onClear,
      shouldClear
    } = useDatesInput({
      type,
      value,
      defaultValue,
      onChange,
      locale,
      format: valueFormat,
      labelSeparator,
      closeOnChange,
      sortDates,
      valueFormatter
    });
    return (0, import_jsx_runtime36.jsx)(
      PickerInputBase,
      {
        formattedValue,
        dropdownOpened,
        dropdownHandlers,
        classNames: resolvedClassNames,
        styles: resolvedStyles,
        unstyled,
        ref,
        onClear,
        shouldClear,
        value: _value,
        size,
        variant,
        dropdownType,
        ...others,
        type,
        __staticSelector: "MonthPickerInput",
        children: (0, import_jsx_runtime36.jsx)(
          MonthPicker,
          {
            ...calendarProps,
            size,
            variant,
            type,
            value: _value,
            defaultDate: calendarProps.defaultDate || (Array.isArray(_value) ? _value[0] || getDefaultClampedDate({ maxDate, minDate }) : _value || getDefaultClampedDate({ maxDate, minDate })),
            onChange: setValue,
            locale,
            classNames: resolvedClassNames,
            styles: resolvedStyles,
            unstyled,
            __staticSelector: "MonthPickerInput",
            __stopPropagation: dropdownType === "popover",
            minDate,
            maxDate
          }
        )
      }
    );
  }
);
MonthPickerInput.classes = { ...PickerInputBase.classes, ...MonthPicker.classes };
MonthPickerInput.displayName = "@mantine/dates/MonthPickerInput";

// node_modules/@mantine/dates/esm/components/DatePickerInput/DatePickerInput.mjs
var import_jsx_runtime37 = __toESM(require_jsx_runtime(), 1);
var import_dayjs47 = __toESM(require_dayjs_min(), 1);
var import_react21 = __toESM(require_react(), 1);
var defaultProps26 = {
  type: "default",
  valueFormat: "MMMM D, YYYY",
  closeOnChange: true,
  sortDates: true,
  dropdownType: "popover"
};
var DatePickerInput = factory(
  (_props, ref) => {
    const props = useProps("DatePickerInput", defaultProps26, _props);
    const {
      type,
      value,
      defaultValue,
      onChange,
      valueFormat,
      labelSeparator,
      locale,
      classNames,
      styles,
      unstyled,
      closeOnChange,
      size,
      variant,
      dropdownType,
      sortDates,
      minDate,
      maxDate,
      vars,
      defaultDate,
      valueFormatter,
      ...rest
    } = props;
    const { resolvedClassNames, resolvedStyles } = useResolvedStylesApi({
      classNames,
      styles,
      props
    });
    const { calendarProps, others } = pickCalendarProps(rest);
    const {
      _value,
      setValue,
      formattedValue,
      dropdownHandlers,
      dropdownOpened,
      onClear,
      shouldClear
    } = useDatesInput({
      type,
      value,
      defaultValue,
      onChange,
      locale,
      format: valueFormat,
      labelSeparator,
      closeOnChange,
      sortDates,
      valueFormatter
    });
    const _defaultDate = Array.isArray(_value) ? _value[0] || defaultDate : _value || defaultDate;
    return (0, import_jsx_runtime37.jsx)(
      PickerInputBase,
      {
        formattedValue,
        dropdownOpened,
        dropdownHandlers,
        classNames: resolvedClassNames,
        styles: resolvedStyles,
        unstyled,
        ref,
        onClear,
        shouldClear,
        value: _value,
        size,
        variant,
        dropdownType,
        ...others,
        type,
        __staticSelector: "DatePickerInput",
        children: (0, import_jsx_runtime37.jsx)(
          DatePicker,
          {
            ...calendarProps,
            size,
            variant,
            type,
            value: _value,
            defaultDate: _defaultDate || getDefaultClampedDate({ maxDate, minDate }),
            onChange: setValue,
            locale,
            classNames: resolvedClassNames,
            styles: resolvedStyles,
            unstyled,
            __staticSelector: "DatePickerInput",
            __stopPropagation: dropdownType === "popover",
            minDate,
            maxDate
          }
        )
      }
    );
  }
);
DatePickerInput.classes = { ...PickerInputBase.classes, ...DatePicker.classes };
DatePickerInput.displayName = "@mantine/dates/DatePickerInput";

// node_modules/@mantine/dates/esm/components/TimeGrid/TimeGrid.mjs
var import_jsx_runtime39 = __toESM(require_jsx_runtime(), 1);

// node_modules/@mantine/dates/esm/components/TimeGrid/compare-time.mjs
function isTimeBefore(value, compareTo) {
  return timeToSeconds(value) < timeToSeconds(compareTo);
}
function isTimeAfter(value, compareTo) {
  return timeToSeconds(value) > timeToSeconds(compareTo);
}

// node_modules/@mantine/dates/esm/components/TimeGrid/TimeGrid.context.mjs
var [TimeGridProvider, useTimeGridContext] = createSafeContext(
  "TimeGridProvider was not found in the component tree"
);

// node_modules/@mantine/dates/esm/components/TimeGrid/TimeGridControl.mjs
var import_jsx_runtime38 = __toESM(require_jsx_runtime(), 1);
function TimeGridControl({
  time,
  active,
  className,
  amPmLabels,
  format,
  withSeconds,
  ...others
}) {
  const ctx = useTimeGridContext();
  const theme = useMantineTheme();
  return (0, import_jsx_runtime38.jsx)(
    UnstyledButton,
    {
      mod: [{ active }],
      ...ctx.getStyles("control", { className: clsx_default(theme.activeClassName, className) }),
      ...others,
      children: (0, import_jsx_runtime38.jsx)(TimeValue, { value: time, format, amPmLabels, withSeconds })
    }
  );
}

// node_modules/@mantine/dates/esm/components/TimeGrid/TimeGrid.module.css.mjs
var classes13 = { "control": "m_ac3f4d63" };

// node_modules/@mantine/dates/esm/components/TimeGrid/TimeGrid.mjs
var defaultProps27 = {
  simpleGridProps: { cols: 3, spacing: "xs" },
  format: "24h",
  amPmLabels: { am: "AM", pm: "PM" }
};
var varsResolver7 = createVarsResolver((_theme, { size, radius }) => ({
  root: {
    "--time-grid-fz": getFontSize(size),
    "--time-grid-radius": getRadius(radius)
  }
}));
var TimeGrid = factory((_props, ref) => {
  const props = useProps("TimeGrid", defaultProps27, _props);
  const {
    classNames,
    className,
    style,
    styles,
    unstyled,
    vars,
    data,
    value,
    defaultValue,
    onChange,
    format,
    withSeconds = false,
    amPmLabels,
    allowDeselect,
    simpleGridProps,
    getControlProps,
    minTime,
    maxTime,
    disableTime,
    disabled,
    ...others
  } = props;
  const getStyles = useStyles({
    name: "TimeGrid",
    classes: classes13,
    props,
    className,
    style,
    classNames,
    styles,
    unstyled,
    vars,
    varsResolver: varsResolver7
  });
  const [_value, setValue] = useUncontrolled({
    value,
    defaultValue,
    finalValue: null,
    onChange
  });
  const controls = data.map((time) => {
    const isDisabled = disabled || !!minTime && isTimeBefore(time, minTime) || !!maxTime && isTimeAfter(time, maxTime) || (Array.isArray(disableTime) ? !!disableTime.find((t) => isSameTime({ time, compare: t, withSeconds })) : !!(disableTime == null ? void 0 : disableTime(time)));
    return (0, import_jsx_runtime39.jsx)(
      TimeGridControl,
      {
        active: isSameTime({ time, compare: _value || "", withSeconds }),
        time,
        onClick: () => {
          const nextValue = allowDeselect && (_value === null ? time === _value : isSameTime({ time, compare: _value, withSeconds })) ? null : time;
          nextValue !== _value && setValue(nextValue);
        },
        format,
        amPmLabels,
        disabled: isDisabled,
        "data-disabled": isDisabled || void 0,
        withSeconds,
        ...getControlProps == null ? void 0 : getControlProps(time)
      },
      time
    );
  });
  return (0, import_jsx_runtime39.jsx)(TimeGridProvider, { value: { getStyles }, children: (0, import_jsx_runtime39.jsx)(Box, { ref, ...getStyles("root"), ...others, children: (0, import_jsx_runtime39.jsx)(
    SimpleGrid,
    {
      ...simpleGridProps,
      ...getStyles("simpleGrid", {
        className: simpleGridProps == null ? void 0 : simpleGridProps.className,
        style: simpleGridProps == null ? void 0 : simpleGridProps.style
      }),
      children: controls
    }
  ) }) });
});
TimeGrid.displayName = "@mantine/dates/TimeGrid";
TimeGrid.classes = classes13;
export {
  Calendar,
  CalendarHeader,
  DATES_PROVIDER_DEFAULT_SETTINGS,
  DateInput,
  DatePicker,
  DatePickerInput,
  DateTimePicker,
  DatesProvider,
  Day,
  DecadeLevel,
  DecadeLevelGroup,
  HiddenDatesInput,
  LevelsGroup,
  Month,
  MonthLevel,
  MonthLevelGroup,
  MonthPicker,
  MonthPickerInput,
  MonthsList,
  PickerControl,
  PickerInputBase,
  TimeGrid,
  TimeInput,
  TimePicker,
  TimeValue,
  WeekdaysRow,
  YearLevel,
  YearLevelGroup,
  YearPicker,
  YearPickerInput,
  YearsList,
  assignTime,
  clampDate,
  getDefaultClampedDate,
  getEndOfWeek,
  getFormattedDate,
  getMonthDays,
  getStartOfWeek,
  getTimeRange,
  handleControlKeyDown,
  isSameMonth,
  isTimeAfter,
  isTimeBefore,
  pickCalendarProps,
  toDateString,
  toDateTimeString,
  useDatesContext
};
//# sourceMappingURL=@mantine_dates.js.map
