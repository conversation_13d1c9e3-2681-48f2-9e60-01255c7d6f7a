"use client";

import JobCard from "@/components/employer/manage-jobs/JobCard";
import { PageContainer, PageHeading } from "@/design-system/components";
import useManageJobs from "@/hooks/employer/use-manage-jobs";
import {
  ActionIcon,
  Badge,
  Box,
  Button,
  Card,
  Divider,
  Grid,
  Group,
  LoadingOverlay,
  Pagination,
  SegmentedControl,
  Select,
  Text,
  TextInput,
  Title,
  useMantineColorScheme,
  useMantineTheme,
} from "@mantine/core";
import { useMediaQuery } from "@mantine/hooks";
import { useState } from "react";
import {
  FaFilter,
  FaPlus,
  FaSearch,
  FaSortAmountDown,
  FaSortAmountUp,
  FaThLarge,
  FaThList,
} from "react-icons/fa";
import { Link } from "react-router";

export default function EmployerManageJobsPage() {
  // State for search and filters
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState<string | null>(null);
  const [sortBy, setSortBy] = useState<string | null>("newest");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [currentPage, setCurrentPage] = useState(1);
  const jobsPerPage = 10;

  // Get jobs data from hook
  const {
    jobs,
    isLoading,
    isDeleting,
    isToggling,
    error,
    deleteJob,
    toggleJobStatus,
  } = useManageJobs();

  // Responsive helpers
  const theme = useMantineTheme();
  const isMobile = useMediaQuery(`(max-width: ${theme.breakpoints.sm})`);

  // Theme helpers
  const { colorScheme } = useMantineColorScheme();
  const isDark = colorScheme === "dark";

  // Filter jobs based on search and status
  const filteredJobs = jobs.filter((job) => {
    const matchesStatus = statusFilter ? job.status === statusFilter : true;
    const matchesSearch = job.title
      .toLowerCase()
      .includes(searchQuery.toLowerCase());
    return matchesStatus && matchesSearch;
  });

  // Sort jobs
  const sortedJobs = [...filteredJobs].sort((a, b) => {
    if (sortBy === "newest") {
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    }
    if (sortBy === "oldest") {
      return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
    }
    if (sortBy === "title-asc") return a.title.localeCompare(b.title);
    if (sortBy === "title-desc") return b.title.localeCompare(a.title);
    return 0;
  });

  // Pagination
  const totalPages = Math.ceil(sortedJobs.length / jobsPerPage);
  const paginatedJobs = sortedJobs.slice(
    (currentPage - 1) * jobsPerPage,
    currentPage * jobsPerPage,
  );

  return (
    <PageContainer
      breadcrumbItems={[{ title: "Home", href: "/" }, { title: "Manage Jobs" }]}
      variant="employer"
      className="pb-12"
    >
      {/* Hero Section */}
      <Box
        className="mb-8 rounded-lg px-4 py-8 md:px-6 md:py-10"
        mx={{ base: -4, sm: -6 }}
        style={{
          background: isDark
            ? "linear-gradient(to right, #1a1b2e, #141b2d, #1a1b2e)"
            : "linear-gradient(to right, #f0f4ff, #e6f0ff, #f0f4ff)",
        }}
      >
        <PageHeading
          title="Manage Jobs"
          subtitle="View, edit, and track your job postings"
          className="mb-6 text-center md:mb-8"
          variant="employer"
        />

        <Card
          className="mx-auto max-w-4xl shadow-sm"
          withBorder={false}
          p={{ base: "xs", sm: "md" }}
        >
          <Group wrap="nowrap" align="flex-start">
            <TextInput
              placeholder="Search by job title"
              className="min-w-0 flex-grow"
              leftSection={<FaSearch size={16} />}
              size={isMobile ? "sm" : "md"}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            <Group wrap="nowrap" gap="xs">
              <Select
                placeholder="Status"
                data={[
                  { value: "Active", label: "Active" },
                  { value: "Pending", label: "Pending" },
                  { value: "Closed", label: "Closed" },
                ]}
                leftSection={<FaFilter size={16} />}
                size={isMobile ? "sm" : "md"}
                value={statusFilter}
                onChange={setStatusFilter}
                clearable
              />
              <Link to="/employer/create-job">
                <Button
                  size={isMobile ? "sm" : "md"}
                  leftSection={<FaPlus size={14} />}
                >
                  Post Job
                </Button>
              </Link>
            </Group>
          </Group>
        </Card>
      </Box>

      {/* Results and View Controls */}
      <Card withBorder radius="md" className="mb-6 shadow-sm" p="md">
        <Group justify="space-between" mb="sm" wrap="nowrap" align="center">
          <Group wrap="nowrap" gap="xs">
            <Text fw={500} size="sm" className="sm:text-base">
              Results
            </Text>
            <Text c="dimmed" size="xs" className="hidden sm:block">
              Showing {sortedJobs.length} job
              {sortedJobs.length !== 1 ? "s" : ""}
            </Text>
          </Group>

          <Group gap="md">
            <Select
              placeholder="Sort by"
              data={[
                { value: "newest", label: "Newest First" },
                { value: "oldest", label: "Oldest First" },
                { value: "title-asc", label: "Title (A-Z)" },
                { value: "title-desc", label: "Title (Z-A)" },
              ]}
              leftSection={
                sortBy?.includes("desc") ? (
                  <FaSortAmountDown size={14} />
                ) : (
                  <FaSortAmountUp size={14} />
                )
              }
              size="sm"
              value={sortBy}
              onChange={setSortBy}
              className="hidden w-40 sm:block"
            />

            <SegmentedControl
              value={viewMode}
              onChange={(value) => setViewMode(value as "grid" | "list")}
              data={[
                {
                  value: "grid",
                  label: (
                    <div className="flex items-center gap-2">
                      <FaThLarge size={14} />
                      <span className="hidden sm:inline">Grid</span>
                    </div>
                  ),
                },
                {
                  value: "list",
                  label: (
                    <div className="flex items-center gap-2">
                      <FaThList size={14} />
                      <span className="hidden sm:inline">List</span>
                    </div>
                  ),
                },
              ]}
              size="xs"
            />
          </Group>
        </Group>

        <Divider mb="sm" />

        {/* Filter badges */}
        {(searchQuery || statusFilter) && (
          <Group mt="xs">
            {searchQuery && (
              <Badge
                variant="light"
                color="blue"
                rightSection={
                  <ActionIcon
                    size="xs"
                    color="blue"
                    variant="transparent"
                    onClick={() => setSearchQuery("")}
                  >
                    ×
                  </ActionIcon>
                }
              >
                Search: {searchQuery}
              </Badge>
            )}
            {statusFilter && (
              <Badge
                variant="light"
                color={
                  statusFilter === "Active"
                    ? "blue"
                    : statusFilter === "Pending"
                      ? "yellow"
                      : "red"
                }
                rightSection={
                  <ActionIcon
                    size="xs"
                    color={
                      statusFilter === "Active"
                        ? "blue"
                        : statusFilter === "Pending"
                          ? "yellow"
                          : "red"
                    }
                    variant="transparent"
                    onClick={() => setStatusFilter(null)}
                  >
                    ×
                  </ActionIcon>
                }
              >
                Status: {statusFilter}
              </Badge>
            )}
          </Group>
        )}
      </Card>

      {/* Loading overlay */}
      <div className="relative min-h-[200px]">
        <LoadingOverlay
          visible={isLoading}
          overlayProps={{ blur: 2 }}
          loaderProps={{
            children: "Loading jobs...",
          }}
        />

        {/* Jobs List */}
        {!isLoading && sortedJobs.length === 0 ? (
          <Card withBorder radius="md" className="p-8 text-center shadow-sm">
            <Title order={3} className="mb-2 text-gray-700">
              No jobs found
            </Title>
            <Text c="dimmed" className="mb-6">
              {error
                ? error
                : "Try adjusting your search or filters to find what you're looking for."}
            </Text>
            <Link to="/employer/create-job">
              <Button leftSection={<FaPlus size={14} />}>Post a New Job</Button>
            </Link>
          </Card>
        ) : (
          <Grid gutter="lg">
            {paginatedJobs.map((job) => (
              <JobCard
                key={`${job._id}-${viewMode}`}
                job={job}
                viewMode={viewMode}
                onDelete={() => deleteJob(job._id)}
                onToggleStatus={() => toggleJobStatus(job._id)}
                isDeleting={isDeleting}
                isToggling={isToggling}
              />
            ))}
          </Grid>
        )}
      </div>

      {/* Pagination */}
      {!isLoading && sortedJobs.length > 0 && (
        <div className="mt-8 flex justify-center">
          <Pagination
            total={totalPages}
            value={currentPage}
            onChange={setCurrentPage}
            radius="md"
            size={isMobile ? "sm" : "md"}
          />
        </div>
      )}
    </PageContainer>
  );
}
