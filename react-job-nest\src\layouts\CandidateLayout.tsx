"use client";

import { useThemeClasses } from "@/design-system/utils/theme-utils";
import { useMantineColorScheme } from "@mantine/core";
import { Outlet } from "react-router";

export default function CandidateLayout() {
  const { colorScheme } = useMantineColorScheme();
  const isDark = colorScheme === "dark";

  return (
    <div className="container py-4">
      <div
        className={useThemeClasses(
          "flex-1 rounded-lg border border-gray-200 bg-white p-4 shadow-sm",
          "flex-1 rounded-lg border border-dark-4 bg-dark-7 p-4 shadow-dark-lg",
        )}
        style={{
          borderColor: isDark ? "var(--mantine-color-dark-4)" : undefined,
          backgroundColor: isDark ? "var(--mantine-color-dark-7)" : undefined,
        }}
      >
        <Outlet />
      </div>
    </div>
  );
}
