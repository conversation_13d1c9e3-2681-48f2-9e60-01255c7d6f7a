import express from "express";
import { protect, restrictTo } from "../middleware/auth.middleware";
import { getApplicationsByJob, updateApplicationStatus } from "../controllers/application.controller";
import { updateApplicationStatusValidator } from "../validators/application.validator";
import { validationMiddleware } from "../validators/validation.middleware";

const router = express.Router();

// All routes are protected and restricted to employers and admins
router.use(protect);
router.use(restrictTo("employer", "admin"));

// Job applications routes
router.get("/jobs/:jobId/applications", getApplicationsByJob);
router.patch(
  "/applications/:id/status",
  updateApplicationStatusValidator,
  validationMiddleware,
  updateApplicationStatus
);

export default router;
