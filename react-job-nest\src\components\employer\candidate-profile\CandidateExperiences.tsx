import { candidateData } from "@/data/candidate-data";
import { Card, Text, Title } from "@mantine/core";

export default function CandidateExperiences() {
  return (
    <Card withBorder radius="md" className="mb-6 p-6 shadow-sm">
      <Title order={2} className="mb-4 text-lg font-semibold">
        Experience
      </Title>
      {candidateData.experience.map((exp, index) => (
        <div
          key={index}
          className="mb-4 border-b border-gray-200 pb-4 last:border-b-0"
        >
          <Text size="md" className="mb-1" component="div">
            <strong>Company:</strong> {exp.company}
          </Text>
          <Text size="md" className="mb-1" component="div">
            <strong>Position:</strong> {exp.position}
          </Text>
          <Text size="md" className="mb-1" component="div">
            <strong>Duration:</strong> {exp.duration}
          </Text>
          <Text size="md" className="mb-1" component="div">
            <strong>Description:</strong> {exp.description}
          </Text>
        </div>
      ))}
    </Card>
  );
}
