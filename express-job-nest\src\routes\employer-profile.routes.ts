import express from "express";
import {
  getMyEmployerProfile,
  updateMyEmployerProfile,
  uploadEmployerProfileImage,
  uploadAndSaveEmployerProfileImage,
} from "../controllers/employer-profile.controller";
import { protect, restrictTo } from "../middleware/auth.middleware";

const router = express.Router();

// All routes are protected and restricted to employers and admins
router.use(protect);
router.use(restrictTo("employer", "admin"));

// Profile routes
router.get("/me", getMyEmployerProfile);
router.patch("/me", updateMyEmployerProfile);

// Profile image routes
router.post("/profile-image", uploadEmployerProfileImage, uploadAndSaveEmployerProfileImage);

export default router;
