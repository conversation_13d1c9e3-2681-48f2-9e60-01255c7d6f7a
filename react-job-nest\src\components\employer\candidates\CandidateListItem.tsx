"use client";

import { useSelectedCandidateStore } from "@/stores/employer-store";
import { useCandidateDetailsModal } from "@/stores/modal-store";
import {
  ActionIcon,
  Avatar,
  Badge,
  Card,
  Group,
  Menu,
  Text,
} from "@mantine/core";
import {
  FaBriefcase,
  FaCalendarAlt,
  FaCheck,
  FaEllipsisV,
  FaEnvelope,
  FaEye,
  FaPhoneAlt,
  FaTimes,
  FaUserCheck,
  FaUserClock,
  FaUserEdit,
  FaUserTie,
} from "react-icons/fa";

// Define the candidate interface for this component
interface Candidate {
  _id: string;
  name: string;
  email: string;
  status: string;
  job: {
    _id: string;
    title: string;
  };
  appliedDate: string;
  skills?: string[];
  resume?: string;
  coverLetter?: string;
}

type CandidateListItemProps = {
  candidate: Candidate;
  job: {
    _id: string;
    title: string;
  };
  onUpdateStatus?: (candidateId: string, newStatus: string) => Promise<boolean>;
};

export default function CandidateListItem({
  candidate,
  job,
  onUpdateStatus,
}: CandidateListItemProps) {
  const setSelectedCandidateId = useSelectedCandidateStore(
    (state) => state.setSelectedCandidateId,
  );
  const candidateDetailsModal = useCandidateDetailsModal();

  const openModal = () => {
    setSelectedCandidateId(Number(candidate._id));
    candidateDetailsModal.open();
  };

  // Function to update candidate status
  const updateStatus = async (newStatus: string) => {
    if (onUpdateStatus) {
      await onUpdateStatus(candidate._id, newStatus);
    }
  };

  // Function to get status color
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "pending":
        return "blue";
      case "reviewing":
        return "indigo";
      case "shortlisted":
        return "green";
      case "interview":
        return "yellow";
      case "offered":
        return "cyan";
      case "hired":
        return "violet";
      case "rejected":
        return "red";
      default:
        return "gray";
    }
  };

  // Function to get status icon
  // const getStatusIcon = (status: string) => {
  //   switch (status.toLowerCase()) {
  //     case "pending":
  //       return <FaUserClock size={14} />;
  //     case "reviewing":
  //       return <FaUserEdit size={14} />;
  //     case "shortlisted":
  //       return <FaCheck size={14} />;
  //     case "interview":
  //       return <FaUserTie size={14} />;
  //     case "offered":
  //       return <FaUserCheck size={14} />;
  //     case "hired":
  //       return <FaUserCheck size={14} />;
  //     case "rejected":
  //       return <FaTimes size={14} />;
  //     default:
  //       return <FaUserClock size={14} />;
  //   }
  // };

  // Function to get initials from name
  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase();
  };

  return (
    <Card withBorder radius="md" className="shadow-sm" p="md">
      <Group justify="space-between" align="flex-start">
        {/* Candidate Info */}
        <Group align="flex-start" wrap="nowrap">
          <Avatar
            radius="xl"
            size="lg"
            color={getStatusColor(candidate.status)}
            className="hidden sm:flex"
          >
            {getInitials(candidate.name)}
          </Avatar>

          <div>
            <Group align="center" gap="xs">
              <Text fw={600} size="lg">
                {candidate.name}
              </Text>
              <Badge color={getStatusColor(candidate.status)} size="sm">
                {candidate.status}
              </Badge>
            </Group>

            <Text size="sm" c="dimmed" mb={8}>
              {candidate.email}
            </Text>

            <Group gap="lg">
              <Group gap="xs" wrap="nowrap">
                <FaBriefcase
                  size={14}
                  className="flex-shrink-0 text-blue-500"
                />
                <Text size="sm" className="line-clamp-1">
                  Applied for: <strong>{job?.title || "Unknown Job"}</strong>
                </Text>
              </Group>

              <Group gap="xs" wrap="nowrap">
                <FaCalendarAlt
                  size={14}
                  className="flex-shrink-0 text-blue-500"
                />
                <Text size="sm">Applied on: {candidate.appliedDate}</Text>
              </Group>
            </Group>

            {/* Skills */}
            {candidate.skills && candidate.skills.length > 0 && (
              <Group gap="xs" mt={8}>
                {candidate.skills.map((skill, index) => (
                  <Badge key={index} color="blue" variant="light" size="sm">
                    {skill}
                  </Badge>
                ))}
              </Group>
            )}
          </div>
        </Group>

        {/* Actions */}
        <Group gap="xs">
          <ActionIcon
            variant="light"
            color="blue"
            onClick={() => openModal()}
            title="View Details"
          >
            <FaEye size={16} />
          </ActionIcon>

          <Menu position="bottom-end" shadow="md">
            <Menu.Target>
              <div className="cursor-pointer">
                <ActionIcon variant="subtle">
                  <FaEllipsisV size={16} />
                </ActionIcon>
              </div>
            </Menu.Target>

            <Menu.Dropdown>
              <Menu.Item
                leftSection={<FaEye size={14} />}
                onClick={() => openModal()}
              >
                View Details
              </Menu.Item>
              <Menu.Item
                leftSection={<FaUserEdit size={14} />}
                closeMenuOnClick={false}
              >
                <Menu
                  position="right-start"
                  offset={10}
                  shadow="md"
                  trigger="hover"
                >
                  <Menu.Target>
                    <div>Change Status</div>
                  </Menu.Target>
                  <Menu.Dropdown>
                    <Menu.Item
                      leftSection={<FaUserClock size={14} />}
                      onClick={() => updateStatus("pending")}
                      disabled={candidate.status.toLowerCase() === "pending"}
                    >
                      Pending
                    </Menu.Item>
                    <Menu.Item
                      leftSection={<FaUserEdit size={14} />}
                      onClick={() => updateStatus("reviewing")}
                      disabled={candidate.status.toLowerCase() === "reviewing"}
                    >
                      Reviewing
                    </Menu.Item>
                    <Menu.Item
                      leftSection={<FaCheck size={14} />}
                      onClick={() => updateStatus("shortlisted")}
                      disabled={
                        candidate.status.toLowerCase() === "shortlisted"
                      }
                    >
                      Shortlisted
                    </Menu.Item>
                    <Menu.Item
                      leftSection={<FaUserTie size={14} />}
                      onClick={() => updateStatus("interview")}
                      disabled={candidate.status.toLowerCase() === "interview"}
                    >
                      Interview
                    </Menu.Item>
                    <Menu.Item
                      leftSection={<FaUserCheck size={14} />}
                      onClick={() => updateStatus("offered")}
                      disabled={candidate.status.toLowerCase() === "offered"}
                    >
                      Offered
                    </Menu.Item>
                    <Menu.Item
                      leftSection={<FaUserCheck size={14} />}
                      onClick={() => updateStatus("hired")}
                      disabled={candidate.status.toLowerCase() === "hired"}
                    >
                      Hired
                    </Menu.Item>
                    <Menu.Item
                      leftSection={<FaTimes size={14} />}
                      onClick={() => updateStatus("rejected")}
                      disabled={candidate.status.toLowerCase() === "rejected"}
                    >
                      Rejected
                    </Menu.Item>
                  </Menu.Dropdown>
                </Menu>
              </Menu.Item>
              <Menu.Item leftSection={<FaEnvelope size={14} />}>
                Send Email
              </Menu.Item>
              <Menu.Item leftSection={<FaPhoneAlt size={14} />}>
                Call Candidate
              </Menu.Item>
            </Menu.Dropdown>
          </Menu>
        </Group>
      </Group>
    </Card>
  );
}
