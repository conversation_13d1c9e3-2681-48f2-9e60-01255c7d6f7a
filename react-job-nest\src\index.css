@import "tailwindcss";

@theme {
  /* Primary color - blue shade */
  --color-primary-color: #1890ff;
  --color-primary-50: #e6f7ff;
  --color-primary-100: #bae7ff;
  --color-primary-200: #91d5ff;
  --color-primary-300: #69c0ff;
  --color-primary-400: #40a9ff;
  --color-primary-500: #1890ff;
  --color-primary-600: #096dd9;
  --color-primary-700: #0050b3;
  --color-primary-800: #003a8c;
  --color-primary-900: #002766;

  /* Secondary color - green shade */
  --color-secondary-50: #e6fffb;
  --color-secondary-100: #b5f5ec;
  --color-secondary-200: #87e8de;
  --color-secondary-300: #5cdbd3;
  --color-secondary-400: #36cfc9;
  --color-secondary-500: #13c2c2;
  --color-secondary-600: #08979c;
  --color-secondary-700: #006d75;
  --color-secondary-800: #00474f;
  --color-secondary-900: #002329;

  /* Accent color - purple shade */
  --color-accent-50: #f9f0ff;
  --color-accent-100: #efdbff;
  --color-accent-200: #d3adf7;
  --color-accent-300: #b37feb;
  --color-accent-400: #9254de;
  --color-accent-500: #722ed1;
  --color-accent-600: #531dab;
  --color-accent-700: #391085;
  --color-accent-800: #22075e;
  --color-accent-900: #120338;

  /* Breakpoints */
  --breakpoint-sm: 576px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 992px;
  --breakpoint-xl: 1200px;
  --breakpoint-2xl: 1400px;
}

.container {
  @apply mx-auto px-8;
}

@layer base {
  * {
    border-color: #e8e8e8; /* Using our gray-200 color */
  }
  textarea {
    @apply !h-[120px];
  }

  /* Add base typography styles */
  html {
    font-family:
      "Inter",
      -apple-system,
      BlinkMacSystemFont,
      "Segoe UI",
      Roboto,
      sans-serif;
  }

  /* Use our theme colors for default elements */
  a {
    color: var(--color-primary-500);
  }

  a:hover {
    color: var(--color-primary-600);
  }
}

/* Hide mobile menu button on desktop during initial render */
@media (min-width: 1200px) {
  .js-loading .mobile-menu-button {
    display: none !important;
  }
}

/* Add transition for mobile menu button */
.mobile-menu-button {
  transition: opacity 0.3s ease-in-out;
}
