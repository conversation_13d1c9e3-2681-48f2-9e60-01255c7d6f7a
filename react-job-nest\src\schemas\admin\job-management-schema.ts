import { z } from "zod";

export const jobFilterSchema = z.object({
  search: z.string().optional(),
  status: z
    .enum(["all", "active", "pending", "expired", "rejected"])
    .optional(),
  company: z.string().optional(),
  category: z.string().optional(),
  sortBy: z.enum(["title", "company", "createdAt", "status"]).optional(),
  sortOrder: z.enum(["asc", "desc"]).optional(),
});

export const jobStatusUpdateSchema = z.object({
  status: z.enum(["active", "pending", "expired", "rejected"]),
  rejectionReason: z.string().optional(),
});
