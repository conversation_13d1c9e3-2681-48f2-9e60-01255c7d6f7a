"use client";

import { useThemeClasses } from "@/design-system/utils/theme-utils";
import useCreateJobForm from "@/hooks/employer/use-create-job-form";
import {
  ActionIcon,
  Box,
  Button,
  Card,
  Divider,
  FileInput,
  MultiSelect,
  NumberInput,
  Select,
  Stepper,
  Switch,
  TagsInput,
  Text,
  TextInput,
  Textarea,
  Tooltip,
} from "@mantine/core";
import { DatePickerInput } from "@mantine/dates";
import { notifications } from "@mantine/notifications";
import { useState } from "react";
import {
  FaArrowLeft,
  FaArrowRight,
  FaBriefcase,
  FaCalendar,
  FaCheck,
  FaClipboardCheck,
  FaDollarSign,
  FaFileAlt,
  FaGift,
  FaInfoCircle,
  FaMapMarkerAlt,
  FaQuestionCircle,
  FaTags,
  FaTrash,
} from "react-icons/fa";

const predefinedQuestions = [
  "Why do you want to work for our company?",
  "What are your strengths and weaknesses?",
  "Where do you see yourself in 5 years?",
  "Describe a challenging situation you faced and how you handled it.",
];

const jobCategories = [
  { value: "software", label: "Software Development" },
  { value: "web-development", label: "Web Development" },
  { value: "mobile-development", label: "Mobile Development" },
  { value: "data-science", label: "Data Science" },
  { value: "ai-ml", label: "Artificial Intelligence & Machine Learning" },
  { value: "devops", label: "DevOps" },
  { value: "cloud-computing", label: "Cloud Computing" },
  { value: "cybersecurity", label: "Cybersecurity" },
  { value: "it-support", label: "IT Support" },
  { value: "networking", label: "Networking" },
  { value: "ui-ux-design", label: "UI/UX Design" },
  { value: "graphic-design", label: "Graphic Design" },
  { value: "marketing", label: "Marketing" },
  { value: "digital-marketing", label: "Digital Marketing" },
  { value: "content-writing", label: "Content Writing" },
  { value: "social-media", label: "Social Media Management" },
  { value: "sales", label: "Sales" },
  { value: "customer-support", label: "Customer Support" },
  { value: "finance", label: "Finance" },
  { value: "accounting", label: "Accounting" },
  { value: "human-resources", label: "Human Resources" },
  { value: "project-management", label: "Project Management" },
  { value: "product-management", label: "Product Management" },
  { value: "business-development", label: "Business Development" },
  { value: "consulting", label: "Consulting" },
  { value: "other", label: "Other" },
];

export default function CreateJobForm() {
  const { form, handleSubmit, isSubmitting, company } = useCreateJobForm();
  const [questions, setQuestions] = useState<string[]>([""]);
  const [active, setActive] = useState(0);

  // Debug function to check auth and company state
  const debugState = () => {
    console.log("Company data:", company);
    console.log("Form values:", form.values);
    console.log("Form errors:", form.errors);
    console.log("Is submitting:", isSubmitting);
  };

  const handleQuestionChange = (index: number, value: string) => {
    const newQuestions = [...questions];
    newQuestions[index] = value;
    setQuestions(newQuestions);
  };

  const addQuestion = () => {
    setQuestions([...questions, ""]);
  };

  const removeQuestion = (index: number) => {
    const newQuestions = questions.filter((_, i) => i !== index);
    setQuestions(newQuestions);
  };

  const addPredefinedQuestion = (question: string) => {
    setQuestions([...questions, question]);
  };

  const nextStep = () => {
    // Validate current step before proceeding
    let isValid = true;

    if (active === 0) {
      // Validate basic information step
      const fieldsToValidate = [
        "jobTitle",
        "requiredExperience",
        "jobCategory",
        "location",
        "jobType",
      ];
      fieldsToValidate.forEach((field) => {
        const result = form.validateField(field);
        if (result.hasError) {
          isValid = false;
        }
      });
    } else if (active === 1) {
      // Validate description step
      const fieldsToValidate = ["jobDescription", "jobRequirements"];
      fieldsToValidate.forEach((field) => {
        const result = form.validateField(field);
        if (result.hasError) {
          isValid = false;
        }
      });
    }

    if (isValid && active < 3) {
      setActive((current) => current + 1);
      window.scrollTo({ top: 0, behavior: "smooth" });
    }
  };

  const prevStep = () => {
    if (active > 0) {
      setActive((current) => current - 1);
      window.scrollTo({ top: 0, behavior: "smooth" });
    }
  };

  const onSubmitForm = async (values: any) => {
    try {
      // Add questions to the form values
      const formValues = {
        ...values,
        questions: questions.filter((q) => q.trim() !== ""),
      };

      console.log("Form submitted:", formValues);

      // Validate required fields
      const requiredFields = [
        "jobTitle",
        "jobType",
        "location",
        "jobCategory",
        "jobDescription",
        "jobRequirements",
        "requiredExperience",
      ];

      const missingFields = requiredFields.filter((field) => !values[field]);

      if (missingFields.length > 0) {
        console.error("Missing required fields:", missingFields);

        // Show notification for missing fields
        notifications.show({
          title: "Missing Required Fields",
          message: `Please fill in all required fields: ${missingFields.join(", ")}`,
          color: "red",
          autoClose: 5000,
        });

        // Focus on the first missing field
        form.setFieldError(missingFields[0], "This field is required");
        return;
      }

      // Validate and convert date field specifically
      if (values.applicationDeadline) {
        try {
          // If it's a string, try to convert it to a Date object
          if (typeof values.applicationDeadline === "string") {
            const dateObj = new Date(values.applicationDeadline);

            // Check if the date is valid
            if (!isNaN(dateObj.getTime())) {
              // Replace the string with a valid Date object
              formValues.applicationDeadline = dateObj;
              console.log("Converted string date to Date object:", dateObj);
            } else {
              throw new Error("Invalid date format");
            }
          } else if (!(values.applicationDeadline instanceof Date)) {
            throw new Error("Invalid date type");
          }
        } catch (error) {
          console.error("Invalid date format for application deadline:", error);
          notifications.show({
            title: "Date Format Error",
            message: "Please select a valid date for Application Deadline",
            color: "red",
            autoClose: 5000,
          });
          form.setFieldError(
            "applicationDeadline",
            "Expected date, received invalid format",
          );
          return;
        }
      }

      // Check if company exists
      if (!company) {
        console.error("No company found. Please create a company first.");
        notifications.show({
          title: "Company Required",
          message: "You need to create a company profile first",
          color: "red",
          autoClose: 5000,
        });
        return;
      }

      console.log("Submitting job with company ID:", company._id);

      // Call the handleSubmit function from the hook
      await handleSubmit(formValues);
    } catch (error) {
      console.error("Error in form submission:", error);
      notifications.show({
        title: "Submission Error",
        message:
          error instanceof Error ? error.message : "An unknown error occurred",
        color: "red",
        autoClose: 5000,
      });
    }
  };

  // Handle form submission
  const handleFormSubmit = (e: React.FormEvent) => {
    e.preventDefault(); // Prevent default form submission

    if (!company) {
      console.error("No company found. Please create a company first.");
      notifications.show({
        title: "Error",
        message: "You need to create a company profile first",
        color: "red",
      });
      return;
    }

    // Pre-process the form values before validation
    const formValues = { ...form.values };

    // Handle date conversion before validation
    if (formValues.applicationDeadline) {
      try {
        // If it's a string, try to convert it to a Date object
        if (typeof formValues.applicationDeadline === "string") {
          const dateObj = new Date(formValues.applicationDeadline);
          if (!isNaN(dateObj.getTime())) {
            form.setFieldValue("applicationDeadline", dateObj);
            console.log(
              "Converted date string to Date object before validation",
            );
          } else {
            throw new Error("Invalid date format");
          }
        }
      } catch (error) {
        console.error("Date conversion error:", error);
        form.setFieldError(
          "applicationDeadline",
          "Expected date, received invalid format",
        );

        notifications.show({
          title: "Date Format Error",
          message:
            "Please select a valid date from the calendar for Application Deadline",
          color: "red",
          autoClose: 5000,
        });

        // Set focus to the date field
        setTimeout(() => {
          const dateField = document.querySelector(
            '[aria-label="Application Deadline"]',
          );
          if (dateField) {
            (dateField as HTMLElement).focus();
          }
        }, 100);

        return;
      }
    }

    // Validate the form
    const validationResult = form.validate();
    console.log("Form validation result:", validationResult);

    if (validationResult.hasErrors) {
      console.error("Form validation errors:", validationResult.errors);

      // Show notification with validation errors
      const errorMessages = Object.entries(validationResult.errors)
        .map(([field, error]) => `${field}: ${error}`)
        .join("\n");

      notifications.show({
        title: "Validation Error",
        message:
          errorMessages || "Please fix the form errors before submitting",
        color: "red",
        autoClose: 5000,
      });

      // Check specifically for date validation errors
      if (validationResult.errors.applicationDeadline) {
        const dateError = validationResult.errors.applicationDeadline;

        if (typeof dateError === "string") {
          let errorMessage =
            "Please select a valid date for Application Deadline";

          if (dateError.includes("Expected date")) {
            errorMessage =
              "Please select a valid date from the calendar for Application Deadline";
          } else if (dateError.includes("past")) {
            errorMessage = "Application deadline cannot be in the past";
          }

          notifications.show({
            title: "Date Format Error",
            message: errorMessage,
            color: "red",
            autoClose: 5000,
          });

          // Set focus to the date field
          setTimeout(() => {
            const dateField = document.querySelector(
              '[aria-label="Application Deadline"]',
            );
            if (dateField) {
              (dateField as HTMLElement).focus();
            }
          }, 100);
        }
      }

      return;
    }

    console.log("Form is valid, submitting...");
    // If validation passes, proceed with form submission
    onSubmitForm(form.values);
  };

  return (
    <form
      noValidate
      onSubmit={handleFormSubmit}
      className="flex flex-col gap-8"
    >
      {/* Stepper */}
      <Card
        shadow="sm"
        radius="md"
        className={useThemeClasses(
          "border border-gray-200",
          "border border-dark-4",
        )}
      >
        <Stepper
          active={active}
          onStepClick={setActive}
          orientation="horizontal"
          color="blue"
          size="md"
          iconSize={32}
          className="px-2 py-4 md:px-4"
          styles={{
            stepBody: {
              marginTop: "8px",
            },
            step: {
              padding: "12px 8px",
              transition: "all 0.2s ease",
            },
            stepIcon: {
              borderWidth: "2px",
              transition: "all 0.2s ease",
            },
            separator: {
              marginLeft: "4px",
              marginRight: "4px",
              height: "2px",
            },
          }}
        >
          <Stepper.Step
            label="Basic Information"
            description="Job details"
            icon={<FaBriefcase size={18} />}
            completedIcon={<FaCheck size={18} />}
          />
          <Stepper.Step
            label="Description"
            description="Job description & requirements"
            icon={<FaFileAlt size={18} />}
            completedIcon={<FaCheck size={18} />}
          />
          <Stepper.Step
            label="Additional Details"
            description="Benefits & perks"
            icon={<FaGift size={18} />}
            completedIcon={<FaCheck size={18} />}
          />
          <Stepper.Step
            label="Review & Submit"
            description="Preview and publish"
            icon={<FaClipboardCheck size={18} />}
            completedIcon={<FaCheck size={18} />}
          />
        </Stepper>
      </Card>

      {/* Step Content */}
      <Card
        shadow="sm"
        radius="md"
        className={useThemeClasses(
          "border border-gray-200 p-0",
          "border border-dark-4 p-0",
        )}
      >
        <div className="p-6 md:p-8">
          {active === 0 && <BasicInformationStep form={form} />}
          {active === 1 && <DescriptionStep form={form} />}
          {active === 2 && (
            <AdditionalDetailsStep
              form={form}
              questions={questions}
              handleQuestionChange={handleQuestionChange}
              addQuestion={addQuestion}
              removeQuestion={removeQuestion}
              addPredefinedQuestion={addPredefinedQuestion}
              predefinedQuestions={predefinedQuestions}
            />
          )}
          {active === 3 && <ReviewStep form={form} />}
        </div>
      </Card>

      {/* Navigation Buttons */}
      <Card
        shadow="sm"
        radius="md"
        className={useThemeClasses(
          "border border-gray-200",
          "border border-dark-4",
        )}
      >
        <div className="flex items-center justify-between p-4">
          {active > 0 ? (
            <Button
              variant="outline"
              onClick={prevStep}
              leftSection={<FaArrowLeft size={14} />}
              radius="md"
              color="gray"
              className="transition-all duration-200"
            >
              Previous Step
            </Button>
          ) : (
            <div></div>
          )}

          <div className="flex gap-2">
            {process.env.NODE_ENV === "development" && (
              <Button
                variant="outline"
                color="gray"
                onClick={debugState}
                size="md"
              >
                Debug
              </Button>
            )}

            {active < 3 ? (
              <Button
                onClick={nextStep}
                rightSection={<FaArrowRight size={14} />}
                radius="md"
                color="primary"
                className="transition-all duration-200"
              >
                Next Step
              </Button>
            ) : (
              <Button
                type="submit"
                color="teal"
                rightSection={<FaCheck size={14} />}
                radius="md"
                className="transition-all duration-200"
                size="md"
                loading={isSubmitting}
                disabled={isSubmitting || !company}
              >
                {!company ? "Create Company First" : "Submit Job"}
              </Button>
            )}
          </div>
        </div>
      </Card>
    </form>
  );
}

// Step 1: Basic Information
function BasicInformationStep({ form }: { form: any }) {
  return (
    <div className="space-y-8">
      <div
        className={useThemeClasses(
          "border-b border-gray-200 pb-4",
          "border-b border-dark-4 pb-4",
        )}
      >
        <div className="flex flex-wrap items-center justify-between gap-2">
          <div className="flex items-center gap-2">
            <div
              className={useThemeClasses(
                "flex h-8 w-8 items-center justify-center rounded-full bg-blue-100 text-blue-600",
                "flex h-8 w-8 items-center justify-center rounded-full bg-blue-900/30 text-blue-300",
              )}
            >
              <FaBriefcase size={16} />
            </div>
            <Text
              className={useThemeClasses(
                "text-xl font-semibold text-gray-800",
                "text-xl font-semibold text-gray-100",
              )}
            >
              Basic Job Information
            </Text>
          </div>
          <Text
            size="sm"
            className={useThemeClasses(
              "rounded-md bg-gray-100 px-2 py-1 text-gray-600",
              "rounded-md bg-dark-5 px-2 py-1 text-gray-300",
            )}
          >
            * Required fields
          </Text>
        </div>
        <Text size="sm" c="dimmed" mt="xs">
          Enter the core details about the position you&apos;re offering
        </Text>
      </div>

      <div className="grid grid-cols-1 gap-x-8 gap-y-6 md:grid-cols-2">
        <div>
          <TextInput
            label={
              <div className="flex items-center gap-1">
                <Text className="font-medium">Job Title</Text>
                <Text c="red" span>
                  *
                </Text>
                <Tooltip label="Be specific with the job title to attract the right candidates">
                  <FaInfoCircle
                    size={14}
                    className="cursor-help text-gray-400"
                  />
                </Tooltip>
              </div>
            }
            placeholder="e.g. Senior React Developer"
            leftSection={
              <FaBriefcase size={18} className="text-primary-color" />
            }
            {...form.getInputProps("jobTitle")}
            error={form.errors.jobTitle}
            aria-label="Job Title"
            size="md"
            className="mb-4"
            radius="md"
          />

          <Select
            label={
              <div className="flex items-center gap-1">
                <Text>Required Experience</Text>
                <Text c="red" span>
                  *
                </Text>
              </div>
            }
            placeholder="Select required experience"
            data={[
              { value: "internship", label: "Internship" },
              { value: "1-2 years", label: "1-2 years" },
              { value: "3-5 years", label: "3-5 years" },
              { value: "5-10 years", label: "5-10 years" },
              { value: "10+ years", label: "10+ years" },
            ]}
            leftSection={
              <FaBriefcase size={18} className="text-primary-color" />
            }
            {...form.getInputProps("requiredExperience")}
            error={form.errors.requiredExperience}
            aria-label="Required Experience"
            size="md"
            className="mb-4"
          />

          <Select
            label={
              <div className="flex items-center gap-1">
                <Text>Job Category</Text>
                <Text c="red" span>
                  *
                </Text>
              </div>
            }
            placeholder="Select job category"
            data={jobCategories}
            leftSection={<FaTags size={18} className="text-primary-color" />}
            {...form.getInputProps("jobCategory")}
            error={form.errors.jobCategory}
            aria-label="Job Category"
            size="md"
            searchable
          />
        </div>

        <div
          className={useThemeClasses(
            "rounded-lg bg-blue-50 p-6",
            "rounded-lg bg-dark-6 p-6",
          )}
        >
          <Text className="text-primary-color mb-3 text-lg font-medium">
            Salary Information
          </Text>

          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <NumberInput
              label="Minimum Salary"
              placeholder="Enter minimum"
              min={0}
              step={100}
              leftSection={
                <FaDollarSign size={18} className="text-primary-color" />
              }
              {...form.getInputProps("minSalary")}
              error={form.errors.minSalary}
              aria-label="Minimum Salary"
              size="md"
            />
            <NumberInput
              label="Maximum Salary"
              placeholder="Enter maximum"
              min={0}
              step={100}
              leftSection={
                <FaDollarSign size={18} className="text-primary-color" />
              }
              {...form.getInputProps("maxSalary")}
              error={form.errors.maxSalary}
              aria-label="Maximum Salary"
              size="md"
            />
          </div>

          <Select
            label="Currency"
            placeholder="Select currency"
            data={[
              { value: "EGP", label: "EGP (Egyptian Pound)" },
              { value: "SAR", label: "SAR (Saudi Riyal)" },
              { value: "USD", label: "USD (US Dollar)" },
              { value: "EUR", label: "EUR (Euro)" },
              { value: "GBP", label: "GBP (British Pound)" },
            ]}
            leftSection={
              <FaDollarSign size={18} className="text-primary-color" />
            }
            {...form.getInputProps("currency")}
            error={form.errors.currency}
            aria-label="Currency"
            size="md"
            className="mt-4"
          />

          <Switch
            label={
              <div className="flex items-center gap-1">
                <Text>Show Salary Range to Applicants</Text>
                <Tooltip label="Showing salary range can increase application rates by up to 30%">
                  <FaInfoCircle
                    size={14}
                    className={useThemeClasses(
                      "cursor-help text-gray-400",
                      "cursor-help text-gray-500",
                    )}
                  />
                </Tooltip>
              </div>
            }
            {...form.getInputProps("showSalary")}
            error={form.errors.showSalary}
            aria-label="Show Salary Range"
            className="mt-4"
            size="md"
          />
        </div>
      </div>

      <Divider my="lg" />

      <Box className="mt-6">
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <Select
            label={
              <div className="flex items-center gap-1">
                <Text>Location</Text>
                <Text c="red" span>
                  *
                </Text>
              </div>
            }
            placeholder="Select location"
            data={[
              { value: "remote", label: "Remote" },
              { value: "onsite", label: "Onsite" },
              { value: "hybrid", label: "Hybrid" },
            ]}
            leftSection={
              <FaMapMarkerAlt size={18} className="text-primary-color" />
            }
            {...form.getInputProps("location")}
            error={form.errors.location}
            aria-label="Location"
            size="md"
          />
          <Select
            label={
              <div className="flex items-center gap-1">
                <Text>Job Type</Text>
                <Text c="red" span>
                  *
                </Text>
              </div>
            }
            placeholder="Select job type"
            data={[
              { value: "full-time", label: "Full Time" },
              { value: "part-time", label: "Part Time" },
              { value: "internship", label: "Internship" },
              { value: "contract", label: "Contract" },
              { value: "temporary", label: "Temporary" },
            ]}
            leftSection={
              <FaBriefcase size={18} className="text-primary-color" />
            }
            {...form.getInputProps("jobType")}
            error={form.errors.jobType}
            aria-label="Job Type"
            size="md"
          />
        </div>
      </Box>
    </div>
  );
}

// Step 2: Description
function DescriptionStep({ form }: { form: any }) {
  return (
    <div className="space-y-8">
      <div
        className={useThemeClasses(
          "border-b border-gray-200 pb-4",
          "border-b border-dark-4 pb-4",
        )}
      >
        <div className="flex flex-wrap items-center justify-between gap-2">
          <div className="flex items-center gap-2">
            <div
              className={useThemeClasses(
                "flex h-8 w-8 items-center justify-center rounded-full bg-blue-100 text-blue-600",
                "flex h-8 w-8 items-center justify-center rounded-full bg-blue-900/30 text-blue-300",
              )}
            >
              <FaFileAlt size={16} />
            </div>
            <Text
              className={useThemeClasses(
                "text-xl font-semibold text-gray-800",
                "text-xl font-semibold text-gray-100",
              )}
            >
              Job Description & Requirements
            </Text>
          </div>
          <Text
            size="sm"
            className={useThemeClasses(
              "rounded-md bg-gray-100 px-2 py-1 text-gray-600",
              "rounded-md bg-dark-5 px-2 py-1 text-gray-300",
            )}
          >
            * Required fields
          </Text>
        </div>
        <Text size="sm" c="dimmed" mt="xs">
          Provide detailed information about the job responsibilities and
          requirements
        </Text>
      </div>

      <Textarea
        label={
          <div className="flex items-center gap-1">
            <Text className="font-medium">Job Description</Text>
            <Text c="red" span>
              *
            </Text>
            <Tooltip label="Describe the role, responsibilities, and what a typical day looks like">
              <FaInfoCircle size={14} className="cursor-help text-gray-400" />
            </Tooltip>
          </div>
        }
        placeholder="Enter a detailed job description..."
        rows={8}
        {...form.getInputProps("jobDescription")}
        error={form.errors.jobDescription}
        aria-label="Job Description"
        size="md"
        className="mb-6"
        radius="md"
      />

      <Textarea
        label={
          <div className="flex items-center gap-1">
            <Text>Job Requirements</Text>
            <Text c="red" span>
              *
            </Text>
            <Tooltip label="List skills, qualifications, and experience needed for this role">
              <FaInfoCircle size={14} className="cursor-help text-gray-400" />
            </Tooltip>
          </div>
        }
        placeholder="Enter job requirements..."
        rows={8}
        {...form.getInputProps("jobRequirements")}
        error={form.errors.jobRequirements}
        aria-label="Job Requirements"
        size="md"
      />

      <Divider my="lg" />

      <Box className="mt-6">
        <Text className="text-primary-color mb-3 text-lg font-semibold">
          Tags & Keywords
        </Text>
        <Text size="sm" c="dimmed" mb="md">
          Add relevant tags to help candidates find your job posting
        </Text>

        <TagsInput
          label="Job Tags"
          placeholder="Add tags (e.g., React, Python, Marketing) and press Enter"
          leftSection={<FaTags size={18} className="text-primary-color" />}
          {...form.getInputProps("jobTags")}
          error={form.errors.jobTags}
          aria-label="Job Tags"
          size="md"
        />
      </Box>
    </div>
  );
}

// Step 3: Additional Details
function AdditionalDetailsStep({
  form,
  questions,
  handleQuestionChange,
  addQuestion,
  removeQuestion,
  addPredefinedQuestion,
  predefinedQuestions,
}: {
  form: any;
  questions: string[];
  handleQuestionChange: (index: number, value: string) => void;
  addQuestion: () => void;
  removeQuestion: (index: number) => void;
  addPredefinedQuestion: (question: string) => void;
  predefinedQuestions: string[];
}) {
  return (
    <div className="space-y-8">
      <div
        className={useThemeClasses(
          "border-b border-gray-200 pb-4",
          "border-b border-dark-4 pb-4",
        )}
      >
        <div className="flex flex-wrap items-center justify-between gap-2">
          <div className="flex items-center gap-2">
            <div
              className={useThemeClasses(
                "flex h-8 w-8 items-center justify-center rounded-full bg-blue-100 text-blue-600",
                "flex h-8 w-8 items-center justify-center rounded-full bg-blue-900/30 text-blue-300",
              )}
            >
              <FaGift size={16} />
            </div>
            <Text
              className={useThemeClasses(
                "text-xl font-semibold text-gray-800",
                "text-xl font-semibold text-gray-100",
              )}
            >
              Benefits & Perks
            </Text>
          </div>
        </div>
        <Text size="sm" c="dimmed" mt="xs">
          Highlight the benefits and perks that come with this position
        </Text>
      </div>

      <MultiSelect
        label={
          <div className="flex items-center gap-1">
            <Text className="font-medium">Benefits and Perks</Text>
            <Tooltip label="Highlight the benefits that make your company stand out">
              <FaInfoCircle size={14} className="cursor-help text-gray-400" />
            </Tooltip>
          </div>
        }
        placeholder="Select benefits and perks"
        data={[
          { value: "health-insurance", label: "Health Insurance" },
          { value: "dental-insurance", label: "Dental Insurance" },
          { value: "vision-insurance", label: "Vision Insurance" },
          { value: "remote-work", label: "Remote Work" },
          { value: "flexible-hours", label: "Flexible Hours" },
          { value: "paid-time-off", label: "Paid Time Off" },
          { value: "bonuses", label: "Bonuses" },
          { value: "stock-options", label: "Stock Options" },
          { value: "retirement-plan", label: "Retirement Plan" },
          { value: "parental-leave", label: "Parental Leave" },
          {
            value: "professional-development",
            label: "Professional Development",
          },
          { value: "gym-membership", label: "Gym Membership" },
          { value: "free-lunch", label: "Free Lunch/Meals" },
          { value: "company-events", label: "Company Events" },
        ]}
        leftSection={<FaGift size={18} className="text-primary-color" />}
        {...form.getInputProps("benefits")}
        error={form.errors.benefits}
        aria-label="Benefits and Perks"
        searchable
        clearable
        size="md"
        radius="md"
        className="mb-4"
      />

      <Divider my="lg" />

      <div
        className={useThemeClasses(
          "border-t border-gray-200 pt-6",
          "border-t border-dark-4 pt-6",
        )}
      >
        <div className="mb-4 flex items-center gap-2">
          <div
            className={useThemeClasses(
              "flex h-8 w-8 items-center justify-center rounded-full bg-blue-100 text-blue-600",
              "flex h-8 w-8 items-center justify-center rounded-full bg-blue-900/30 text-blue-300",
            )}
          >
            <FaCalendar size={16} />
          </div>
          <Text
            className={useThemeClasses(
              "text-xl font-semibold text-gray-800",
              "text-xl font-semibold text-gray-100",
            )}
          >
            Application Details
          </Text>
        </div>

        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          <DatePickerInput
            label={
              <div className="flex items-center gap-1">
                <Text className="font-medium">Application Deadline</Text>
                <Tooltip label="The last day candidates can apply for this position">
                  <FaInfoCircle
                    size={14}
                    className="cursor-help text-gray-400"
                  />
                </Tooltip>
              </div>
            }
            placeholder="Select deadline"
            leftSection={
              <FaCalendar size={18} className="text-primary-color" />
            }
            error={form.errors.applicationDeadline}
            aria-label="Application Deadline"
            size="md"
            radius="md"
            clearable
            valueFormat="YYYY-MM-DD" // Ensure consistent date format
            minDate={new Date()} // Prevent selecting dates in the past
            value={
              form.values.applicationDeadline instanceof Date
                ? form.values.applicationDeadline
                : null
            }
            onChange={(date) => {
              console.log("Date selected:", date, "Type:", typeof date);
              // Ensure we're setting a valid Date object
              if (date === null) {
                form.setFieldValue("applicationDeadline", undefined);
              } else {
                // Ensure it's a proper Date object
                try {
                  // Try to convert to a Date object if needed
                  const dateObj = new Date(date as any);
                  if (!isNaN(dateObj.getTime())) {
                    form.setFieldValue("applicationDeadline", dateObj);
                    console.log("Set date value:", dateObj);
                  } else {
                    console.error("Invalid date value:", date);
                    form.setFieldError(
                      "applicationDeadline",
                      "Invalid date format",
                    );
                  }
                } catch (error) {
                  console.error("Error setting date:", error);
                  form.setFieldError(
                    "applicationDeadline",
                    "Invalid date format",
                  );
                }
              }
            }}
          />
          <FileInput
            label={
              <div className="flex items-center gap-1">
                <Text className="font-medium">Job Attachment (Optional)</Text>
                <Tooltip label="Upload additional documents like detailed job description">
                  <FaInfoCircle
                    size={14}
                    className="cursor-help text-gray-400"
                  />
                </Tooltip>
              </div>
            }
            placeholder="Upload job description document"
            accept=".pdf,.doc,.docx"
            leftSection={<FaFileAlt size={18} className="text-primary-color" />}
            {...form.getInputProps("jobAttachment")}
            error={form.errors.jobAttachment}
            aria-label="Job Attachment"
            size="md"
            radius="md"
            clearable
          />
        </div>
      </div>

      <Divider my="lg" />

      <div
        className={useThemeClasses(
          "border-t border-gray-200 pt-6",
          "border-t border-dark-4 pt-6",
        )}
      >
        <div className="mb-4 flex flex-wrap items-center justify-between gap-3">
          <div className="flex items-center gap-2">
            <div
              className={useThemeClasses(
                "flex h-8 w-8 items-center justify-center rounded-full bg-blue-100 text-blue-600",
                "flex h-8 w-8 items-center justify-center rounded-full bg-blue-900/30 text-blue-300",
              )}
            >
              <FaQuestionCircle size={16} />
            </div>
            <Text
              className={useThemeClasses(
                "text-xl font-semibold text-gray-800",
                "text-xl font-semibold text-gray-100",
              )}
            >
              Screening Questions
            </Text>
          </div>
          <Button
            size="sm"
            variant="light"
            color="blue"
            onClick={addQuestion}
            leftSection={<FaQuestionCircle size={14} />}
            radius="md"
            className={useThemeClasses(
              "transition-all duration-200 hover:bg-blue-100",
              "transition-all duration-200 hover:bg-blue-900/30",
            )}
          >
            Add Question
          </Button>
        </div>
        <Text size="sm" c="dimmed" mb="md">
          Add questions to pre-screen candidates during the application process
        </Text>

        <Card
          withBorder
          radius="md"
          className={useThemeClasses("mb-6 bg-gray-50/50", "mb-6 bg-dark-6")}
        >
          <Select
            label={
              <div className="flex items-center gap-1">
                <Text className="font-medium">Predefined Questions</Text>
                <Tooltip label="Choose from common screening questions">
                  <FaInfoCircle
                    size={14}
                    className={useThemeClasses(
                      "cursor-help text-gray-400",
                      "cursor-help text-gray-500",
                    )}
                  />
                </Tooltip>
              </div>
            }
            placeholder="Select a predefined question to add"
            data={predefinedQuestions.map((question) => ({
              value: question,
              label: question,
            }))}
            onChange={(value) => {
              if (value) {
                addPredefinedQuestion(value);
              }
            }}
            size="md"
            radius="md"
            searchable
            clearable
          />
        </Card>

        {questions.length > 0 ? (
          <div className="space-y-4">
            {questions.map((question, index) => (
              <div key={index} className="flex items-end gap-3">
                <TextInput
                  label={
                    <div className="flex items-center gap-1">
                      <Text className="font-medium">Question {index + 1}</Text>
                    </div>
                  }
                  placeholder="Enter a question for candidates to answer"
                  value={question}
                  onChange={(event) =>
                    handleQuestionChange(index, event.currentTarget.value)
                  }
                  className="flex-1"
                  size="md"
                  radius="md"
                />
                <ActionIcon
                  color="red"
                  variant="light"
                  onClick={() => removeQuestion(index)}
                  className="mb-1"
                  size="lg"
                  radius="md"
                >
                  <FaTrash size={16} />
                </ActionIcon>
              </div>
            ))}
          </div>
        ) : (
          <Text
            size="sm"
            c="dimmed"
            className={useThemeClasses("py-4 text-center", "py-4 text-center")}
          >
            No screening questions added yet. Add questions to help identify the
            best candidates.
          </Text>
        )}
      </div>
    </div>
  );
}

// Step 4: Review
function ReviewStep({ form }: { form: any }) {
  // Use form values or fallback to dummy data for preview
  const formValues = form.values;

  // Dummy data for preview
  const dummyData = {
    jobTitle: "Senior Frontend Developer",
    requiredExperience: "3-5 years",
    location: "Remote",
    jobType: "Full Time",
    jobCategory: "Web Development",
    minSalary: 80000,
    maxSalary: 120000,
    currency: "USD",
    showSalary: true,
    jobDescription:
      "We are looking for a skilled Senior Frontend Developer to join our growing team. You will be responsible for building user interfaces using React, Next.js, and modern CSS frameworks. You'll work closely with designers, backend developers, and product managers to deliver exceptional user experiences.",
    jobRequirements:
      "• 3+ years of experience with React and modern JavaScript\n• Experience with Next.js and TypeScript\n• Strong understanding of responsive design principles\n• Experience with state management libraries (Redux, Zustand, etc.)\n• Excellent problem-solving skills and attention to detail\n• Good communication skills and ability to work in a team",
    benefits: [
      "Health Insurance",
      "Dental Insurance",
      "Remote Work",
      "Flexible Hours",
      "Paid Time Off",
      "Professional Development",
    ],
    jobTags: ["React", "Next.js", "TypeScript", "Frontend", "UI/UX"],
    applicationDeadline: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
  };

  // Use form values if available, otherwise use dummy data
  const displayData = {
    jobTitle: formValues.jobTitle || dummyData.jobTitle,
    requiredExperience:
      formValues.requiredExperience || dummyData.requiredExperience,
    location: formValues.location || dummyData.location,
    jobType: formValues.jobType || dummyData.jobType,
    jobCategory: formValues.jobCategory || dummyData.jobCategory,
    minSalary: formValues.minSalary || dummyData.minSalary,
    maxSalary: formValues.maxSalary || dummyData.maxSalary,
    currency: formValues.currency || dummyData.currency,
    showSalary:
      formValues.showSalary !== undefined
        ? formValues.showSalary
        : dummyData.showSalary,
    jobDescription: formValues.jobDescription || dummyData.jobDescription,
    jobRequirements: formValues.jobRequirements || dummyData.jobRequirements,
    benefits:
      formValues.benefits?.length > 0
        ? formValues.benefits
        : dummyData.benefits,
    jobTags:
      formValues.jobTags?.length > 0 ? formValues.jobTags : dummyData.jobTags,
    applicationDeadline:
      formValues.applicationDeadline || dummyData.applicationDeadline,
  };

  return (
    <div className="space-y-8">
      <div
        className={useThemeClasses(
          "border-b border-gray-200 pb-4",
          "border-b border-dark-4 pb-4",
        )}
      >
        <div className="flex flex-wrap items-center justify-between gap-2">
          <div className="flex items-center gap-2">
            <div
              className={useThemeClasses(
                "flex h-8 w-8 items-center justify-center rounded-full bg-blue-100 text-blue-600",
                "flex h-8 w-8 items-center justify-center rounded-full bg-blue-900/30 text-blue-300",
              )}
            >
              <FaClipboardCheck size={16} />
            </div>
            <Text
              className={useThemeClasses(
                "text-xl font-semibold text-gray-800",
                "text-xl font-semibold text-gray-100",
              )}
            >
              Review Your Job Posting
            </Text>
          </div>
        </div>
        <Text size="sm" c="dimmed" mt="xs">
          Please review all information before submitting
        </Text>
      </div>

      {/* Job Preview Card */}
      <div
        className={useThemeClasses(
          "overflow-hidden rounded-lg border border-gray-200 bg-white shadow-sm",
          "overflow-hidden rounded-lg border border-dark-4 bg-dark-7 shadow-dark-lg",
        )}
      >
        {/* Header with company info */}
        <div
          className={useThemeClasses(
            "bg-gradient-to-r from-blue-50 to-indigo-50 p-6",
            "bg-gradient-to-r from-dark-6 to-dark-5 p-6",
          )}
        >
          <div className="flex items-start gap-4">
            <div
              className={useThemeClasses(
                "flex h-16 w-16 items-center justify-center rounded-md bg-white shadow-sm",
                "flex h-16 w-16 items-center justify-center rounded-md bg-dark-8 shadow-dark-sm",
              )}
            >
              <FaBriefcase size={24} className="text-primary-color" />
            </div>
            <div className="flex-1">
              <Text
                className={useThemeClasses(
                  "text-2xl font-bold text-gray-800",
                  "text-2xl font-bold text-gray-100",
                )}
              >
                {displayData.jobTitle}
              </Text>
              <Text className="text-primary-color font-medium">
                Your Company Name
              </Text>
              <div className="mt-2 flex flex-wrap gap-3">
                <div
                  className={useThemeClasses(
                    "flex items-center gap-1 rounded-full bg-blue-100 px-3 py-1 text-blue-700",
                    "flex items-center gap-1 rounded-full bg-blue-900/30 px-3 py-1 text-blue-300",
                  )}
                >
                  <FaBriefcase size={14} />
                  <Text size="sm" fw={500}>
                    {displayData.jobType}
                  </Text>
                </div>
                <div
                  className={useThemeClasses(
                    "flex items-center gap-1 rounded-full bg-green-100 px-3 py-1 text-green-700",
                    "flex items-center gap-1 rounded-full bg-green-900/30 px-3 py-1 text-green-300",
                  )}
                >
                  <FaMapMarkerAlt size={14} />
                  <Text size="sm" fw={500}>
                    {displayData.location}
                  </Text>
                </div>
                {displayData.showSalary && (
                  <div
                    className={useThemeClasses(
                      "flex items-center gap-1 rounded-full bg-yellow-100 px-3 py-1 text-yellow-700",
                      "flex items-center gap-1 rounded-full bg-yellow-900/30 px-3 py-1 text-yellow-300",
                    )}
                  >
                    <FaDollarSign size={14} />
                    <Text size="sm" fw={500}>
                      {displayData.minSalary.toLocaleString()} -{" "}
                      {displayData.maxSalary.toLocaleString()}{" "}
                      {displayData.currency}
                    </Text>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Job details */}
        <div className="p-6">
          <div
            className={useThemeClasses(
              "mb-6 grid grid-cols-1 gap-4 rounded-lg bg-gray-50 p-4 sm:grid-cols-2 md:grid-cols-3",
              "mb-6 grid grid-cols-1 gap-4 rounded-lg bg-dark-6 p-4 sm:grid-cols-2 md:grid-cols-3",
            )}
          >
            <div>
              <Text size="sm" c="dimmed">
                Category
              </Text>
              <Text fw={500}>{displayData.jobCategory}</Text>
            </div>
            <div>
              <Text size="sm" c="dimmed">
                Experience
              </Text>
              <Text fw={500}>{displayData.requiredExperience}</Text>
            </div>
            <div>
              <Text size="sm" c="dimmed">
                Application Deadline
              </Text>
              <Text fw={500}>
                {displayData.applicationDeadline instanceof Date
                  ? displayData.applicationDeadline.toLocaleDateString(
                      "en-US",
                      {
                        year: "numeric",
                        month: "short",
                        day: "numeric",
                      },
                    )
                  : "30 days from posting"}
              </Text>
            </div>
          </div>

          <div className="mb-6">
            <Text
              className={useThemeClasses(
                "mb-2 text-lg font-semibold text-gray-800",
                "mb-2 text-lg font-semibold text-gray-100",
              )}
            >
              Job Description
            </Text>
            <Text
              size="sm"
              className={useThemeClasses(
                "whitespace-pre-line text-gray-700",
                "whitespace-pre-line text-gray-300",
              )}
            >
              {displayData.jobDescription}
            </Text>
          </div>

          <div className="mb-6">
            <Text
              className={useThemeClasses(
                "mb-2 text-lg font-semibold text-gray-800",
                "mb-2 text-lg font-semibold text-gray-100",
              )}
            >
              Requirements
            </Text>
            <Text
              size="sm"
              className={useThemeClasses(
                "whitespace-pre-line text-gray-700",
                "whitespace-pre-line text-gray-300",
              )}
            >
              {displayData.jobRequirements}
            </Text>
          </div>

          <div className="mb-6">
            <Text
              className={useThemeClasses(
                "mb-2 text-lg font-semibold text-gray-800",
                "mb-2 text-lg font-semibold text-gray-100",
              )}
            >
              Benefits & Perks
            </Text>
            <div className="grid grid-cols-1 gap-2 sm:grid-cols-2 md:grid-cols-3">
              {displayData.benefits.map((benefit: string, index: number) => (
                <div
                  key={index}
                  className={useThemeClasses(
                    "flex items-center gap-2 rounded-md bg-blue-50 p-2",
                    "flex items-center gap-2 rounded-md bg-blue-900/30 p-2",
                  )}
                >
                  <FaGift
                    size={14}
                    className={useThemeClasses(
                      "text-blue-500",
                      "text-blue-300",
                    )}
                  />
                  <Text size="sm">{benefit}</Text>
                </div>
              ))}
            </div>
          </div>

          <div>
            <Text
              className={useThemeClasses(
                "mb-2 text-lg font-semibold text-gray-800",
                "mb-2 text-lg font-semibold text-gray-100",
              )}
            >
              Skills & Tags
            </Text>
            <div className="flex flex-wrap gap-2">
              {displayData.jobTags.map((tag: string, index: number) => (
                <div
                  key={index}
                  className={useThemeClasses(
                    "rounded-full bg-gray-100 px-3 py-1 text-gray-700",
                    "rounded-full bg-dark-5 px-3 py-1 text-gray-300",
                  )}
                >
                  <Text size="sm">{tag}</Text>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Submit notice */}
      <Card
        withBorder
        radius="md"
        className={useThemeClasses(
          "border-blue-200 bg-blue-50/50",
          "border-blue-900/50 bg-blue-900/20",
        )}
      >
        <div className="flex gap-3">
          <div
            className={useThemeClasses(
              "flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full bg-blue-100 text-blue-600",
              "flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full bg-blue-900/50 text-blue-300",
            )}
          >
            <FaInfoCircle size={20} />
          </div>
          <div>
            <Text
              size="sm"
              className={useThemeClasses(
                "mb-1 font-medium text-blue-600",
                "mb-1 font-medium text-blue-300",
              )}
            >
              Ready to submit?
            </Text>
            <Text size="sm" c="dimmed">
              Once submitted, your job posting will be reviewed by our team and
              published shortly. Make sure all the information is accurate and
              complete.
            </Text>
          </div>
        </div>
      </Card>

      <Text className="text-center text-sm" c="dimmed">
        By submitting this job posting, you agree to our{" "}
        <span className="text-primary-color cursor-pointer hover:underline">
          terms and conditions
        </span>
        .
      </Text>
    </div>
  );
}
