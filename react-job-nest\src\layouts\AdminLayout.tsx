"use client";

import CompanyBlockModal from "@/components/admin/companies/CompanyBlockModal";
import CompanyDetailsModal from "@/components/admin/companies/CompanyDetailsModal";
import JobDetailsModal from "@/components/admin/jobs/JobDetailsModal";
import JobRejectModal from "@/components/admin/jobs/JobRejectModal";
import DeleteUserModal from "@/components/admin/users/DeleteUserModal";
import UserDetailsModal from "@/components/admin/users/UserDetailsModal";
import UserEditModal from "@/components/admin/users/UserEditModal";
import { useThemeClasses } from "@/design-system/utils/theme-utils";
import { useAdminNavigationStore } from "@/stores/admin-store";
import {
  useCompanyBlockModal,
  useCompanyDetailsModal,
  useJobDetailsModal,
  useJobRejectModal,
} from "@/stores/modal-store";
import { useMantineColorScheme } from "@mantine/core";
import { useEffect } from "react";
import { Outlet, useLocation } from "react-router";

export default function AdminLayout() {
  const { pathname } = useLocation();
  const setActivePath = useAdminNavigationStore((state) => state.setActivePath);
  const { colorScheme } = useMantineColorScheme();
  const isDark = colorScheme === "dark";

  // Get modal states and handlers
  const jobDetailsModal = useJobDetailsModal();
  const jobRejectModal = useJobRejectModal();
  const companyDetailsModal = useCompanyDetailsModal();
  const companyBlockModal = useCompanyBlockModal();

  // Set the current active page in the admin navigation store
  useEffect(() => {
    setActivePath(pathname);
  }, [pathname, setActivePath]);

  return (
    <div
      className={useThemeClasses(
        "min-h-screen bg-gray-50",
        "min-h-screen bg-dark-8",
      )}
    >
      <div className="container py-6">
        <div
          className={useThemeClasses(
            "flex-1 overflow-hidden rounded-xl border border-gray-100 bg-white shadow-sm",
            "flex-1 overflow-hidden rounded-xl border border-dark-4 bg-dark-7 shadow-dark-lg",
          )}
          style={{
            borderColor: isDark ? "var(--mantine-color-dark-4)" : undefined,
            backgroundColor: isDark ? "var(--mantine-color-dark-7)" : undefined,
          }}
        >
          <Outlet />
        </div>
      </div>

      {/* Global Admin Modals */}
      <UserDetailsModal />
      <UserEditModal />
      <DeleteUserModal
        onConfirm={() => {
          console.log("Delete user confirmation from layout");
        }}
      />
      <JobDetailsModal
        opened={jobDetailsModal.isOpen}
        onClose={jobDetailsModal.close}
        jobId={null}
      />
      <JobRejectModal
        opened={jobRejectModal.isOpen}
        onClose={jobRejectModal.close}
        onConfirm={() => {}}
        jobId={null}
      />
      <CompanyDetailsModal
        opened={companyDetailsModal.isOpen}
        onClose={companyDetailsModal.close}
        companyId={null}
      />
      <CompanyBlockModal
        opened={companyBlockModal.isOpen}
        onClose={companyBlockModal.close}
        onConfirm={() => {}}
        companyId={null}
      />
    </div>
  );
}
