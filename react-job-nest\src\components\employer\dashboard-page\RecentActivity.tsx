import { Card, Skeleton, Tabs, Title } from "@mantine/core";
import { FaBriefcase, FaCalendarAlt, FaUsers } from "react-icons/fa";
import RecentlyAppliedCandidates from "./RecentlyAppliedCandidates";
import RecentlyPostedJobs from "./RecentlyPostedJobs";
import UpcomingInterviews from "./UpcomingInterviews";

// Activity interface
interface Activity {
  id: string;
  type: string;
  message: string;
  date: string;
  status?: string;
  data?: any;
}

interface RecentActivityProps {
  activities?: Activity[];
  isLoading?: boolean;
}

export default function RecentActivity({
  activities = [],
  isLoading = false,
}: RecentActivityProps) {
  // Filter activities by type
  const candidates = activities.filter((a) => a.type === "application");
  const jobs = activities.filter((a) => a.type === "job");
  const interviews = activities.filter((a) => a.type === "interview");

  return (
    <div className="mb-8">
      <Title order={3} className="mb-4 text-xl font-semibold text-blue-800">
        Recent Activity
      </Title>

      <Card withBorder radius="md" className="shadow-sm">
        {isLoading ? (
          <div className="p-4">
            <Skeleton height={40} radius="md" className="mb-4" />
            <Skeleton height={100} radius="md" className="mb-3" />
            <Skeleton height={100} radius="md" className="mb-3" />
            <Skeleton height={100} radius="md" />
          </div>
        ) : (
          <Tabs defaultValue="candidates">
            <Tabs.List>
              <Tabs.Tab value="candidates" leftSection={<FaUsers size={16} />}>
                Recent Candidates
              </Tabs.Tab>
              <Tabs.Tab value="jobs" leftSection={<FaBriefcase size={16} />}>
                Recent Jobs
              </Tabs.Tab>
              <Tabs.Tab
                value="interviews"
                leftSection={<FaCalendarAlt size={16} />}
              >
                Upcoming Interviews
              </Tabs.Tab>
            </Tabs.List>

            <Tabs.Panel value="candidates" pt="md">
              <RecentlyAppliedCandidates candidates={candidates} />
            </Tabs.Panel>

            <Tabs.Panel value="jobs" pt="md">
              <RecentlyPostedJobs jobs={jobs} />
            </Tabs.Panel>

            <Tabs.Panel value="interviews" pt="md">
              <UpcomingInterviews interviews={interviews} />
            </Tabs.Panel>
          </Tabs>
        )}
      </Card>
    </div>
  );
}
