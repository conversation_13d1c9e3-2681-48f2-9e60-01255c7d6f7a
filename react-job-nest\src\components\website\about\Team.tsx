import team from "@/assets/team-1.jpg";
import {
  Badge,
  Card,
  Group,
  Text,
  ThemeIcon,
  useMantineColorScheme,
} from "@mantine/core";
import {
  FaGithub,
  FaGlobe,
  FaLinkedin,
  FaTwitter,
  FaUsers,
} from "react-icons/fa";
import { <PERSON> } from "react-router";

const teamMembers = [
  {
    id: 1,
    name: "<PERSON>",
    position: "CEO & Founder",
    bio: "<PERSON> leads the company with a vision to revolutionize the job market. With over 20 years of experience in the industry, he ensures that JobNest stays ahead of the curve.",
    image: team,
    skills: ["Leadership", "Strategy", "Business Development"],
    social: {
      linkedin: "#",
      twitter: "#",
      github: "#",
      website: "#",
    },
  },
  {
    id: 2,
    name: "<PERSON>",
    position: "CTO",
    bio: "<PERSON> is the technical backbone of JobNest. Her expertise in software development and technology drives the innovation and efficiency of our platform.",
    image: team,
    skills: ["Software Architecture", "AI", "Cloud Infrastructure"],
    social: {
      linkedin: "#",
      twitter: "#",
      github: "#",
    },
  },
  {
    id: 3,
    name: "<PERSON>",
    position: "COO",
    bio: "<PERSON> oversees the daily operations at JobNest. Her strategic planning and operational expertise ensure that our platform runs smoothly and efficiently.",
    image: team,
    skills: ["Operations", "Process Optimization", "Team Management"],
    social: {
      linkedin: "#",
      twitter: "#",
    },
  },
  {
    id: 4,
    name: "Robert Chen",
    position: "Head of Product",
    bio: "Robert leads our product development team, ensuring that JobNest continues to evolve with innovative features that meet the needs of our users.",
    image: team,
    skills: ["Product Strategy", "UX Design", "Market Research"],
    social: {
      linkedin: "#",
      twitter: "#",
      github: "#",
    },
  },
];

export default function Team() {
  const { colorScheme } = useMantineColorScheme();
  const isDark = colorScheme === "dark";

  return (
    <section
      className={`py-20 ${
        isDark
          ? "bg-gradient-to-b from-gray-900 to-gray-800"
          : "bg-gradient-to-b from-white to-gray-50"
      }`}
    >
      <div className="container">
        <div className="mx-auto max-w-3xl text-center">
          <Badge color="primary_color" size="lg" radius="sm" className="mb-4">
            Our Team
          </Badge>
          <h2 className="mb-4 text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-primary-color to-blue-700">
            Meet the Experts
          </h2>
          <p
            className={`mb-12 text-lg ${isDark ? "text-gray-300" : "text-gray-600"}`}
          >
            Our team is composed of dedicated professionals with a passion for
            connecting people and opportunities. We work tirelessly to improve
            our platform and provide the best experience for our users.
          </p>
        </div>

        <div className="grid grid-cols-1 gap-10 md:grid-cols-2 lg:grid-cols-4">
          {teamMembers.map(
            ({ id, name, position, bio, image, skills, social }) => (
              <Card
                key={id}
                className={`group relative overflow-hidden transition-all duration-300 hover:shadow-xl hover:-translate-y-2 ${
                  isDark ? "bg-gray-800 border-gray-700" : "border-0 bg-white"
                }`}
                radius="lg"
                withBorder
                shadow="sm"
              >
                <Card.Section>
                  <div className="relative">
                    <div className="aspect-square overflow-hidden">
                      <img
                        src={image}
                        alt={name}
                        className="h-full w-full object-cover transition-transform duration-500 group-hover:scale-105"
                        width={500}
                        height={500}
                      />
                    </div>

                    {/* Gradient overlay */}
                    <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100"></div>

                    {/* Social media icons */}
                    <div className="absolute bottom-0 left-0 right-0 flex justify-center gap-3 p-4 translate-y-full transition-transform duration-300 group-hover:translate-y-0">
                      {social.linkedin && (
                        <Link
                          to={social.linkedin}
                          className="flex h-9 w-9 items-center justify-center rounded-full bg-white text-blue-600 shadow-md transition-transform duration-300 hover:scale-110 hover:bg-blue-600 hover:text-white"
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          <FaLinkedin size={18} />
                        </Link>
                      )}
                      {social.twitter && (
                        <Link
                          to={social.twitter}
                          className="flex h-9 w-9 items-center justify-center rounded-full bg-white text-blue-400 shadow-md transition-transform duration-300 hover:scale-110 hover:bg-blue-400 hover:text-white"
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          <FaTwitter size={18} />
                        </Link>
                      )}
                      {social.github && (
                        <Link
                          to={social.github}
                          className="flex h-9 w-9 items-center justify-center rounded-full bg-white text-gray-800 shadow-md transition-transform duration-300 hover:scale-110 hover:bg-gray-800 hover:text-white"
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          <FaGithub size={18} />
                        </Link>
                      )}
                      {social.website && (
                        <Link
                          to={social.website}
                          className="flex h-9 w-9 items-center justify-center rounded-full bg-white text-purple-600 shadow-md transition-transform duration-300 hover:scale-110 hover:bg-purple-600 hover:text-white"
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          <FaGlobe size={18} />
                        </Link>
                      )}
                    </div>
                  </div>
                </Card.Section>

                <div className="p-6">
                  <Text
                    component="h3"
                    className={`!mb-1 text-xl font-bold ${isDark ? "text-white" : ""}`}
                  >
                    {name}
                  </Text>
                  <Text className="text-primary-color text-sm font-medium">
                    {position}
                  </Text>
                  <Text
                    className={`!my-4 line-clamp-3 ${isDark ? "text-gray-300" : "text-gray-600"}`}
                  >
                    {bio}
                  </Text>

                  {/* Skills badges */}
                  <Group gap="xs" className="mt-auto flex-wrap">
                    {skills.map((skill, index) => (
                      <Badge
                        key={index}
                        color={
                          index % 3 === 0
                            ? "blue"
                            : index % 3 === 1
                              ? "green"
                              : "violet"
                        }
                        variant="light"
                        size="sm"
                      >
                        {skill}
                      </Badge>
                    ))}
                  </Group>
                </div>
              </Card>
            ),
          )}
        </div>

        {/* Decorative elements */}
        <div className="relative mt-16 flex justify-center">
          <ThemeIcon
            size={80}
            radius="xl"
            className={`text-primary-color ${isDark ? "bg-primary-color/20" : "bg-primary-color/10"}`}
          >
            <FaUsers size={40} />
          </ThemeIcon>
          <div
            className={`absolute -z-10 h-40 w-40 rounded-full blur-xl ${
              isDark ? "bg-primary-color/10" : "bg-primary-color/5"
            }`}
          ></div>
        </div>
      </div>
    </section>
  );
}
