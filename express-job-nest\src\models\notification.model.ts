import mongoose, { Document, Schema } from 'mongoose';

export type NotificationType = 
  // User notifications
  'welcome' | 'account_update' | 'password_reset' | 
  // Job notifications
  'job_applied' | 'job_application_status' | 'job_expired' | 'job_approved' | 'job_rejected' |
  // Employer notifications
  'new_application' | 'subscription_expiring' | 'subscription_renewed' | 'company_verified' | 'company_blocked' |
  // Admin notifications
  'new_user' | 'new_job' | 'new_company' | 'payment_received' | 'report';

export interface INotification extends Document {
  recipient: mongoose.Types.ObjectId;
  type: NotificationType;
  title: string;
  message: string;
  isRead: boolean;
  isEmailSent: boolean;
  relatedEntities?: {
    user?: mongoose.Types.ObjectId;
    job?: mongoose.Types.ObjectId;
    company?: mongoose.Types.ObjectId;
    application?: mongoose.Types.ObjectId;
    subscription?: mongoose.Types.ObjectId;
  };
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

const notificationSchema = new Schema<INotification>(
  {
    recipient: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'Notification recipient is required'],
    },
    type: {
      type: String,
      required: [true, 'Notification type is required'],
      enum: [
        // User notifications
        'welcome', 'account_update', 'password_reset',
        // Job notifications
        'job_applied', 'job_application_status', 'job_expired', 'job_approved', 'job_rejected',
        // Employer notifications
        'new_application', 'subscription_expiring', 'subscription_renewed', 'company_verified', 'company_blocked',
        // Admin notifications
        'new_user', 'new_job', 'new_company', 'payment_received', 'report'
      ],
    },
    title: {
      type: String,
      required: [true, 'Notification title is required'],
    },
    message: {
      type: String,
      required: [true, 'Notification message is required'],
    },
    isRead: {
      type: Boolean,
      default: false,
    },
    isEmailSent: {
      type: Boolean,
      default: false,
    },
    relatedEntities: {
      user: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
      },
      job: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Job',
      },
      company: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Company',
      },
      application: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Application',
      },
      subscription: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'UserSubscription',
      },
    },
    metadata: {
      type: Schema.Types.Mixed,
    },
  },
  {
    timestamps: true, // Automatically add createdAt and updatedAt fields
  }
);

// Create indexes for better query performance
notificationSchema.index({ recipient: 1, isRead: 1 });
notificationSchema.index({ recipient: 1, createdAt: -1 });
notificationSchema.index({ type: 1 });

const Notification = mongoose.model<INotification>('Notification', notificationSchema);

export default Notification;
