import { cn } from "@/design-system/utils";
import { useThemeClasses } from "@/design-system/utils/theme-utils";
import { type ReactNode } from "react";
import { type IconType } from "react-icons";

export interface SectionHeadingProps {
  children: ReactNode;
  icon?: IconType;
  className?: string;
  variant?: "default" | "admin" | "employer" | "candidate";
}

/**
 * Reusable section heading component with optional icon
 *
 * @param variant - Different styling variants based on user role
 */
export default function SectionHeading({
  children,
  icon: Icon,
  className = "",
  variant = "default",
}: SectionHeadingProps) {
  // Apply variant-specific styling
  const variantClasses = {
    default: "mb-4 flex items-center gap-2",
    admin: "mb-4 flex items-center gap-2",
    employer: "mb-4 flex items-center gap-2",
    candidate: "mb-4 flex items-center gap-2 text-primary-color",
  };

  const iconClasses = {
    default: "text-primary-color",
    admin: "text-primary-color",
    employer: "text-primary-color",
    candidate: "",
  };

  const headingClasses = {
    default: "text-primary-color text-xl font-semibold",
    admin: useThemeClasses(
      "text-lg font-semibold text-gray-800 sm:text-xl",
      "text-lg font-semibold text-gray-100 sm:text-xl",
    ),
    employer: useThemeClasses(
      "text-lg font-semibold text-gray-800 sm:text-xl",
      "text-lg font-semibold text-gray-100 sm:text-xl",
    ),
    candidate: "text-xl font-semibold",
  };

  const iconSize = {
    default: 20,
    admin: 22,
    employer: 22,
    candidate: 22,
  };

  return (
    <div className={cn(variantClasses[variant], className)}>
      {Icon && (
        <Icon size={iconSize[variant]} className={iconClasses[variant]} />
      )}
      <h2 className={headingClasses[variant]}>{children}</h2>
    </div>
  );
}
