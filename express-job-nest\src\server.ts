import express, { Request, Response, NextFunction } from "express";
import cors from "cors";
import morgan from "morgan";
import dotenv from "dotenv";
import mongoose from "mongoose";
import cookieParser from "cookie-parser";

// Import routes
import authRoutes from "./routes/auth.routes";
import userRoutes from "./routes/user.routes";
import jobRoutes from "./routes/job.routes";
import companyRoutes from "./routes/company.routes";
import applicationRoutes from "./routes/application.routes";
import subscriptionRoutes from "./routes/subscription.routes";
import candidateProfileRoutes from "./routes/candidate-profile.routes";
import jobRecommendationRoutes from "./routes/job-recommendation.routes";
import jobSearchRoutes from "./routes/job-search.routes";

// Admin routes
import adminRoutes from "./routes/admin.routes";
import adminJobRoutes from "./routes/admin-job.routes";
import adminCompanyRoutes from "./routes/admin-company.routes";
import adminSettingsRoutes from "./routes/admin-settings.routes";

// Employer routes
import employerDashboardRoutes from "./routes/employer-dashboard.routes";
import employerRoutes from "./routes/employer.routes";

// Notification routes
import notificationRoutes from "./routes/notification.routes";

// Load environment variables
dotenv.config();

// Create Express app
const app = express();

// Middleware
app.use(
  cors({
    origin: [
      "http://localhost:5173",
      "https://react-job-nest.vercel.app",
    ], // Frontend URL
    credentials: true, // Allow cookies to be sent
  })
);
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(cookieParser()); // Parse cookies
app.use(morgan("dev"));

// Serve static files from the uploads directory
app.use("/uploads", express.static("uploads"));

// Routes
app.use("/api/auth", authRoutes);
app.use("/api/users", userRoutes);
app.use("/api/jobs", jobRoutes);
app.use("/api/companies", companyRoutes);
app.use("/api/applications", applicationRoutes);
app.use("/api/subscriptions", subscriptionRoutes);
app.use("/api/candidate-profiles", candidateProfileRoutes);
app.use("/api/job-recommendations", jobRecommendationRoutes);
app.use("/api/job-search", jobSearchRoutes);

// Admin routes
app.use("/api/admin", adminRoutes);
app.use("/api/admin/jobs", adminJobRoutes);
app.use("/api/admin/companies", adminCompanyRoutes);
app.use("/api/admin/settings", adminSettingsRoutes);

// Employer routes
app.use("/api/employer/dashboard", employerDashboardRoutes);
app.use("/api/employer", employerRoutes);

// Notification routes
app.use("/api/notifications", notificationRoutes);

// Health check route
app.get("/api/health", (_req: Request, res: Response) => {
  res.status(200).json({ status: "ok", message: "Server is running" });
});

// Error handling middleware
app.use((err: any, _req: Request, res: Response, _next: NextFunction) => {
  console.error(err.stack);
  const statusCode = err.statusCode || 500;
  const message = err.message || "Internal Server Error";
  res.status(statusCode).json({
    success: false,
    message,
    stack: process.env.NODE_ENV === "production" ? undefined : err.stack,
  });
});

// 404 handler
app.use((_req: Request, res: Response) => {
  res.status(404).json({
    success: false,
    message: `Route not found`,
  });
});

// Database connection
const connectDB = async () => {
  try {
    const conn = await mongoose.connect(
      process.env.MONGODB_URI || "mongodb://localhost:27017/job-nest"
    );
    console.log(`MongoDB Connected: ${conn.connection.host}`);
  } catch (error) {
    console.error(`Error connecting to MongoDB: ${error}`);
    process.exit(1);
  }
};

// Start server
const PORT = process.env.PORT || 5000;

const startServer = async () => {
  await connectDB();
  app.listen(PORT, () => {
    console.log(
      `Server running in ${process.env.NODE_ENV} mode on port ${PORT}`
    );
  });
};

export { app, startServer };
