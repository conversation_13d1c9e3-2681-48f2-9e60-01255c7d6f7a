"use client";

import { Sidebar, type SidebarLink } from "@/design-system/components";
import { URLS } from "@/utils/urls";
import {
  FaCreditCard,
  FaEnvelope,
  FaPlus,
  FaTachometerAlt,
  FaTasks,
  FaUser,
} from "react-icons/fa";

const employerLinks: SidebarLink[] = [
  {
    id: 1,
    name: "Dashboard",
    href: URLS.employer.dashboard,
    icon: FaTachometerAlt,
  },
  {
    id: 2,
    name: "Create a Job",
    href: URLS.employer.createJob,
    icon: FaPlus,
  },
  {
    id: 3,
    name: "Manage Jobs",
    href: URLS.employer.manageJobs,
    icon: FaTasks,
  },
  {
    id: 4,
    name: "Candidates",
    href: URLS.employer.candidates,
    icon: FaEnvelope,
  },
  {
    id: 5,
    name: "Profile",
    href: URLS.employer.profile,
    icon: FaUser,
  },
  {
    id: 6,
    name: "Pricing Plans",
    href: URLS.employer.pricing,
    icon: FaCreditCard,
  },
];

interface EmployerSidebarProps {
  onLinkClick?: () => void;
}

export default function EmployerSidebar({ onLinkClick }: EmployerSidebarProps) {
  return (
    <Sidebar
      title="Employer Portal"
      links={employerLinks}
      onLinkClick={onLinkClick}
    />
  );
}
