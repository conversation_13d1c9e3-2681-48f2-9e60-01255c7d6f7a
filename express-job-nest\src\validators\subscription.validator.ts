import { body } from "express-validator";

export const createSubscriptionPlanValidator = [
  body("name")
    .notEmpty()
    .withMessage("Plan name is required")
    .isString()
    .withMessage("Plan name must be a string"),

  body("description")
    .notEmpty()
    .withMessage("Plan description is required")
    .isString()
    .withMessage("Plan description must be a string"),

  body("price")
    .notEmpty()
    .withMessage("Price is required")
    .isNumeric()
    .withMessage("Price must be a number")
    .custom((value) => value >= 0)
    .withMessage("Price cannot be negative"),

  body("annualPrice")
    .notEmpty()
    .withMessage("Annual price is required")
    .isNumeric()
    .withMessage("Annual price must be a number")
    .custom((value) => value >= 0)
    .withMessage("Annual price cannot be negative"),

  body("currency")
    .optional()
    .isString()
    .withMessage("Currency must be a string"),

  body("features.activeJobs")
    .notEmpty()
    .withMessage("Active jobs limit is required")
    .custom(
      (value) =>
        value === "unlimited" || (typeof value === "number" && value > 0)
    )
    .withMessage('Active jobs must be a positive number or "unlimited"'),

  body("features.jobVisibilityDays")
    .notEmpty()
    .withMessage("Job visibility days is required")
    .isNumeric()
    .withMessage("Job visibility days must be a number")
    .custom((value) => value > 0)
    .withMessage("Job visibility days must be positive"),

  body("features.cvViews")
    .notEmpty()
    .withMessage("CV views limit is required")
    .custom(
      (value) =>
        value === "unlimited" || (typeof value === "number" && value > 0)
    )
    .withMessage('CV views must be a positive number or "unlimited"'),

  body("features.featuredJobs")
    .optional()
    .isNumeric()
    .withMessage("Featured jobs must be a number")
    .custom((value) => value >= 0)
    .withMessage("Featured jobs cannot be negative"),

  body("features.priorityPlacement")
    .optional()
    .isBoolean()
    .withMessage("Priority placement must be a boolean"),

  body("features.featuredCompanyProfile")
    .optional()
    .isBoolean()
    .withMessage("Featured company profile must be a boolean"),

  body("features.candidateMatching")
    .optional()
    .isBoolean()
    .withMessage("Candidate matching must be a boolean"),

  body("features.advancedAnalytics")
    .optional()
    .isBoolean()
    .withMessage("Advanced analytics must be a boolean"),

  body("features.dedicatedSupport")
    .optional()
    .isBoolean()
    .withMessage("Dedicated support must be a boolean"),

  body("features.customFeatures")
    .optional()
    .isArray()
    .withMessage("Custom features must be an array"),

  body("isActive")
    .optional()
    .isBoolean()
    .withMessage("Is active must be a boolean"),

  body("isPopular")
    .optional()
    .isBoolean()
    .withMessage("Is popular must be a boolean"),

  body("sortOrder")
    .optional()
    .isNumeric()
    .withMessage("Sort order must be a number"),
];

export const updateSubscriptionPlanValidator = [
  body("name").optional().isString().withMessage("Plan name must be a string"),

  body("description")
    .optional()
    .isString()
    .withMessage("Plan description must be a string"),

  body("price")
    .optional()
    .isNumeric()
    .withMessage("Price must be a number")
    .custom((value) => value >= 0)
    .withMessage("Price cannot be negative"),

  body("annualPrice")
    .optional()
    .isNumeric()
    .withMessage("Annual price must be a number")
    .custom((value) => value >= 0)
    .withMessage("Annual price cannot be negative"),

  body("currency")
    .optional()
    .isString()
    .withMessage("Currency must be a string"),

  body("features.activeJobs")
    .optional()
    .custom(
      (value) =>
        value === "unlimited" || (typeof value === "number" && value > 0)
    )
    .withMessage('Active jobs must be a positive number or "unlimited"'),

  body("features.jobVisibilityDays")
    .optional()
    .isNumeric()
    .withMessage("Job visibility days must be a number")
    .custom((value) => value > 0)
    .withMessage("Job visibility days must be positive"),

  body("features.cvViews")
    .optional()
    .custom(
      (value) =>
        value === "unlimited" || (typeof value === "number" && value > 0)
    )
    .withMessage('CV views must be a positive number or "unlimited"'),

  body("isActive")
    .optional()
    .isBoolean()
    .withMessage("Is active must be a boolean"),

  body("isPopular")
    .optional()
    .isBoolean()
    .withMessage("Is popular must be a boolean"),

  body("sortOrder")
    .optional()
    .isNumeric()
    .withMessage("Sort order must be a number"),
];

export const subscribeToPlanValidator = [
  body("planId")
    .notEmpty()
    .withMessage("Plan ID is required")
    .isMongoId()
    .withMessage("Invalid plan ID format"),

  body("billingPeriod")
    .notEmpty()
    .withMessage("Billing period is required")
    .isIn(["month", "year"])
    .withMessage("Billing period must be either month or year"),

  body("paymentMethod")
    .notEmpty()
    .withMessage("Payment method is required")
    .isString()
    .withMessage("Payment method must be a string"),

  body("transactionId")
    .optional()
    .isString()
    .withMessage("Transaction ID must be a string"),
];
