import { useThemeClasses } from "@/design-system/utils/theme-utils";
import { type Job } from "@/types";
import {
  Badge,
  Button,
  Card,
  Group,
  Stack,
  Text,
  Title,
  Tooltip,
  useMantineColorScheme,
} from "@mantine/core";
import {
  FaBriefcase,
  FaBuilding,
  FaClock,
  FaMapMarkerAlt,
  FaStar,
} from "react-icons/fa";
import { Link } from "react-router";

export default function JobCard({ job }: { job: Job }) {
  const { colorScheme } = useMantineColorScheme();
  const isDark = colorScheme === "dark";

  // Function to get a random color for company logo background
  const getRandomColor = () => {
    const lightColors = [
      "#E9F7EF",
      "#EBF5FB",
      "#F4ECF7",
      "#FEF9E7",
      "#F2F3F4",
      "#FDEDEC",
    ];

    const darkColors = [
      "#1E3A29", // Dark green
      "#1A3A4A", // Dark blue
      "#2E2A3A", // Dark purple
      "#3A3A2A", // Dark yellow
      "#2A2A2A", // Dark gray
      "#3A2A2A", // Dark red
    ];

    const colors = isDark ? darkColors : lightColors;
    return colors[Math.floor(Math.random() * colors.length)];
  };

  // Function to get company initials for the logo
  const getCompanyInitials = (
    company: string | { name: string; _id: string; logo?: string },
  ) => {
    // Handle both string and object company formats
    const companyName = typeof company === "string" ? company : company.name;

    if (!companyName || typeof companyName !== "string") {
      return "CO"; // Default fallback
    }

    return companyName
      .split(" ")
      .map((word) => word[0])
      .join("")
      .toUpperCase()
      .substring(0, 2);
  };

  // Handle different field names between frontend and backend
  const jobId = job._id || job.id;
  const jobType = job.type || job.jobType;
  const jobExperience = job.experience || job.requiredExperience;
  const jobDatePosted = job.datePosted || job.createdAt;

  return (
    <Card
      component={Link}
      to={`/candidate/jobs/${jobId}`}
      padding="lg"
      radius="md"
      withBorder
      className={`block h-full transition-all duration-200 ${
        isDark
          ? "border-dark-4 bg-dark-7 hover:border-primary-color/30 hover:shadow-dark-lg"
          : "border-gray-200 bg-white hover:border-primary-color/20 hover:shadow-md"
      }`}
      style={{
        borderColor: isDark ? "var(--mantine-color-dark-4)" : undefined,
        backgroundColor: isDark ? "var(--mantine-color-dark-7)" : undefined,
      }}
    >
      <Stack gap="md">
        <Group align="flex-start" wrap="nowrap">
          <div
            className="flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-md sm:h-12 sm:w-12"
            style={{ backgroundColor: getRandomColor() }}
          >
            <Text
              fw={700}
              size="lg"
              className={useThemeClasses("text-gray-800", "text-gray-100")}
            >
              {getCompanyInitials(job.company)}
            </Text>
          </div>
          <div className="min-w-0 flex-1">
            <Title
              order={4}
              className="text-primary-color line-clamp-1 text-base sm:text-lg"
            >
              {job.title}
            </Title>
            <Group gap="xs" wrap="nowrap">
              <FaBuilding
                size={12}
                className={useThemeClasses(
                  "flex-shrink-0 text-gray-500",
                  "flex-shrink-0 text-gray-400",
                )}
              />
              <Text
                size="xs"
                c="dimmed"
                className="line-clamp-1 min-w-0 truncate sm:text-sm"
              >
                {typeof job.company === "string"
                  ? job.company
                  : job.company.name}
              </Text>
            </Group>
          </div>
        </Group>

        <div className="flex flex-wrap gap-2 sm:gap-3">
          <Group gap="xs" wrap="nowrap" className="mr-1">
            <FaMapMarkerAlt
              size={12}
              className={useThemeClasses(
                "flex-shrink-0 text-gray-500",
                "flex-shrink-0 text-gray-400",
              )}
            />
            <Text size="xs" c="dimmed" className="line-clamp-1 sm:text-sm">
              {job.location}
            </Text>
          </Group>
          <Group gap="xs" wrap="nowrap">
            <FaClock
              size={12}
              className={useThemeClasses(
                "flex-shrink-0 text-gray-500",
                "flex-shrink-0 text-gray-400",
              )}
            />
            <Text size="xs" c="dimmed" className="sm:text-sm">
              {jobDatePosted
                ? new Date(jobDatePosted).toLocaleDateString()
                : "2 days ago"}
            </Text>
          </Group>
        </div>

        <Text lineClamp={2} size="xs" className="flex-grow py-1 sm:text-sm">
          {job.description}
        </Text>

        <div className="flex flex-wrap gap-1 sm:gap-2">
          <Badge
            variant="filled"
            color="primary"
            leftSection={<FaBriefcase size={8} />}
            size="xs"
            radius="sm"
            className="mb-1"
            styles={{
              root: {
                backgroundColor: isDark
                  ? "var(--mantine-color-primary-700) !important"
                  : "var(--mantine-color-primary-500) !important",
              },
            }}
          >
            {jobType}
          </Badge>
          <Badge
            variant="filled"
            color="secondary"
            size="xs"
            radius="sm"
            className="mb-1"
            styles={{
              root: {
                backgroundColor: isDark
                  ? "var(--mantine-color-secondary-700) !important"
                  : "var(--mantine-color-secondary-500) !important",
              },
            }}
          >
            {job.workType}
          </Badge>
          <Badge
            variant="outline"
            size="xs"
            radius="sm"
            className="mb-1"
            styles={{
              root: {
                borderColor: isDark
                  ? "var(--mantine-color-dark-4) !important"
                  : "var(--mantine-color-gray-3) !important",
                color: isDark
                  ? "var(--mantine-color-gray-3) !important"
                  : "var(--mantine-color-gray-7) !important",
              },
            }}
          >
            {jobExperience}
          </Badge>
          <Badge
            variant="outline"
            size="xs"
            radius="sm"
            className="mb-1"
            styles={{
              root: {
                borderColor: isDark
                  ? "var(--mantine-color-dark-4) !important"
                  : "var(--mantine-color-gray-3) !important",
                color: isDark
                  ? "var(--mantine-color-gray-3) !important"
                  : "var(--mantine-color-gray-7) !important",
              },
            }}
          >
            {job.category}
          </Badge>
        </div>

        <Group justify="space-between" mt="xs" align="center" wrap="nowrap">
          <Text c="dimmed" size="xs" className="truncate sm:text-sm">
            {job.salary
              ? `$${job.salary}`
              : job.minSalary && job.maxSalary
                ? `${job.currency || "$"}${job.minSalary.toLocaleString()} - ${job.currency || "$"}${job.maxSalary.toLocaleString()}`
                : "Salary not disclosed"}
          </Text>
          <Tooltip label="Save Job">
            <Button
              variant="subtle"
              size="xs"
              p={4}
              color={isDark ? "primary.4" : "primary"}
              onClick={(e) => {
                e.preventDefault();
                // Add save job functionality here
              }}
              className="transition-all duration-200 hover:bg-primary-color/10"
            >
              <FaStar size={14} />
            </Button>
          </Tooltip>
        </Group>
      </Stack>
    </Card>
  );
}
