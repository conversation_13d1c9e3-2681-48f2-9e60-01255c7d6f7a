import { z } from "zod";

// Basic registration schema fields (step 1)
const employerBasicFields = {
  name: z.string().min(2, "Name must be at least 2 characters"),
  email: z.string().email("Invalid email address"),
  password: z
    .string()
    .min(8, "Password must be at least 8 characters")
    .regex(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
      "Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character",
    ),
  passwordConfirmation: z.string(),
  role: z.literal("employer"),
};

// Basic registration schema (step 1)
export const employerBasicSchema = z
  .object(employerBasicFields)
  .refine((data) => data.password === data.passwordConfirmation, {
    message: "Passwords do not match",
    path: ["passwordConfirmation"],
  });

// Company information schema fields (step 2)
const employerCompanyFields = {
  companyName: z.string().min(2, "Company name must be at least 2 characters"),
  contactPerson: z
    .string()
    .min(2, "Contact person name must be at least 2 characters"),
  companyEmail: z.string().email("Invalid email address"),
  phone: z.string().min(10, "Phone number must be at least 10 digits"),
  industry: z.string().min(2, "Industry must be at least 2 characters"),
  companySize: z.string().min(1, "Company size is required"),
};

// Company information schema (step 2)
export const employerCompanySchema = z.object(employerCompanyFields);

// Combined schema for all steps
export const employerRegisterSchema = z
  .object({
    ...employerBasicFields,
    ...employerCompanyFields,
  })
  .refine((data) => data.password === data.passwordConfirmation, {
    message: "Passwords do not match",
    path: ["passwordConfirmation"],
  });
