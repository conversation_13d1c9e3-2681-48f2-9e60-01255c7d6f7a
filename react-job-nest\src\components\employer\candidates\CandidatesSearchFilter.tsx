"use client";

import { <PERSON><PERSON>, Grid, Group, Select, TextInput } from "@mantine/core";
import { DatePickerInput } from "@mantine/dates";
import {
  FaCalendarAlt,
  FaFilter,
  FaSearch,
  FaSortAmountDown,
  FaUserTag,
} from "react-icons/fa";

// Define the props interface
interface CandidatesSearchFilterProps {
  jobs: Array<{ _id: string; title: string }>;
  selectedJob: string;
  setSelectedJob: (jobId: string) => void;
  selectedStatus: string;
  setSelectedStatus: (status: string) => void;
  dateRange: [Date | null, Date | null];
  setDateRange: (range: any) => void;
  sortBy: string;
  setSortBy: (sortBy: string) => void;
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  onApplyFilters: () => void;
}

export default function CandidatesSearchFilter({
  jobs,
  selectedJob,
  setSelectedJob,
  selectedStatus,
  setSelectedStatus,
  dateRange,
  setDateRange,
  sortBy,
  setSortBy,
  searchQuery,
  setSearchQuery,
  onApplyFilters,
}: CandidatesSearchFilterProps) {
  // Reset all filters
  const resetFilters = () => {
    setSelectedJob("");
    setSelectedStatus("");
    setDateRange([null, null]);
    setSortBy("newest");
    setSearchQuery("");
  };

  // We'll handle search query changes in the parent component
  // This prevents unnecessary API calls and infinite loops

  // Apply filters
  const applyFilters = () => {
    onApplyFilters();
  };

  return (
    <div className="space-y-4">
      {/* Search and main filters */}
      <Grid gutter="md">
        <Grid.Col span={{ base: 12, md: 6 }}>
          <TextInput
            placeholder="Search by name, email, or job title"
            leftSection={<FaSearch size={16} />}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </Grid.Col>
        <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
          <Select
            placeholder="Filter by job"
            data={jobs.map((job) => ({
              value: job._id,
              label: job.title,
            }))}
            leftSection={<FaFilter size={16} />}
            value={selectedJob}
            onChange={(value) => setSelectedJob(value || "")}
            clearable
          />
        </Grid.Col>
        <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
          <Select
            placeholder="Filter by status"
            data={[
              { value: "pending", label: "Pending" },
              { value: "reviewing", label: "Reviewing" },
              { value: "shortlisted", label: "Shortlisted" },
              { value: "interview", label: "Interview" },
              { value: "offered", label: "Offered" },
              { value: "hired", label: "Hired" },
              { value: "rejected", label: "Rejected" },
            ]}
            leftSection={<FaUserTag size={16} />}
            value={selectedStatus}
            onChange={(value) => setSelectedStatus(value || "")}
            clearable
          />
        </Grid.Col>
      </Grid>

      {/* Advanced filters */}
      <Grid gutter="md">
        <Grid.Col span={{ base: 12, md: 6 }}>
          <DatePickerInput
            type="range"
            placeholder="Filter by application date"
            leftSection={<FaCalendarAlt size={16} />}
            value={dateRange}
            onChange={setDateRange}
            clearable
          />
        </Grid.Col>
        <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
          <Select
            placeholder="Sort by"
            data={[
              { value: "newest", label: "Most Recent" },
              { value: "oldest", label: "Oldest First" },
              { value: "name_asc", label: "Name (A-Z)" },
              { value: "name_desc", label: "Name (Z-A)" },
              { value: "status", label: "Status" },
            ]}
            leftSection={<FaSortAmountDown size={16} />}
            value={sortBy}
            onChange={(value) => setSortBy(value || "newest")}
          />
        </Grid.Col>
        <Grid.Col span={{ base: 12, sm: 6, md: 3 }} className="flex items-end">
          <Group grow>
            <Button variant="outline" onClick={resetFilters} size="sm">
              Reset
            </Button>
            <Button onClick={applyFilters} size="sm">
              Apply
            </Button>
          </Group>
        </Grid.Col>
      </Grid>
    </div>
  );
}
