import express from "express";
import {
  getAllCompanies,
  getCompanyById,
  updateCompany,
  deleteCompany,
  verifyCompany,
  toggleCompanyStatus,
  getCompanyIndustries,
} from "../controllers/admin-company.controller";
import { protect, restrictTo } from "../middleware/auth.middleware";
import { body } from "express-validator";
import { validationMiddleware } from "../validators/validation.middleware";

// Define validators
const updateCompanyValidator = [
  body("name")
    .optional()
    .isString()
    .withMessage("Name must be a string")
    .isLength({ min: 2 })
    .withMessage("Name must be at least 2 characters long"),
  body("description")
    .optional()
    .isString()
    .withMessage("Description must be a string"),
  body("industry")
    .optional()
    .isString()
    .withMessage("Industry must be a string"),
  body("size")
    .optional()
    .isIn(["1-10", "11-50", "51-200", "201-500", "501-1000", "1000+"])
    .withMessage(
      "Size must be one of: 1-10, 11-50, 51-200, 201-500, 501-1000, 1000+"
    ),
  body("location")
    .optional()
    .isString()
    .withMessage("Location must be a string"),
  body("isVerified")
    .optional()
    .isBoolean()
    .withMessage("isVerified must be a boolean"),
  body("isActive")
    .optional()
    .isBoolean()
    .withMessage("isActive must be a boolean"),
];

const toggleStatusValidator = [
  body("reason")
    .notEmpty()
    .withMessage("Reason is required")
    .isString()
    .withMessage("Reason must be a string"),
];

const router = express.Router();

// All routes are protected and restricted to admins
router.use(protect);
router.use(restrictTo("admin"));

// Company management routes
router.get("/", getAllCompanies);
router.get("/industries", getCompanyIndustries);
router.get("/:id", getCompanyById);
router.patch(
  "/:id",
  updateCompanyValidator,
  validationMiddleware,
  updateCompany
);
router.delete("/:id", deleteCompany);
router.patch("/verify/:id", verifyCompany);
router.patch(
  "/toggle-status/:id",
  toggleStatusValidator,
  validationMiddleware,
  toggleCompanyStatus
);

export default router;
