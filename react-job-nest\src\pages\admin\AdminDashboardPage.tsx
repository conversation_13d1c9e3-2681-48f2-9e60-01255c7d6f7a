"use client";

import AdminMainHeading from "@/components/admin/AdminMainHeading";
import AdminNavbar from "@/components/admin/AdminNavbar";
import Charts from "@/components/admin/dashboard/Charts";
import KeyMetrics from "@/components/admin/dashboard/KeyMetrics";
import QuickActions from "@/components/admin/dashboard/QuickActions";
import RecentActivity from "@/components/admin/dashboard/RecentActivity";
import Card from "@/design-system/components/card/Card";
import PageContainer from "@/design-system/components/layout/PageContainer";

export default function AdminDashboardPage() {
  return (
    <>
      <AdminNavbar />
      <PageContainer variant="admin">
        {/* Page Title */}
        <AdminMainHeading
          title="Admin Dashboard"
          subtitle="Overview of platform statistics and activities"
        />

        {/* Key Metrics Section */}
        <div className="mt-6">
          <KeyMetrics />
        </div>

        {/* Quick Actions and Recent Activity in a 2-column layout */}
        <div className="mt-8 grid grid-cols-1 gap-6 lg:grid-cols-2">
          {/* Quick Actions Section */}
          <Card withBorder withShadow padding="lg" radius="md" variant="admin">
            <QuickActions />
          </Card>

          {/* Recent Activity Section */}
          <Card withBorder withShadow padding="lg" radius="md" variant="admin">
            <RecentActivity />
          </Card>
        </div>

        {/* Charts Section */}
        <div className="mt-8">
          <Card withBorder withShadow padding="lg" radius="md" variant="admin">
            <Charts />
          </Card>
        </div>
      </PageContainer>
    </>
  );
}
