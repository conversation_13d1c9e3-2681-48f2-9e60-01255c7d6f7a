import { candidateData } from "@/data/candidate-data";
import { Badge, Card, Group, Title } from "@mantine/core";

export default function CandidateSkills() {
  return (
    <Card withBorder radius="md" className="mb-6 p-6 shadow-sm">
      <Title order={2} className="mb-4 text-lg font-semibold">
        Skills
      </Title>
      <Group>
        {candidateData.skills.map((skill, index) => (
          <Badge key={index} color="teal" size="lg" variant="light">
            {skill}
          </Badge>
        ))}
      </Group>
    </Card>
  );
}
