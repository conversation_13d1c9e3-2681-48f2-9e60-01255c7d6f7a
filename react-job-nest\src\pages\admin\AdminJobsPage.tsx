"use client";

import AdminNavbar from "@/components/admin/AdminNavbar";
import JobDetailsModal from "@/components/admin/jobs/JobDetailsModal";
import JobFilters from "@/components/admin/jobs/JobFilters";
import JobRejectModal from "@/components/admin/jobs/JobRejectModal";
import JobsTable from "@/components/admin/jobs/JobsTable";
import PageContainer from "@/design-system/components/layout/PageContainer";
import PageHeading from "@/design-system/components/typography/PageHeading";
import { jobFilterSchema } from "@/schemas/admin/job-management-schema";
import {
  Badge,
  Button,
  Card,
  Group,
  Modal,
  Pagination,
  Tabs,
  Text,
  TextInput,
} from "@mantine/core";
import { useForm } from "@mantine/form";
import { notifications } from "@mantine/notifications";
import { zodResolver } from "mantine-form-zod-resolver";
import { useState } from "react";
import { <PERSON>a<PERSON>ilt<PERSON>, <PERSON>a<PERSON><PERSON>, FaSearch } from "react-icons/fa";

export default function AdminJobsPage() {
  const [selectedJobId, setSelectedJobId] = useState<number | null>(null);
  const [detailsModalOpened, setDetailsModalOpened] = useState(false);
  const [rejectModalOpened, setRejectModalOpened] = useState(false);
  const [showFilters, setShowFilters] = useState(false);

  const filterForm = useForm({
    initialValues: {
      search: "",
      status: "all",
      company: "",
      category: "",
      sortBy: "title",
      sortOrder: "asc",
    },
    validate: zodResolver(jobFilterSchema),
  });

  const handleFilterSubmit = () => {
    console.log("Filter values:", filterForm.values);
    // In a real application, you would fetch filtered jobs here
  };

  const handleFilterReset = () => {
    filterForm.reset();
  };

  const handleViewJob = (jobId: number) => {
    setSelectedJobId(jobId);
    setDetailsModalOpened(true);
  };

  const handleApproveJob = (jobId: number) => {
    // In a real application, you would approve the job here
    console.log("Approving job:", jobId);

    notifications.show({
      title: "Job Approved",
      message: "The job has been successfully approved",
      color: "green",
    });
  };

  const handleRejectJobClick = (jobId: number) => {
    setSelectedJobId(jobId);
    setRejectModalOpened(true);
  };

  const confirmRejectJob = (reason: string) => {
    // In a real application, you would reject the job with the reason here
    console.log("Rejecting job:", selectedJobId, "Reason:", reason);

    notifications.show({
      title: "Job Rejected",
      message: "The job has been rejected",
      color: "red",
    });
  };

  return (
    <>
      <AdminNavbar />
      <PageContainer variant="admin">
        {/* Page Title */}
        <PageHeading
          title="Job Management"
          subtitle="View and manage all jobs on the platform"
          variant="admin"
        />

        <div className="mt-6">
          {/* Filter Modal */}
          <Modal
            opened={showFilters}
            onClose={() => setShowFilters(false)}
            title="Filter Jobs"
            size="lg"
            padding="md"
            classNames={{
              header: "mb-2",
              title: "font-semibold text-lg",
              body: "pt-0",
            }}
            centered
            overlayProps={{
              backgroundOpacity: 0.55,
              blur: 3,
            }}
          >
            <JobFilters
              form={filterForm}
              onSubmit={() => {
                handleFilterSubmit();
                setShowFilters(false);
              }}
              onReset={() => {
                handleFilterReset();
                setShowFilters(false);
              }}
            />
          </Modal>

          {/* Content Section */}
          <div>
            <Card shadow="sm" padding="md" radius="md" withBorder>
              {/* Table Header with Actions */}
              <div className="mb-4 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                <div className="flex flex-1 flex-col gap-4 sm:flex-row sm:items-center">
                  <TextInput
                    placeholder="Search jobs..."
                    leftSection={<FaSearch size={14} />}
                    className="flex-1"
                    value={filterForm.values.search}
                    onChange={(e) => {
                      filterForm.setFieldValue("search", e.target.value);
                      // Auto-submit after a short delay
                      if (
                        e.target.value.length > 2 ||
                        e.target.value.length === 0
                      ) {
                        setTimeout(() => handleFilterSubmit(), 500);
                      }
                    }}
                  />
                </div>

                <Group gap="sm">
                  <Button
                    leftSection={<FaFilter size={14} />}
                    variant="light"
                    onClick={() => setShowFilters(!showFilters)}
                  >
                    Filters
                  </Button>
                  <Button
                    leftSection={<FaPlus size={14} />}
                    className="bg-primary-color hover:bg-primary-color/90"
                  >
                    Add Job
                  </Button>
                </Group>
              </div>

              {/* Job Status Tabs */}
              <Tabs defaultValue="all" mb="md">
                <Tabs.List>
                  <Tabs.Tab value="all">
                    All Jobs
                    <Badge
                      size="xs"
                      variant="filled"
                      ml={6}
                      className="bg-gray-500"
                    >
                      120
                    </Badge>
                  </Tabs.Tab>
                  <Tabs.Tab value="active">
                    Active
                    <Badge size="xs" variant="filled" color="green" ml={6}>
                      75
                    </Badge>
                  </Tabs.Tab>
                  <Tabs.Tab value="pending">
                    Pending
                    <Badge size="xs" variant="filled" color="yellow" ml={6}>
                      25
                    </Badge>
                  </Tabs.Tab>
                  <Tabs.Tab value="expired">
                    Expired
                    <Badge size="xs" variant="filled" color="gray" ml={6}>
                      15
                    </Badge>
                  </Tabs.Tab>
                  <Tabs.Tab value="rejected">
                    Rejected
                    <Badge size="xs" variant="filled" color="red" ml={6}>
                      5
                    </Badge>
                  </Tabs.Tab>
                </Tabs.List>
              </Tabs>

              {/* Jobs Table */}
              <JobsTable
                onView={handleViewJob}
                onApprove={handleApproveJob}
                onReject={handleRejectJobClick}
              />

              {/* Pagination */}
              <Group justify="space-between" mt="xl">
                <Text size="sm" c="dimmed">
                  Showing 1-5 of 5 jobs
                </Text>
                <Pagination total={1} />
              </Group>
            </Card>
          </div>
        </div>

        {/* Modals */}
        <JobDetailsModal
          opened={detailsModalOpened}
          onClose={() => setDetailsModalOpened(false)}
          jobId={selectedJobId}
          onApprove={() => handleApproveJob(selectedJobId!)}
          onReject={() => setRejectModalOpened(true)}
        />

        <JobRejectModal
          opened={rejectModalOpened}
          onClose={() => setRejectModalOpened(false)}
          onConfirm={confirmRejectJob}
          jobId={selectedJobId}
        />
      </PageContainer>
    </>
  );
}
