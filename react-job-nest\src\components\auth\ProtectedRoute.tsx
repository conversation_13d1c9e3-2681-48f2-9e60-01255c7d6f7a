"use client";

import { useAuthStore } from "@/stores/auth-store";
import { URLS } from "@/utils/urls";
import { Center, Loader } from "@mantine/core";
import { Navigate, Outlet, useLocation } from "react-router";

type UserRole = "candidate" | "employer" | "admin";

interface ProtectedRouteProps {
  allowedRoles?: UserRole[];
  redirectPath?: string;
}

/**
 * ProtectedRoute component that restricts access to routes based on authentication status and user role
 * @param allowedRoles - Array of roles that are allowed to access the route
 * @param redirectPath - Path to redirect to if the user is not authenticated or not authorized
 */
export default function ProtectedRoute({
  allowedRoles,
  redirectPath = URLS.auth.login,
}: ProtectedRouteProps) {
  const { isLoggedIn, user, isLoading } = useAuthStore();
  const location = useLocation();

  // If the user is not logged in, redirect to the login page
  if (!isLoading && !isLoggedIn) {
    return <Navigate to={redirectPath} state={{ from: location }} replace />;
  }

  // If roles are specified and the user's role is not in the allowed roles, redirect to the home page
  if (
    !isLoading &&
    isLoggedIn &&
    allowedRoles &&
    allowedRoles.length > 0 &&
    user?.role &&
    !allowedRoles.includes(user.role as UserRole)
  ) {
    // Redirect to the appropriate dashboard based on the user's role
    let redirectTo = "/";

    if (user.role === "candidate") {
      redirectTo = URLS.candidate.dashboard;
    } else if (user.role === "employer") {
      redirectTo = URLS.employer.dashboard;
    } else if (user.role === "admin") {
      redirectTo = URLS.admin.dashboard;
    }

    return <Navigate to={redirectTo} replace />;
  }

  // If the user is still loading, show a loading indicator
  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <Center>
          <Loader size="xl" color="blue" />
        </Center>
      </div>
    );
  }

  // If the user is logged in and authorized (or no roles are specified), render the child routes
  return <Outlet />;
}
