# Admin Features for Job Nest

This document outlines the admin features implemented in the Job Nest application.

## Overview

The admin module provides functionality for platform administrators to:

1. View dashboard statistics and analytics
2. Manage users (candidates, employers, and admins)
3. Manage job postings (approve, reject, edit, delete)
4. Manage companies (verify, block/unblock, edit, delete)
5. Configure platform settings

## API Endpoints

### Admin Dashboard

#### Get Dashboard Statistics
- **Endpoint**: `GET /api/admin/dashboard`
- **Description**: Retrieves statistics and analytics for the admin dashboard
- **Authentication**: Required (Admin)
- **Response**: Returns counts, recent activities, and trends

### User Management

#### Get All Users
- **Endpoint**: `GET /api/admin/users`
- **Description**: Retrieves all users with filtering and pagination
- **Authentication**: Required (Admin)
- **Query Parameters**:
  - `page`: Page number (default: 1)
  - `limit`: Number of users per page (default: 10)
  - `role`: Filter by role (all, candidate, employer, admin)
  - `search`: Search by name or email

#### Get User by ID
- **Endpoint**: `GET /api/admin/users/:id`
- **Description**: Retrieves a specific user by ID
- **Authentication**: Required (Admin)

#### Update User
- **Endpoint**: `PATCH /api/admin/users/:id`
- **Description**: Updates a user's information
- **Authentication**: Required (Admin)
- **Request Body**: User data to update

#### Delete User
- **Endpoint**: `DELETE /api/admin/users/:id`
- **Description**: Deletes a user
- **Authentication**: Required (Admin)

### Job Management

#### Get All Jobs
- **Endpoint**: `GET /api/admin/jobs`
- **Description**: Retrieves all jobs with filtering and pagination
- **Authentication**: Required (Admin)
- **Query Parameters**:
  - `page`: Page number (default: 1)
  - `limit`: Number of jobs per page (default: 10)
  - `status`: Filter by status (all, active, inactive)
  - `jobType`: Filter by job type
  - `category`: Filter by category
  - `search`: Search by title or description

#### Get Job Categories
- **Endpoint**: `GET /api/admin/jobs/categories`
- **Description**: Retrieves all job categories
- **Authentication**: Required (Admin)

#### Get Job by ID
- **Endpoint**: `GET /api/admin/jobs/:id`
- **Description**: Retrieves a specific job by ID
- **Authentication**: Required (Admin)

#### Update Job
- **Endpoint**: `PATCH /api/admin/jobs/:id`
- **Description**: Updates a job's information
- **Authentication**: Required (Admin)
- **Request Body**: Job data to update

#### Delete Job
- **Endpoint**: `DELETE /api/admin/jobs/:id`
- **Description**: Deletes a job
- **Authentication**: Required (Admin)

#### Approve Job
- **Endpoint**: `PATCH /api/admin/jobs/approve/:id`
- **Description**: Approves a job posting
- **Authentication**: Required (Admin)

#### Reject Job
- **Endpoint**: `PATCH /api/admin/jobs/reject/:id`
- **Description**: Rejects a job posting
- **Authentication**: Required (Admin)
- **Request Body**: 
  - `reason`: Reason for rejection

### Company Management

#### Get All Companies
- **Endpoint**: `GET /api/admin/companies`
- **Description**: Retrieves all companies with filtering and pagination
- **Authentication**: Required (Admin)
- **Query Parameters**:
  - `page`: Page number (default: 1)
  - `limit`: Number of companies per page (default: 10)
  - `verified`: Filter by verification status (true, false)
  - `active`: Filter by active status (true, false)
  - `industry`: Filter by industry
  - `search`: Search by name or description

#### Get Company Industries
- **Endpoint**: `GET /api/admin/companies/industries`
- **Description**: Retrieves all company industries
- **Authentication**: Required (Admin)

#### Get Company by ID
- **Endpoint**: `GET /api/admin/companies/:id`
- **Description**: Retrieves a specific company by ID
- **Authentication**: Required (Admin)

#### Update Company
- **Endpoint**: `PATCH /api/admin/companies/:id`
- **Description**: Updates a company's information
- **Authentication**: Required (Admin)
- **Request Body**: Company data to update

#### Delete Company
- **Endpoint**: `DELETE /api/admin/companies/:id`
- **Description**: Deletes a company
- **Authentication**: Required (Admin)

#### Verify Company
- **Endpoint**: `PATCH /api/admin/companies/verify/:id`
- **Description**: Verifies a company
- **Authentication**: Required (Admin)

#### Toggle Company Status
- **Endpoint**: `PATCH /api/admin/companies/toggle-status/:id`
- **Description**: Blocks or unblocks a company
- **Authentication**: Required (Admin)
- **Request Body**: 
  - `reason`: Reason for blocking/unblocking

## Integration with Frontend

The frontend should:

1. Provide an admin dashboard with statistics and analytics
2. Display user management interface with filtering and search
3. Display job management interface with approval/rejection functionality
4. Display company management interface with verification functionality
5. Provide settings configuration interface
