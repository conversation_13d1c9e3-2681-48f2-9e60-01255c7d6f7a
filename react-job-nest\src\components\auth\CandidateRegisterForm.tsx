"use client";

import { candidateRegisterSchema } from "@/schemas/auth/candidate-register-schema";
import { useAuthStore } from "@/stores/auth-store";
import { type RegisterData } from "@/types/auth";
import { URLS } from "@/utils/urls";
import {
  Button,
  LoadingOverlay,
  PasswordInput,
  TextInput,
} from "@mantine/core";
import { useForm } from "@mantine/form";
import { zodResolver } from "mantine-form-zod-resolver";
import { useState } from "react";
import { FaEnvelope, FaLock, FaUser } from "react-icons/fa";
import { Link, useNavigate } from "react-router";

export default function CandidateRegisterForm() {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm({
    initialValues: {
      name: "",
      email: "",
      password: "",
      passwordConfirmation: "",
      role: "candidate" as const,
    },
    validate: zod<PERSON><PERSON><PERSON><PERSON>(candidateRegisterSchema),
  });

  const handleSubmit = async (values: RegisterData) => {
    setIsLoading(true);

    try {
      // Get register function from auth store
      const { register } = useAuthStore.getState();

      // Call register function
      const success = await register(values);

      if (success) {
        // Get the updated user after registration
        const { user } = useAuthStore.getState();

        if (user) {
          // Redirect to candidate dashboard
          navigate(URLS.candidate.dashboard);
        } else {
          // If user is not available yet, redirect to home page
          navigate("/");
        }
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="relative">
      <LoadingOverlay visible={isLoading} overlayProps={{ blur: 2 }} />

      <form
        className="flex flex-col gap-4"
        onSubmit={form.onSubmit(handleSubmit)}
      >
        <TextInput
          label="Full Name"
          placeholder="John Doe"
          required
          leftSection={<FaUser />}
          {...form.getInputProps("name")}
        />

        <TextInput
          label="Email"
          placeholder="<EMAIL>"
          required
          leftSection={<FaEnvelope />}
          {...form.getInputProps("email")}
        />

        <PasswordInput
          label="Password"
          placeholder="Your password"
          required
          leftSection={<FaLock />}
          {...form.getInputProps("password")}
        />

        <PasswordInput
          label="Confirm Password"
          placeholder="Confirm your password"
          required
          leftSection={<FaLock />}
          {...form.getInputProps("passwordConfirmation")}
        />

        <Button type="submit" fullWidth mt="xl" loading={isLoading}>
          Create Account
        </Button>

        <div className="mt-4 text-center">
          <Link
            to="/auth/login"
            className="text-sm text-blue-600 hover:underline"
          >
            Already have an account? Login
          </Link>
        </div>
      </form>
    </div>
  );
}
