import { Request, Response, NextFunction } from "express";
import { catchAsync } from "../utils/catch-async";
import { AppError } from "../utils/app-error";
import Notification, { NotificationType } from "../models/notification.model";
import User from "../models/user.model";
import emailService from "../services/email.service";

// Create a notification
export const createNotification = catchAsync(
  async (
    recipientId: string,
    type: NotificationType,
    title: string,
    message: string,
    relatedEntities?: {
      user?: string;
      job?: string;
      company?: string;
      application?: string;
      subscription?: string;
    },
    metadata?: Record<string, any>,
    sendEmail: boolean = false
  ) => {
    // Create the notification
    const notification = await Notification.create({
      recipient: recipientId,
      type,
      title,
      message,
      relatedEntities,
      metadata,
      isEmailSent: false,
    });

    // Send email if requested
    if (sendEmail) {
      // Get recipient's email
      const recipient = await User.findById(recipientId);

      if (recipient && recipient.email) {
        // Prepare data for email template
        const emailData = {
          ...metadata,
          title,
          message,
          name: recipient.name,
        };

        // Send the email
        const emailSent = await emailService.sendNotificationEmail(
          recipient.email,
          type,
          emailData
        );

        // Update notification if email was sent
        if (emailSent) {
          notification.isEmailSent = true;
          await notification.save();
        }
      }
    }

    return notification;
  }
);

// Get current user's notifications
export const getMyNotifications = catchAsync(
  async (req: Request, res: Response, _next: NextFunction) => {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const skip = (page - 1) * limit;
    const unreadOnly = req.query.unread === "true";

    // Build query
    const query: any = { recipient: req.user._id };

    if (unreadOnly) {
      query.isRead = false;
    }

    // Get notifications with pagination
    const notifications = await Notification.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .populate("relatedEntities.user", "name email")
      .populate("relatedEntities.job", "title company")
      .populate("relatedEntities.company", "name")
      .populate("relatedEntities.application", "status");

    // Get total count
    const totalNotifications = await Notification.countDocuments(query);

    // Get unread count
    const unreadCount = await Notification.countDocuments({
      recipient: req.user._id,
      isRead: false,
    });

    res.status(200).json({
      success: true,
      count: notifications.length,
      totalPages: Math.ceil(totalNotifications / limit),
      currentPage: page,
      unreadCount,
      notifications,
    });
  }
);

// Mark notification as read
export const markNotificationAsRead = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    const { notificationId } = req.params;

    // Find the notification
    const notification = await Notification.findById(notificationId);

    if (!notification) {
      return next(new AppError("Notification not found", 404));
    }

    // Check if the notification belongs to the current user
    if (
      notification.recipient.toString() !== (req.user as any)._id.toString()
    ) {
      return next(
        new AppError("You are not authorized to access this notification", 403)
      );
    }

    // Mark as read
    notification.isRead = true;
    await notification.save();

    res.status(200).json({
      success: true,
      message: "Notification marked as read",
    });
  }
);

// Mark all notifications as read
export const markAllNotificationsAsRead = catchAsync(
  async (req: Request, res: Response, _next: NextFunction) => {
    // Update all unread notifications for the current user
    await Notification.updateMany(
      { recipient: req.user._id, isRead: false },
      { isRead: true }
    );

    res.status(200).json({
      success: true,
      message: "All notifications marked as read",
    });
  }
);

// Delete a notification
export const deleteNotification = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    const { notificationId } = req.params;

    // Find the notification
    const notification = await Notification.findById(notificationId);

    if (!notification) {
      return next(new AppError("Notification not found", 404));
    }

    // Check if the notification belongs to the current user
    if (
      notification.recipient.toString() !== (req.user as any)._id.toString()
    ) {
      return next(
        new AppError("You are not authorized to delete this notification", 403)
      );
    }

    // Delete the notification
    await Notification.findByIdAndDelete(notificationId);

    res.status(200).json({
      success: true,
      message: "Notification deleted successfully",
    });
  }
);

// Delete all notifications
export const deleteAllNotifications = catchAsync(
  async (req: Request, res: Response, _next: NextFunction) => {
    // Delete all notifications for the current user
    await Notification.deleteMany({ recipient: req.user._id });

    res.status(200).json({
      success: true,
      message: "All notifications deleted successfully",
    });
  }
);
