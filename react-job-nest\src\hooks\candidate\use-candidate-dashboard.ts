import candidateApi from "@/services/candidate-api";
import { useAuthStore } from "@/stores/auth-store";
import { notifications } from "@mantine/notifications";
import { useEffect, useState } from "react";

// Dashboard stats interface
interface CandidateDashboardStats {
  total: number;
  pending: number;
  reviewing: number;
  shortlisted: number;
  interview: number;
  offered: number;
  hired: number;
  rejected: number;
  successRate: number;
  applicationTrends: Array<{
    year: number;
    week: number;
    count: number;
    date: string;
  }>;
  jobCategories: Array<{ category: string; count: number }>;
  recentApplications: Array<{
    _id: string;
    status: string;
    createdAt: string;
    job: {
      _id: string;
      title: string;
      category: string;
      jobType: string;
      location: string;
      minSalary?: number;
      maxSalary?: number;
      company: {
        _id: string;
        name: string;
        logo?: string;
      };
    };
  }>;
}

// Recommended job interface
interface RecommendedJob {
  _id: string;
  title: string;
  category: string;
  jobType: string;
  location: string;
  minSalary?: number;
  maxSalary?: number;
  description: string;
  requirements: string[];
  isActive: boolean;
  createdAt: string;
  company: {
    _id: string;
    name: string;
    logo?: string;
  };
  matchScore?: number;
}

export default function useCandidateDashboard() {
  const [isLoading, setIsLoading] = useState(true);
  const [stats, setStats] = useState<CandidateDashboardStats>({
    total: 0,
    pending: 0,
    reviewing: 0,
    shortlisted: 0,
    interview: 0,
    offered: 0,
    hired: 0,
    rejected: 0,
    successRate: 0,
    applicationTrends: [],
    jobCategories: [],
    recentApplications: [],
  });
  const [recommendedJobs, setRecommendedJobs] = useState<RecommendedJob[]>([]);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuthStore();

  useEffect(() => {
    const fetchDashboardData = async () => {
      if (!user) {
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);

        // Fetch application statistics
        const statsResponse =
          await candidateApi.recommendations.getApplicationStats();
        const statsData = statsResponse.data;

        if (statsData.success) {
          setStats(statsData.stats);
        } else {
          throw new Error("Failed to fetch application stats");
        }

        // Fetch recommended jobs
        const jobsResponse =
          await candidateApi.recommendations.getRecommendedJobs();
        const jobsData = jobsResponse.data;

        if (jobsData.success) {
          setRecommendedJobs(jobsData.jobs || []);
        } else {
          throw new Error("Failed to fetch recommended jobs");
        }
      } catch (error: any) {
        console.error("Error fetching candidate dashboard data:", error);
        setError(error.message || "Failed to load dashboard data");

        // Show error notification
        notifications.show({
          title: "Error",
          message: "Failed to load dashboard data. Please try again.",
          color: "red",
        });

        // Reset to empty data on error
        setStats({
          total: 0,
          pending: 0,
          reviewing: 0,
          shortlisted: 0,
          interview: 0,
          offered: 0,
          hired: 0,
          rejected: 0,
          successRate: 0,
          applicationTrends: [],
          jobCategories: [],
          recentApplications: [],
        });
        setRecommendedJobs([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchDashboardData();
  }, [user]);

  return {
    isLoading,
    stats,
    recommendedJobs,
    error,
  };
}
