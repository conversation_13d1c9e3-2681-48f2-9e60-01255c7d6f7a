import { useThemeClasses } from "@/design-system/utils/theme-utils";
import {
  ActionIcon,
  Badge,
  Button,
  Group,
  Text,
  useMantineColorScheme,
} from "@mantine/core";
import {
  FaBriefcase,
  FaBuilding,
  FaCalendarAlt,
  FaGraduationCap,
  FaMapMarkerAlt,
  FaSearch,
  FaTags,
  FaTimes,
} from "react-icons/fa";

export default function FilterBadges({
  searchTerm,
  filters,
  removeFilter,
}: {
  searchTerm?: string;
  filters: Record<string, string>;
  removeFilter: (filterType: string) => void;
}) {
  const { colorScheme } = useMantineColorScheme();
  const isDark = colorScheme === "dark";

  // Get the total number of active filters
  const activeFiltersCount =
    (searchTerm ? 1 : 0) + Object.values(filters).filter(Boolean).length;

  // If there are no active filters, return null
  if (activeFiltersCount === 0) {
    return null;
  }

  // Map filter types to icons
  const getFilterIcon = (filterType: string) => {
    switch (filterType) {
      case "search":
        return <FaSearch size={10} />;
      case "jobType":
        return <FaBriefcase size={10} />;
      case "workType":
      case "location":
        return <FaMapMarkerAlt size={10} />;
      case "category":
        return <FaTags size={10} />;
      case "datePosted":
        return <FaCalendarAlt size={10} />;
      case "experience":
      case "careerLevel":
        return <FaGraduationCap size={10} />;
      case "company":
        return <FaBuilding size={10} />;
      default:
        return null;
    }
  };

  // Format filter label
  const formatFilterLabel = (key: string) => {
    return key
      .replace(/([A-Z])/g, " $1") // Insert a space before all capital letters
      .replace(/^./, (str) => str.toUpperCase()); // Capitalize the first letter
  };

  return (
    <div>
      <Group justify="space-between" mb="xs" wrap="nowrap">
        <Text size="sm" fw={500} className="whitespace-nowrap">
          Active Filters ({activeFiltersCount})
        </Text>
        {activeFiltersCount > 0 && (
          <Button
            variant="subtle"
            size="xs"
            onClick={() => {
              if (searchTerm) removeFilter("search");
              Object.keys(filters).forEach((key) => {
                if (filters[key]) removeFilter(key);
              });
            }}
            className="whitespace-nowrap"
            px="xs"
          >
            Clear All
          </Button>
        )}
      </Group>

      <div className="flex flex-wrap gap-2">
        {searchTerm && (
          <Badge
            variant="filled"
            color="blue.6"
            radius="sm"
            size="sm"
            leftSection={getFilterIcon("search")}
            rightSection={
              <ActionIcon
                size="xs"
                variant="transparent"
                onClick={() => removeFilter("search")}
                className="text-white"
              >
                <FaTimes size={12} />
              </ActionIcon>
            }
            className="mb-1"
            styles={{
              root: {
                backgroundColor: isDark
                  ? "var(--mantine-color-blue-8) !important"
                  : undefined,
              },
            }}
          >
            {searchTerm.length > 15
              ? `${searchTerm.substring(0, 15)}...`
              : searchTerm}
          </Badge>
        )}

        {Object.entries(filters).map(([key, value]) =>
          value ? (
            <Badge
              key={key}
              variant="light"
              color="gray.7"
              radius="sm"
              size="sm"
              leftSection={getFilterIcon(key)}
              rightSection={
                <ActionIcon
                  size="xs"
                  variant="transparent"
                  onClick={() => removeFilter(key)}
                  className={useThemeClasses("", "text-gray-300")}
                >
                  <FaTimes size={12} />
                </ActionIcon>
              }
              className="mb-1"
              styles={{
                root: {
                  backgroundColor: isDark
                    ? "var(--mantine-color-dark-6) !important"
                    : undefined,
                  color: isDark
                    ? "var(--mantine-color-gray-3) !important"
                    : undefined,
                },
              }}
            >
              <span className="max-w-[150px] truncate">
                {`${formatFilterLabel(key)}: ${value}`}
              </span>
            </Badge>
          ) : null,
        )}
      </div>
    </div>
  );
}
