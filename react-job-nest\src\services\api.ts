import { cache } from "@/utils/cache";
import { getApiBaseUrl } from "@/utils/endpoints";
import axios, {
  AxiosError,
  type AxiosInstance,
  type AxiosRequestConfig,
  type AxiosResponse,
} from "axios";

// Define the base API configuration
const apiConfig: AxiosRequestConfig = {
  baseURL: getApiBaseUrl(), // Use the API base URL from endpoints.ts
  headers: {
    "Content-Type": "application/json",
  },
  withCredentials: true, // Include cookies in requests
};

// Create the API instance
const apiClient: AxiosInstance = axios.create(apiConfig);

// Request interceptor for adding auth token
apiClient.interceptors.request.use(
  (config) => {
    // Get the token from localStorage or cache
    const token = cache.get("accessToken");

    // If token exists, add it to the headers
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // Log the request in development mode
    if (process.env.NODE_ENV === "development") {
      console.log(
        `API Request: ${config.method?.toUpperCase()} ${config.baseURL}${config.url}`,
      );
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  },
);

// Response interceptor for handling errors
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    // Log successful responses in development mode
    if (process.env.NODE_ENV === "development") {
      console.log(
        `API Response Success: ${response.config.method?.toUpperCase()} ${response.config.url}`,
        {
          status: response.status,
          statusText: response.statusText,
        },
      );
    }
    return response;
  },
  async (error: AxiosError) => {
    const originalRequest = error.config as AxiosRequestConfig & {
      _retry?: boolean;
    };

    // Log the error in development mode
    if (process.env.NODE_ENV === "development") {
      console.warn("API Error:", {
        status: error.response?.status,
        statusText: error.response?.statusText,
        url: originalRequest.url,
        method: originalRequest.method,
        data: error.response?.data,
        // If the response is HTML, log a warning
        isHtml:
          error.response?.headers?.["content-type"]?.includes("text/html"),
      });

      // If the response is HTML, log a special warning
      if (error.response?.headers?.["content-type"]?.includes("text/html")) {
        console.error(
          "Received HTML response instead of JSON. This usually means the API endpoint doesn't exist or CORS is not configured correctly.",
        );
      }
    }

    // Handle 401 Unauthorized errors (token expired)
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        // Only clear the token, but don't redirect
        // This prevents infinite loops when checking auth status
        cache.clear();

        // Don't redirect automatically - let the component handle it
        // window.location.href = "/auth/login";

        return Promise.reject(error);
      } catch (refreshError) {
        return Promise.reject(refreshError);
      }
    }

    // In development mode, if the backend is not available, we can simulate a successful response
    if (
      process.env.NODE_ENV === "development" &&
      (!error.response ||
        error.response.status === 404 ||
        error.message.includes("Network Error"))
    ) {
      console.warn(
        "Backend not available in development mode. Consider mocking the response in the component.",
      );
      console.info(
        "Troubleshooting tips:\n" +
          "1. Make sure your Express server is running on http://localhost:5000\n" +
          "2. Check that CORS is enabled in your Express server\n" +
          "3. Verify that the endpoint exists and is correctly implemented\n" +
          "4. Check the browser console for more detailed error information",
      );
    }

    return Promise.reject(error);
  },
);

// API service with methods for common operations
const api = {
  // Auth endpoints
  auth: {
    login: (credentials: { email: string; password: string }) =>
      apiClient.post("/auth/login", credentials),

    register: (data: any) => apiClient.post("/auth/register", data),

    logout: () => apiClient.post("/auth/logout"),

    getCurrentUser: () => apiClient.get("/auth/me"),

    forgotPassword: (email: string) =>
      apiClient.post("/auth/forgot-password", { email }),

    resetPassword: (
      token: string,
      password: string,
      passwordConfirmation: string,
    ) =>
      apiClient.post(`/auth/reset-password/${token}`, {
        password,
        passwordConfirmation,
      }),
  },

  // Generic methods for CRUD operations
  get: <T>(url: string, config?: AxiosRequestConfig) =>
    apiClient.get<T>(url, config),

  post: <T>(url: string, data?: any, config?: AxiosRequestConfig) =>
    apiClient.post<T>(url, data, config),

  put: <T>(url: string, data?: any, config?: AxiosRequestConfig) =>
    apiClient.put<T>(url, data, config),

  patch: <T>(url: string, data?: any, config?: AxiosRequestConfig) =>
    apiClient.patch<T>(url, data, config),

  delete: <T>(url: string, config?: AxiosRequestConfig) =>
    apiClient.delete<T>(url, config),
};

export default api;
