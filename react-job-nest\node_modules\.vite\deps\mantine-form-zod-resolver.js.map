{"version": 3, "sources": ["../../mantine-form-zod-resolver/dist/esm/index.mjs"], "sourcesContent": ["function zodResolver(schema, options) {\n  return (values) => {\n    const parsed = schema.safeParse(values);\n    if (parsed.success) {\n      return {};\n    }\n    const results = {};\n    if (\"error\" in parsed) {\n      if (options?.errorPriority === \"first\") {\n        parsed.error.errors.reverse();\n      }\n      parsed.error.errors.forEach((error) => {\n        results[error.path.join(\".\")] = error.message;\n      });\n    }\n    return results;\n  };\n}\n\nexport { zodResolver };\n"], "mappings": ";;;AAAA,SAAS,YAAY,QAAQ,SAAS;AACpC,SAAO,CAAC,WAAW;AACjB,UAAM,SAAS,OAAO,UAAU,MAAM;AACtC,QAAI,OAAO,SAAS;AAClB,aAAO,CAAC;AAAA,IACV;AACA,UAAM,UAAU,CAAC;AACjB,QAAI,WAAW,QAAQ;AACrB,WAAI,mCAAS,mBAAkB,SAAS;AACtC,eAAO,MAAM,OAAO,QAAQ;AAAA,MAC9B;AACA,aAAO,MAAM,OAAO,QAAQ,CAAC,UAAU;AACrC,gBAAQ,MAAM,KAAK,KAAK,GAAG,CAAC,IAAI,MAAM;AAAA,MACxC,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT;AACF;", "names": []}