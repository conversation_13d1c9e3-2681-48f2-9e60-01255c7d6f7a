import { z } from "zod";

export const userFilterSchema = z.object({
  search: z.string().optional(),
  role: z.enum(["all", "candidate", "employer", "admin"]).optional(),
  status: z.enum(["all", "active", "inactive", "blocked"]).optional(),
  sortBy: z.enum(["name", "email", "role", "createdAt"]).optional(),
  sortOrder: z.enum(["asc", "desc"]).optional(),
});

export const userEditSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  email: z.string().email("Invalid email address"),
  role: z.enum(["candidate", "employer", "admin"]),
  status: z.enum(["active", "inactive", "blocked"]),
  phone: z.string().optional(),
  address: z.string().optional(),
  bio: z.string().optional(),
});

export const userCreateSchema = userEditSchema
  .extend({
    password: z
      .string()
      .min(8, "Password must be at least 8 characters")
      .regex(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
        "Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character",
      ),
    passwordConfirmation: z.string(),
  })
  .refine((data) => data.password === data.passwordConfirmation, {
    message: "Passwords do not match",
    path: ["passwordConfirmation"],
  });
