import nodemailer from "nodemailer";
import { NotificationType } from "../models/notification.model";
import Settings from "../models/settings.model";

interface EmailOptions {
  to: string;
  subject: string;
  html: string;
  from?: string;
}

interface TemplateData {
  [key: string]: any;
}

class EmailService {
  private transporter: nodemailer.Transporter;
  private defaultFromEmail: string;
  private defaultFromName: string;

  constructor() {
    // Initialize with default values
    this.defaultFromEmail = process.env.EMAIL_FROM || "<EMAIL>";
    this.defaultFromName = process.env.EMAIL_FROM_NAME || "JobNest";

    // Create transporter with default settings
    this.transporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST || "smtp.mailtrap.io",
      port: parseInt(process.env.SMTP_PORT || "2525"),
      auth: {
        user: process.env.SMTP_USERNAME || "",
        pass: process.env.SMTP_PASSWORD || "",
      },
    });

    // Load settings from database when available
    this.loadSettings();
  }

  // Load email settings from database
  public async loadSettings() {
    try {
      const settings = await Settings.findOne();
      if (settings && settings.email) {
        this.defaultFromEmail =
          settings.email.fromEmail || this.defaultFromEmail;
        this.defaultFromName = settings.email.fromName || this.defaultFromName;

        // Update transporter with settings from database
        this.transporter = nodemailer.createTransport({
          host: settings.email.smtpHost,
          port: settings.email.smtpPort,
          auth: {
            user: settings.email.smtpUsername,
            pass: settings.email.smtpPassword,
          },
        });
      }
    } catch (error) {
      console.error("Error loading email settings:", error);
    }
  }

  // Send email
  public async sendEmail(options: EmailOptions): Promise<boolean> {
    try {
      const mailOptions = {
        from:
          options.from ||
          `"${this.defaultFromName}" <${this.defaultFromEmail}>`,
        to: options.to,
        subject: options.subject,
        html: options.html,
      };

      await this.transporter.sendMail(mailOptions);
      return true;
    } catch (error) {
      console.error("Error sending email:", error);
      return false;
    }
  }

  // Get email template based on notification type
  private getEmailTemplate(
    type: NotificationType,
    data: TemplateData
  ): { subject: string; html: string } {
    switch (type) {
      case "welcome":
        return {
          subject: "Welcome to JobNest!",
          html: `
            <h1>Welcome to JobNest, ${data.name}!</h1>
            <p>Thank you for joining our platform. We're excited to help you find your dream job or perfect candidate.</p>
            <p>Get started by completing your profile and exploring our features.</p>
            <a href="${data.loginUrl}" style="display: inline-block; padding: 10px 20px; background-color: #4A6CF7; color: white; text-decoration: none; border-radius: 5px;">Login to Your Account</a>
          `,
        };

      case "password_reset":
        return {
          subject: "Reset Your Password",
          html: `
            <h1>Password Reset Request</h1>
            <p>You requested a password reset. Click the button below to set a new password:</p>
            <a href="${data.resetUrl}" style="display: inline-block; padding: 10px 20px; background-color: #4A6CF7; color: white; text-decoration: none; border-radius: 5px;">Reset Password</a>
            <p>If you didn't request this, please ignore this email.</p>
          `,
        };

      case "job_applied":
        return {
          subject: "Application Submitted Successfully",
          html: `
            <h1>Application Submitted</h1>
            <p>Your application for <strong>${data.jobTitle}</strong> at <strong>${data.companyName}</strong> has been submitted successfully.</p>
            <p>You can track the status of your application in your dashboard.</p>
            <a href="${data.applicationUrl}" style="display: inline-block; padding: 10px 20px; background-color: #4A6CF7; color: white; text-decoration: none; border-radius: 5px;">View Application</a>
          `,
        };

      case "new_application":
        return {
          subject: "New Job Application Received",
          html: `
            <h1>New Application Received</h1>
            <p>A new application has been submitted for <strong>${data.jobTitle}</strong>.</p>
            <p><strong>Candidate:</strong> ${data.candidateName}</p>
            <p>Review the application in your employer dashboard.</p>
            <a href="${data.applicationUrl}" style="display: inline-block; padding: 10px 20px; background-color: #4A6CF7; color: white; text-decoration: none; border-radius: 5px;">View Application</a>
          `,
        };

      case "job_application_status":
        return {
          subject: "Application Status Update",
          html: `
            <h1>Application Status Update</h1>
            <p>The status of your application for <strong>${data.jobTitle}</strong> at <strong>${data.companyName}</strong> has been updated to <strong>${data.status}</strong>.</p>
            <a href="${data.applicationUrl}" style="display: inline-block; padding: 10px 20px; background-color: #4A6CF7; color: white; text-decoration: none; border-radius: 5px;">View Details</a>
          `,
        };

      // Add more templates as needed

      default:
        return {
          subject: "Notification from JobNest",
          html: `
            <h1>${data.title || "Notification"}</h1>
            <p>${
              data.message || "You have a new notification from JobNest."
            }</p>
          `,
        };
    }
  }

  // Send notification email
  public async sendNotificationEmail(
    to: string,
    type: NotificationType,
    data: TemplateData
  ): Promise<boolean> {
    const template = this.getEmailTemplate(type, data);

    return this.sendEmail({
      to,
      subject: template.subject,
      html: template.html,
    });
  }
}

// Create a singleton instance
const emailService = new EmailService();

export default emailService;
