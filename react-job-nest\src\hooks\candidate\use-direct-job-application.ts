import candidateApi from "@/services/candidate-api";
import { notifications } from "@mantine/notifications";
import { useState } from "react";
import useJobDetails from "./use-job-details";

interface UseDirectJobApplicationReturn {
  jobDetails: any;
  isLoading: boolean;
  isSubmitting: boolean;
  error: string | null;
  handleApply: () => Promise<boolean>;
}

export default function useDirectJobApplication(
  jobId: string | undefined,
): UseDirectJobApplicationReturn {
  const { jobDetails, isLoading, error } = useJobDetails(jobId);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Handle direct application submission
  const handleApply = async (): Promise<boolean> => {
    if (!jobId) {
      notifications.show({
        title: "Error",
        message: "Job ID is required",
        color: "red",
      });
      return false;
    }

    try {
      setIsSubmitting(true);

      // Create a simple application with minimal required data
      const applicationData = {
        job: jobId,
        // In a real implementation, you would upload the file to a server
        // For now, we'll use a placeholder URL
        resume: "https://example.com/resume.pdf",
        // Either omit questionAnswers entirely or provide a valid entry
        // We'll include a default answer to satisfy the schema requirements
        questionAnswers: [
          {
            question: "Automatic Application",
            answer: "Applied directly from job listing",
          },
        ],
      };

      console.log("Submitting direct application:", applicationData);

      const response =
        await candidateApi.applications.submitApplication(applicationData);
      console.log("Application response:", response);

      // Show success notification
      notifications.show({
        title: "Application Submitted",
        message: "Your application has been submitted successfully!",
        color: "green",
      });

      return true;
    } catch (error: any) {
      console.error("Error submitting application:", error);

      // Extract error message from the response if available
      let errorMessage = "Failed to submit application";

      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        console.log("Error response data:", error.response.data);

        if (error.response.data && error.response.data.message) {
          errorMessage = error.response.data.message;
        } else if (error.response.data && error.response.data.error) {
          errorMessage = error.response.data.error;
        } else if (error.response.status === 400) {
          errorMessage = "Invalid application data. Please try again.";
        } else if (error.response.status === 401) {
          errorMessage = "You must be logged in to apply for jobs.";
        } else if (error.response.status === 403) {
          errorMessage = "You don't have permission to apply for this job.";
        }
      } else if (error.request) {
        // The request was made but no response was received
        errorMessage = "No response from server. Please check your connection.";
      } else {
        // Something happened in setting up the request that triggered an Error
        errorMessage = error.message || "Failed to submit application";
      }

      // Show error notification
      notifications.show({
        title: "Error",
        message: errorMessage,
        color: "red",
      });

      return false;
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    jobDetails,
    isLoading,
    isSubmitting,
    error,
    handleApply,
  };
}
