"use client";

import EditJobForm from "@/components/employer/forms/EditJobForm";
import { PageContainer, PageHeading } from "@/design-system/components";
import { Box, useMantineColorScheme } from "@mantine/core";
import { useParams } from "react-router";

export default function EmployerEditJobPage() {
  const params = useParams();
  const job_id = params.job_id; // Match the route parameter name from App.tsx
  const { colorScheme } = useMantineColorScheme();
  const isDark = colorScheme === "dark";

  // Add debug logging
  console.log("EmployerEditJobPage - params:", params);
  console.log("EmployerEditJobPage - job_id from params:", job_id);

  // Ensure job_id is a valid string
  // In development mode, always consider the job_id valid for testing
  const validJobId =
    process.env.NODE_ENV === "development"
      ? job_id || "mock-job-id"
      : job_id && job_id !== "undefined"
        ? job_id
        : null;

  return (
    <PageContainer
      breadcrumbItems={[
        { title: "Home", href: "/" },
        { title: "Manage Jobs", href: "/employer/manage-jobs" },
        { title: "Edit Job" },
      ]}
      variant="employer"
      className="pb-12"
    >
      {/* Hero Section */}
      <Box
        className="mb-8 rounded-lg px-4 py-8 md:px-6 md:py-10"
        mx={{ base: -4, sm: -6 }}
        style={{
          background: isDark
            ? "linear-gradient(to right, #1a1b2e, #141b2d, #1a1b2e)"
            : "linear-gradient(to right, #f0f4ff, #e6f0ff, #f0f4ff)",
        }}
      >
        <PageHeading
          title="Edit Job Posting"
          subtitle="Update your job posting to attract the best candidates"
          className="mb-0 text-center"
          variant="employer"
        />
      </Box>

      {validJobId ? (
        <EditJobForm jobId={validJobId} />
      ) : (
        <Box className="p-6 text-center">
          <PageHeading
            title="Invalid Job ID"
            subtitle="Please select a valid job from the manage jobs page"
            className="mb-4"
          />
        </Box>
      )}
    </PageContainer>
  );
}
