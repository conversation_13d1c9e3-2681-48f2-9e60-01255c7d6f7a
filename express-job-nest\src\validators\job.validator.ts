import { body } from 'express-validator';

export const createJobValidator = [
  body('title')
    .notEmpty().withMessage('Job title is required')
    .isLength({ min: 3, max: 100 }).withMessage('Job title must be between 3 and 100 characters'),
  
  body('company')
    .notEmpty().withMessage('Company ID is required')
    .isMongoId().withMessage('Invalid company ID format'),
  
  body('description')
    .notEmpty().withMessage('Job description is required')
    .isLength({ min: 10 }).withMessage('Job description must be at least 10 characters long'),
  
  body('requirements')
    .notEmpty().withMessage('Job requirements are required')
    .isLength({ min: 10 }).withMessage('Job requirements must be at least 10 characters long'),
  
  body('jobType')
    .notEmpty().withMessage('Job type is required')
    .isIn(['full-time', 'part-time', 'contract', 'internship', 'temporary'])
    .withMessage('Job type must be one of: full-time, part-time, contract, internship, temporary'),
  
  body('location')
    .notEmpty().withMessage('Job location is required')
    .isIn(['remote', 'onsite', 'hybrid'])
    .withMessage('Job location must be one of: remote, onsite, hybrid'),
  
  body('category')
    .notEmpty().withMessage('Job category is required'),
  
  body('tags')
    .optional()
    .isArray().withMessage('Tags must be an array'),
  
  body('minSalary')
    .optional()
    .isNumeric().withMessage('Minimum salary must be a number')
    .custom((value, { req }) => {
      if (value < 0) {
        throw new Error('Minimum salary cannot be negative');
      }
      return true;
    }),
  
  body('maxSalary')
    .optional()
    .isNumeric().withMessage('Maximum salary must be a number')
    .custom((value, { req }) => {
      if (value < 0) {
        throw new Error('Maximum salary cannot be negative');
      }
      if (req.body.minSalary && value < req.body.minSalary) {
        throw new Error('Maximum salary must be greater than minimum salary');
      }
      return true;
    }),
  
  body('currency')
    .optional()
    .isString().withMessage('Currency must be a string'),
  
  body('showSalary')
    .optional()
    .isBoolean().withMessage('Show salary must be a boolean'),
  
  body('benefits')
    .optional()
    .isArray().withMessage('Benefits must be an array'),
  
  body('applicationDeadline')
    .optional()
    .isISO8601().withMessage('Application deadline must be a valid date')
    .custom((value) => {
      const deadline = new Date(value);
      const now = new Date();
      if (deadline < now) {
        throw new Error('Application deadline cannot be in the past');
      }
      return true;
    }),
  
  body('isActive')
    .optional()
    .isBoolean().withMessage('Is active must be a boolean'),
  
  body('questions')
    .optional()
    .isArray().withMessage('Questions must be an array'),
  
  body('requiredExperience')
    .notEmpty().withMessage('Required experience is required')
    .isString().withMessage('Required experience must be a string'),
];

export const updateJobValidator = [
  body('title')
    .optional()
    .isLength({ min: 3, max: 100 }).withMessage('Job title must be between 3 and 100 characters'),
  
  body('description')
    .optional()
    .isLength({ min: 10 }).withMessage('Job description must be at least 10 characters long'),
  
  body('requirements')
    .optional()
    .isLength({ min: 10 }).withMessage('Job requirements must be at least 10 characters long'),
  
  body('jobType')
    .optional()
    .isIn(['full-time', 'part-time', 'contract', 'internship', 'temporary'])
    .withMessage('Job type must be one of: full-time, part-time, contract, internship, temporary'),
  
  body('location')
    .optional()
    .isIn(['remote', 'onsite', 'hybrid'])
    .withMessage('Job location must be one of: remote, onsite, hybrid'),
  
  body('category')
    .optional(),
  
  body('tags')
    .optional()
    .isArray().withMessage('Tags must be an array'),
  
  body('minSalary')
    .optional()
    .isNumeric().withMessage('Minimum salary must be a number')
    .custom((value, { req }) => {
      if (value < 0) {
        throw new Error('Minimum salary cannot be negative');
      }
      return true;
    }),
  
  body('maxSalary')
    .optional()
    .isNumeric().withMessage('Maximum salary must be a number')
    .custom((value, { req }) => {
      if (value < 0) {
        throw new Error('Maximum salary cannot be negative');
      }
      if (req.body.minSalary && value < req.body.minSalary) {
        throw new Error('Maximum salary must be greater than minimum salary');
      }
      return true;
    }),
  
  body('currency')
    .optional()
    .isString().withMessage('Currency must be a string'),
  
  body('showSalary')
    .optional()
    .isBoolean().withMessage('Show salary must be a boolean'),
  
  body('benefits')
    .optional()
    .isArray().withMessage('Benefits must be an array'),
  
  body('applicationDeadline')
    .optional()
    .isISO8601().withMessage('Application deadline must be a valid date')
    .custom((value) => {
      const deadline = new Date(value);
      const now = new Date();
      if (deadline < now) {
        throw new Error('Application deadline cannot be in the past');
      }
      return true;
    }),
  
  body('isActive')
    .optional()
    .isBoolean().withMessage('Is active must be a boolean'),
  
  body('questions')
    .optional()
    .isArray().withMessage('Questions must be an array'),
  
  body('requiredExperience')
    .optional()
    .isString().withMessage('Required experience must be a string'),
];
