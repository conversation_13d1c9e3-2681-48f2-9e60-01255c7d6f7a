"use client";

import { <PERSON><PERSON>, Divider, Group, SegmentedControl, Title } from "@mantine/core";
import { useState } from "react";
import { FaBriefcase, FaUser } from "react-icons/fa";
import { Link } from "react-router";
import CandidateRegisterForm from "./CandidateRegisterForm";
import EmployerRegisterForm from "./EmployerRegisterForm";

interface RegisterFormProps {
  initialRole?: "candidate" | "employer" | null;
}

export default function RegisterForm({
  initialRole = null,
}: RegisterFormProps) {
  const [role, setRole] = useState<"candidate" | "employer">(
    initialRole === "employer" ? "employer" : "candidate",
  );

  return (
    <div className="flex flex-col gap-6">
      <Title order={4} className="text-center">
        I want to register as:
      </Title>

      <SegmentedControl
        fullWidth
        value={role}
        onChange={(value) => setRole(value as "candidate" | "employer")}
        data={[
          {
            value: "candidate",
            label: (
              <Group gap="xs">
                <FaUser />
                <span>Job Seeker</span>
              </Group>
            ),
          },
          {
            value: "employer",
            label: (
              <Group gap="xs">
                <FaBriefcase />
                <span>Employer</span>
              </Group>
            ),
          },
        ]}
      />

      <div className="mt-4">
        {role === "candidate" ? (
          <CandidateRegisterForm />
        ) : (
          <EmployerRegisterForm />
        )}
      </div>

      <Divider
        label="Already have an account?"
        labelPosition="center"
        my="lg"
      />

      <Button component={Link} to="/auth/login" variant="outline" fullWidth>
        Login
      </Button>
    </div>
  );
}
