import { Request, Response, NextFunction } from "express";
import Application from "../models/application.model";
import Job from "../models/job.model";
import Company from "../models/company.model";
import { AppError } from "../utils/app-error";
import { catchAsync } from "../utils/catch-async";

// Get all applications (for admin)
export const getAllApplications = catchAsync(
  async (req: Request, res: Response, _next: NextFunction) => {
    // Build query
    const queryObj = { ...req.query };
    const excludedFields = ["page", "sort", "limit", "fields"];
    excludedFields.forEach((field) => delete queryObj[field]);

    // Advanced filtering
    let queryStr = JSON.stringify(queryObj);
    queryStr = queryStr.replace(/\b(gte|gt|lte|lt)\b/g, (match) => `$${match}`);

    let query = Application.find(JSON.parse(queryStr))
      .populate({
        path: "job",
        select: "title company",
        populate: {
          path: "company",
          select: "name",
        },
      })
      .populate({
        path: "candidate",
        select: "name email",
      });

    // Sorting
    if (req.query.sort) {
      const sortBy = (req.query.sort as string).split(",").join(" ");
      query = query.sort(sortBy);
    } else {
      query = query.sort("-createdAt");
    }

    // Field limiting
    if (req.query.fields) {
      const fields = (req.query.fields as string).split(",").join(" ");
      query = query.select(fields) as any;
    } else {
      query = query.select("-__v") as any;
    }

    // Pagination
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const skip = (page - 1) * limit;

    query = query.skip(skip).limit(limit);

    // Execute query
    const applications = await query;
    const total = await Application.countDocuments(JSON.parse(queryStr));

    res.status(200).json({
      success: true,
      count: applications.length,
      total,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      applications,
    });
  }
);

// Get application by ID
export const getApplicationById = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    const application = await Application.findById(req.params.id)
      .populate({
        path: "job",
        select: "title company description requirements",
        populate: {
          path: "company",
          select: "name logo",
        },
      })
      .populate({
        path: "candidate",
        select: "name email",
      });

    if (!application) {
      return next(new AppError("No application found with that ID", 404));
    }

    // Check if the user is authorized to view this application
    const isCandidate =
      application.candidate._id.toString() === req.user._id?.toString();

    // Check if the user is the employer
    let isEmployer = false;
    if (application.job) {
      // Get the job to find the company
      const job = await Job.findById(application.job);
      if (job) {
        const company = await Company.findById(job.company);
        if (company && company.owner.toString() === req.user._id?.toString()) {
          isEmployer = true;
        }
      }
    }

    if (!isCandidate && !isEmployer && req.user.role !== "admin") {
      return next(
        new AppError("You are not authorized to view this application", 403)
      );
    }

    res.status(200).json({
      success: true,
      application,
    });
  }
);

// Create new application
export const createApplication = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    // Check if the job exists and is active
    const job = await Job.findOne({ _id: req.body.job, isActive: true });

    if (!job) {
      return next(new AppError("Job not found or is no longer active", 404));
    }

    // Check if application deadline has passed
    if (
      job.applicationDeadline &&
      new Date(job.applicationDeadline) < new Date()
    ) {
      return next(new AppError("Application deadline has passed", 400));
    }

    // Check if the user has already applied for this job
    const existingApplication = await Application.findOne({
      job: req.body.job,
      candidate: req.user._id,
    });

    if (existingApplication) {
      return next(new AppError("You have already applied for this job", 400));
    }

    // Create the application
    const newApplication = await Application.create({
      ...req.body,
      candidate: req.user._id,
    });

    res.status(201).json({
      success: true,
      application: newApplication,
    });
  }
);

// Update application status (for employers and admins)
export const updateApplicationStatus = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    const { status, notes } = req.body;

    // Find the application
    const application = await Application.findById(req.params.id);

    if (!application) {
      return next(new AppError("No application found with that ID", 404));
    }

    // Check if the user is authorized to update this application
    let isAuthorized = false;
    if (req.user.role === "admin") {
      isAuthorized = true;
    } else {
      // Check if the user is the employer
      const job = await Job.findById(application.job);
      if (job) {
        const company = await Company.findById(job.company);
        if (company && company.owner.toString() === req.user._id?.toString()) {
          isAuthorized = true;
        }
      }
    }

    if (!isAuthorized) {
      return next(
        new AppError("You are not authorized to update this application", 403)
      );
    }

    // Update the application
    application.status = status;
    if (notes) {
      application.notes = notes;
    }
    await application.save();

    res.status(200).json({
      success: true,
      application,
    });
  }
);

// Get applications for a specific job
export const getApplicationsByJob = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    const jobId = req.params.jobId;

    // Check if the job exists
    const job = await Job.findById(jobId);
    if (!job) {
      return next(new AppError("No job found with that ID", 404));
    }

    // Check if the user is authorized to view these applications
    const company = await Company.findById(job.company);
    if (!company || company.owner.toString() !== req.user._id?.toString()) {
      return next(
        new AppError("You are not authorized to view these applications", 403)
      );
    }

    // Get applications
    const applications = await Application.find({ job: jobId }).populate({
      path: "candidate",
      select: "name email",
    });

    res.status(200).json({
      success: true,
      count: applications.length,
      applications,
    });
  }
);

// Get applications submitted by the current user
export const getMyApplications = catchAsync(
  async (req: Request, res: Response, _next: NextFunction) => {
    const applications = await Application.find({
      candidate: req.user._id as unknown as string,
    }).populate({
      path: "job",
      select: "title company location jobType",
      populate: {
        path: "company",
        select: "name logo",
      },
    });

    res.status(200).json({
      success: true,
      count: applications.length,
      applications,
    });
  }
);
