/**
 * Utility functions for handling URLs
 */

/**
 * Get the backend base URL
 * @returns The base URL of the backend server
 */
export const getBackendBaseUrl = (): string => {
  // In development, the backend is running on localhost:5000
  if (process.env.NODE_ENV === "development") {
    return "http://localhost:5000";
  }

  // In production, the backend URL would be configured differently
  // This is a placeholder for the production URL
  return window.location.origin;
};

/**
 * Convert a relative URL to an absolute URL
 * @param url The URL to convert
 * @returns The absolute URL
 */
export const getFullUrl = (url: string): string => {
  // If the URL is already absolute, return it as is
  if (url.startsWith("http")) {
    return url;
  }

  // If the URL is a relative path, prepend the backend base URL
  const baseUrl = getBackendBaseUrl();

  // If the URL starts with a slash, remove it to avoid double slashes
  const cleanUrl = url.startsWith("/") ? url : `/${url}`;

  return `${baseUrl}${cleanUrl}`;
};

/**
 * Convert a profile image URL to a full URL for display
 * @param profileImageUrl The profile image URL from the backend
 * @returns The full URL for display, or null if no image
 */
export const getProfileImageUrl = (
  profileImageUrl?: string | null,
): string | null => {
  if (!profileImageUrl) {
    return null;
  }

  return getFullUrl(profileImageUrl);
};

/**
 * Convert a resume URL to a full URL for display
 * @param resumeUrl The resume URL from the backend
 * @returns The full URL for display, or null if no resume
 */
export const getResumeUrl = (resumeUrl?: string | null): string | null => {
  if (!resumeUrl) {
    return null;
  }

  return getFullUrl(resumeUrl);
};
