"use client";

import { cn } from "@/design-system/utils";
import { useThemeClasses } from "@/design-system/utils/theme-utils";
import { type IconType } from "react-icons";
import { Link, useLocation } from "react-router";

export interface SidebarLink {
  id: number;
  name: string;
  href: string;
  icon: IconType;
}

interface SidebarProps {
  title: string;
  links: SidebarLink[];
  onLinkClick?: () => void;
  className?: string;
}

/**
 * Reusable sidebar component for admin, employer, and candidate sections
 */
export default function Sidebar({
  title,
  links,
  onLinkClick,
  className = "",
}: SidebarProps) {
  const { pathname } = useLocation();

  return (
    <div
      className={cn(
        useThemeClasses(
          "h-full min-w-[250px] rounded-lg border bg-white p-4 md:sticky md:top-3",
          "h-full min-w-[250px] rounded-lg border border-dark-4 bg-dark-7 p-4 md:sticky md:top-3",
        ),
        className,
      )}
    >
      {/* Title only shown on desktop since mobile has it in the drawer header */}
      <div className="mb-6 hidden items-center justify-center md:flex">
        <h2 className="text-primary-color text-xl font-bold">{title}</h2>
      </div>

      <nav className="flex flex-col gap-2">
        {links.map((link) => {
          const isActive = pathname === link.href;
          const Icon = link.icon;

          return (
            <Link
              key={link.id}
              to={link.href}
              onClick={onLinkClick}
              className={cn(
                isActive
                  ? "bg-primary-color text-white"
                  : useThemeClasses("hover:bg-gray-100", "hover:bg-dark-5"),
                "flex items-center gap-3 rounded-md px-4 py-3 transition-colors",
              )}
            >
              <Icon size={18} />
              <span>{link.name}</span>
            </Link>
          );
        })}
      </nav>
    </div>
  );
}
