import { candidateData } from "@/data/candidate-data";
import { Card, Text, Title } from "@mantine/core";

export default function CandidateCertifications() {
  return (
    <Card withBorder radius="md" className="mt-6 p-6 shadow-sm">
      <Title order={2} className="mb-4 text-lg font-semibold">
        Certifications
      </Title>
      {candidateData.certifications.map((cert, index) => (
        <div
          key={index}
          className="mb-4 border-b border-gray-200 pb-4 last:border-b-0"
        >
          <Text size="md" className="mb-1" component="div">
            <strong>Name:</strong> {cert.name}
          </Text>
          <Text size="md" className="mb-1" component="div">
            <strong>Issuer:</strong> {cert.issuer}
          </Text>
          <Text size="md" className="mb-1" component="div">
            <strong>Date:</strong> {cert.date}
          </Text>
        </div>
      ))}
    </Card>
  );
}
