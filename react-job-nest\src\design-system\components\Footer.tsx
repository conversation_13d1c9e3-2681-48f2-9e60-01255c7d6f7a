"use client";

import { useMantineColorScheme } from "@mantine/core";
import {
  FaEnvelope,
  FaFacebook,
  FaInstagram,
  FaLinkedin,
  FaMapMarkerAlt,
  FaPhone,
  FaTwitter,
} from "react-icons/fa";
import { Link } from "react-router";
import Logo from "./Logo";

const footerLinks = {
  company: [
    { name: "About Us", href: "/about-us" },
    { name: "Contact Us", href: "/contact-us" },
    { name: "FAQ", href: "/faq" },
    { name: "Resources", href: "/resources" },
  ],
  candidates: [
    { name: "Find Jobs", href: "/candidate/jobs" },
    { name: "Create Profile", href: "/auth/register" },
    { name: "Career Resources", href: "/resources" },
    { name: "Job Alerts", href: "/candidate/job-alerts" },
  ],
  employers: [
    { name: "Post a Job", href: "/employer/create-job" },
    { name: "Find Candidates", href: "/employer/candidates" },
    { name: "Employer Resources", href: "/resources" },
    { name: "Pricing Plans", href: "/employer/pricing" },
  ],
  legal: [
    { name: "Privacy Policy", href: "/privacy-policy" },
    { name: "Terms of Service", href: "/terms-and-conditions" },
    { name: "Cookie Policy", href: "/cookie-policy" },
    { name: "Accessibility", href: "/accessibility" },
  ],
};

export default function Footer() {
  const { colorScheme } = useMantineColorScheme();
  const isDark = colorScheme === "dark";

  return (
    <footer
      className={
        isDark
          ? "bg-gradient-to-b from-gray-900 to-gray-800"
          : "bg-gradient-to-b from-gray-50 to-gray-100"
      }
    >
      {/* Top wave decoration */}
      <div className={isDark ? "text-gray-900" : "text-gray-50"}>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 1440 48"
          fill="currentColor"
          preserveAspectRatio="none"
        >
          <path d="M0,48L80,42.7C160,37,320,27,480,21.3C640,16,800,16,960,21.3C1120,27,1280,37,1360,42.7L1440,48L1440,48L1360,48C1280,48,1120,48,960,48C800,48,640,48,480,48C320,48,160,48,80,48L0,48Z"></path>
        </svg>
      </div>

      <div className="container">
        <div className="py-12 sm:py-16">
          {/* Main Footer Content */}
          <div className="grid grid-cols-1 gap-y-10 sm:gap-x-6 md:grid-cols-2 lg:grid-cols-6 xl:grid-cols-12">
            {/* Company Info */}
            <div className="md:col-span-2 lg:col-span-3 xl:col-span-6">
              <div className="mb-6">
                <Logo />
              </div>
              <p
                className={`mb-6 ${isDark ? "text-gray-300" : "text-gray-600"}`}
              >
                JobNest is your ultimate platform for connecting talented
                professionals with their dream careers and helping employers
                find the perfect candidates.
              </p>
              <div className="space-y-4">
                <div
                  className={`flex items-center gap-3 ${isDark ? "text-gray-300" : "text-gray-600"}`}
                >
                  <div
                    className={`${isDark ? "bg-primary-color/20" : "bg-primary-color/10"} text-primary-color flex h-8 w-8 shrink-0 items-center justify-center rounded-full`}
                  >
                    <FaMapMarkerAlt size={14} />
                  </div>
                  <span className="text-sm sm:text-base">
                    123 Job Street, Career City, 10001
                  </span>
                </div>
                <div
                  className={`flex items-center gap-3 ${isDark ? "text-gray-300" : "text-gray-600"}`}
                >
                  <div
                    className={`${isDark ? "bg-primary-color/20" : "bg-primary-color/10"} text-primary-color flex h-8 w-8 shrink-0 items-center justify-center rounded-full`}
                  >
                    <FaPhone size={14} />
                  </div>
                  <span className="text-sm sm:text-base">
                    +****************
                  </span>
                </div>
                <div
                  className={`flex items-center gap-3 ${isDark ? "text-gray-300" : "text-gray-600"}`}
                >
                  <div
                    className={`${isDark ? "bg-primary-color/20" : "bg-primary-color/10"} text-primary-color flex h-8 w-8 shrink-0 items-center justify-center rounded-full`}
                  >
                    <FaEnvelope size={14} />
                  </div>
                  <span className="text-sm sm:text-base">
                    <EMAIL>
                  </span>
                </div>
              </div>
            </div>

            {/* Quick Links - Responsive Grid */}
            <div className="sm:col-span-1 md:col-span-1 lg:col-span-1 xl:col-span-2">
              <h3
                className={`after:bg-primary-color/30 mb-5 text-lg font-semibold ${isDark ? "text-white" : "text-gray-900"} after:mt-2 after:block after:h-1 after:w-10 after:rounded-full`}
              >
                Company
              </h3>
              <ul className="space-y-3">
                {footerLinks.company.map((link, index) => (
                  <li key={index}>
                    <Link
                      to={link.href}
                      className={`group hover:text-primary-color flex items-center gap-2 ${isDark ? "text-gray-300" : "text-gray-600"} transition-colors`}
                    >
                      <span className="bg-primary-color h-1 w-0 rounded-full transition-all duration-300 group-hover:w-3"></span>
                      <span className="text-sm sm:text-base">{link.name}</span>
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            <div className="sm:col-span-1 md:col-span-1 lg:col-span-1 xl:col-span-2">
              <h3
                className={`after:bg-primary-color/30 mb-5 text-lg font-semibold ${isDark ? "text-white" : "text-gray-900"} after:mt-2 after:block after:h-1 after:w-10 after:rounded-full`}
              >
                Job Seekers
              </h3>
              <ul className="space-y-3">
                {footerLinks.candidates.map((link, index) => (
                  <li key={index}>
                    <Link
                      to={link.href}
                      className={`group hover:text-primary-color flex items-center gap-2 ${isDark ? "text-gray-300" : "text-gray-600"} transition-colors`}
                    >
                      <span className="bg-primary-color h-1 w-0 rounded-full transition-all duration-300 group-hover:w-3"></span>
                      <span className="text-sm sm:text-base">{link.name}</span>
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            <div className="sm:col-span-1 md:col-span-1 lg:col-span-1 xl:col-span-2">
              <h3
                className={`after:bg-primary-color/30 mb-5 text-lg font-semibold ${isDark ? "text-white" : "text-gray-900"} after:mt-2 after:block after:h-1 after:w-10 after:rounded-full`}
              >
                Employers
              </h3>
              <ul className="space-y-3">
                {footerLinks.employers.map((link, index) => (
                  <li key={index}>
                    <Link
                      to={link.href}
                      className={`group hover:text-primary-color flex items-center gap-2 ${isDark ? "text-gray-300" : "text-gray-600"} transition-colors`}
                    >
                      <span className="bg-primary-color h-1 w-0 rounded-full transition-all duration-300 group-hover:w-3"></span>
                      <span className="text-sm sm:text-base">{link.name}</span>
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          </div>

          {/* Social Media & Secondary Links */}
          <div
            className={`mt-16 border-t ${isDark ? "border-gray-700" : "border-gray-200"} pt-10`}
          >
            <div className="flex flex-col items-center justify-between gap-8 md:flex-row">
              {/* Social Media Icons */}
              <div className="flex gap-4">
                <Link
                  to="https://facebook.com"
                  target="_blank"
                  rel="noopener noreferrer"
                  className={`hover:bg-primary-color flex h-9 w-9 items-center justify-center rounded-full ${isDark ? "bg-gray-800 text-gray-300" : "bg-white text-gray-600"} shadow-sm transition-all hover:text-white hover:shadow-md sm:h-10 sm:w-10`}
                  aria-label="Facebook"
                >
                  <FaFacebook size={16} className="sm:text-lg" />
                </Link>
                <Link
                  to="https://twitter.com"
                  target="_blank"
                  rel="noopener noreferrer"
                  className={`hover:bg-primary-color flex h-9 w-9 items-center justify-center rounded-full ${isDark ? "bg-gray-800 text-gray-300" : "bg-white text-gray-600"} shadow-sm transition-all hover:text-white hover:shadow-md sm:h-10 sm:w-10`}
                  aria-label="Twitter"
                >
                  <FaTwitter size={16} className="sm:text-lg" />
                </Link>
                <Link
                  to="https://linkedin.com"
                  target="_blank"
                  rel="noopener noreferrer"
                  className={`hover:bg-primary-color flex h-9 w-9 items-center justify-center rounded-full ${isDark ? "bg-gray-800 text-gray-300" : "bg-white text-gray-600"} shadow-sm transition-all hover:text-white hover:shadow-md sm:h-10 sm:w-10`}
                  aria-label="LinkedIn"
                >
                  <FaLinkedin size={16} className="sm:text-lg" />
                </Link>
                <Link
                  to="https://instagram.com"
                  target="_blank"
                  rel="noopener noreferrer"
                  className={`hover:bg-primary-color flex h-9 w-9 items-center justify-center rounded-full ${isDark ? "bg-gray-800 text-gray-300" : "bg-white text-gray-600"} shadow-sm transition-all hover:text-white hover:shadow-md sm:h-10 sm:w-10`}
                  aria-label="Instagram"
                >
                  <FaInstagram size={16} className="sm:text-lg" />
                </Link>
              </div>

              {/* Legal Links */}
              <div className="flex flex-wrap justify-center gap-3 text-xs sm:gap-6 sm:text-sm">
                {footerLinks.legal.map((link, index) => (
                  <Link
                    key={index}
                    to={link.href}
                    className={`hover:text-primary-color ${isDark ? "text-gray-400" : "text-gray-600"} transition-colors`}
                  >
                    {link.name}
                  </Link>
                ))}
              </div>
            </div>

            {/* Copyright */}
            <div className="mt-10 flex flex-col items-center justify-center gap-2 text-center">
              <div className="flex flex-wrap items-center justify-center gap-2">
                <span className="text-primary-color font-semibold">
                  JobNest
                </span>
                <span
                  className={`hidden h-1 w-1 rounded-full ${isDark ? "bg-gray-600" : "bg-gray-300"} sm:block`}
                ></span>
                <span
                  className={`text-xs ${isDark ? "text-gray-400" : "text-gray-500"} sm:text-sm`}
                >
                  Connecting Talent with Opportunity
                </span>
              </div>
              <p
                className={`text-xs ${isDark ? "text-gray-400" : "text-gray-500"} sm:text-sm`}
              >
                &copy; {new Date().getFullYear()} JobNest. All rights reserved.
              </p>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
