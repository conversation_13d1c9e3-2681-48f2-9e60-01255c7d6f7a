import mongoose, { Document, Schema } from 'mongoose';

export interface IEmailSettings {
  smtpHost: string;
  smtpPort: number;
  smtpUsername: string;
  smtpPassword: string;
  fromEmail: string;
  fromName: string;
}

export interface IJobSettings {
  defaultJobExpiryDays: number;
  maxJobsPerEmployer: number;
  requireJobApproval: boolean;
  allowJobEditing: boolean;
  jobCategories: string[];
  jobTypes: string[];
  jobLocations: string[];
}

export interface INotificationSettings {
  enableEmailNotifications: boolean;
  notifyAdminOnNewUser: boolean;
  notifyAdminOnNewJob: boolean;
  notifyAdminOnNewCompany: boolean;
  notifyEmployerOnNewApplication: boolean;
  notifyCandidateOnApplicationStatusChange: boolean;
}

export interface IGeneralSettings {
  siteName: string;
  siteDescription: string;
  contactEmail: string;
  supportEmail: string;
  phoneNumber: string;
  address: string;
  logoUrl: string;
  faviconUrl: string;
  socialLinks: {
    facebook?: string;
    twitter?: string;
    linkedin?: string;
    instagram?: string;
  };
  maintenanceMode: boolean;
}

export interface ISettings extends Document {
  general: IGeneralSettings;
  email: IEmailSettings;
  job: IJobSettings;
  notification: INotificationSettings;
  updatedBy: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

const emailSettingsSchema = new Schema<IEmailSettings>({
  smtpHost: {
    type: String,
    required: [true, 'SMTP host is required'],
    default: 'smtp.mailtrap.io',
  },
  smtpPort: {
    type: Number,
    required: [true, 'SMTP port is required'],
    default: 2525,
  },
  smtpUsername: {
    type: String,
    required: [true, 'SMTP username is required'],
    default: '',
  },
  smtpPassword: {
    type: String,
    required: [true, 'SMTP password is required'],
    default: '',
  },
  fromEmail: {
    type: String,
    required: [true, 'From email is required'],
    default: '<EMAIL>',
  },
  fromName: {
    type: String,
    required: [true, 'From name is required'],
    default: 'JobNest',
  },
});

const jobSettingsSchema = new Schema<IJobSettings>({
  defaultJobExpiryDays: {
    type: Number,
    required: [true, 'Default job expiry days is required'],
    default: 30,
  },
  maxJobsPerEmployer: {
    type: Number,
    required: [true, 'Max jobs per employer is required'],
    default: 10,
  },
  requireJobApproval: {
    type: Boolean,
    default: true,
  },
  allowJobEditing: {
    type: Boolean,
    default: true,
  },
  jobCategories: {
    type: [String],
    default: [
      'Technology',
      'Healthcare',
      'Education',
      'Finance',
      'Marketing',
      'Sales',
      'Customer Service',
      'Administrative',
      'Engineering',
      'Other',
    ],
  },
  jobTypes: {
    type: [String],
    default: [
      'Full-time',
      'Part-time',
      'Contract',
      'Temporary',
      'Internship',
      'Remote',
    ],
  },
  jobLocations: {
    type: [String],
    default: ['Remote', 'Onsite', 'Hybrid'],
  },
});

const notificationSettingsSchema = new Schema<INotificationSettings>({
  enableEmailNotifications: {
    type: Boolean,
    default: true,
  },
  notifyAdminOnNewUser: {
    type: Boolean,
    default: true,
  },
  notifyAdminOnNewJob: {
    type: Boolean,
    default: true,
  },
  notifyAdminOnNewCompany: {
    type: Boolean,
    default: true,
  },
  notifyEmployerOnNewApplication: {
    type: Boolean,
    default: true,
  },
  notifyCandidateOnApplicationStatusChange: {
    type: Boolean,
    default: true,
  },
});

const generalSettingsSchema = new Schema<IGeneralSettings>({
  siteName: {
    type: String,
    required: [true, 'Site name is required'],
    default: 'JobNest',
  },
  siteDescription: {
    type: String,
    required: [true, 'Site description is required'],
    default: 'Find your dream job or perfect candidate',
  },
  contactEmail: {
    type: String,
    required: [true, 'Contact email is required'],
    default: '<EMAIL>',
  },
  supportEmail: {
    type: String,
    required: [true, 'Support email is required'],
    default: '<EMAIL>',
  },
  phoneNumber: {
    type: String,
    default: '+****************',
  },
  address: {
    type: String,
    default: '123 Job Street, Employment City, 12345',
  },
  logoUrl: {
    type: String,
    default: '/logo.png',
  },
  faviconUrl: {
    type: String,
    default: '/favicon.ico',
  },
  socialLinks: {
    facebook: String,
    twitter: String,
    linkedin: String,
    instagram: String,
  },
  maintenanceMode: {
    type: Boolean,
    default: false,
  },
});

const settingsSchema = new Schema<ISettings>(
  {
    general: {
      type: generalSettingsSchema,
      required: true,
      default: {},
    },
    email: {
      type: emailSettingsSchema,
      required: true,
      default: {},
    },
    job: {
      type: jobSettingsSchema,
      required: true,
      default: {},
    },
    notification: {
      type: notificationSettingsSchema,
      required: true,
      default: {},
    },
    updatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
  },
  {
    timestamps: true, // Automatically add createdAt and updatedAt fields
  }
);

const Settings = mongoose.model<ISettings>('Settings', settingsSchema);

export default Settings;
