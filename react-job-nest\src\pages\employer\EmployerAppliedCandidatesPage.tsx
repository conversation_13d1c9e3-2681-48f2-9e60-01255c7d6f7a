"use client";

import CandidatesList from "@/components/employer/candidates/CandidatesList";
import CandidatesSearchFilter from "@/components/employer/candidates/CandidatesSearchFilter";
import { PageContainer, PageHeading } from "@/design-system/components";
import useCandidates from "@/hooks/employer/use-candidates";
import {
  Box,
  Button,
  Card,
  Divider,
  Group,
  Pagination,
  SegmentedControl,
  Text,
  useMantineColorScheme,
} from "@mantine/core";
import { useMediaQuery } from "@mantine/hooks";
import { notifications } from "@mantine/notifications";
import { useEffect, useState } from "react";
import { FaThLarge, FaThList } from "react-icons/fa";

export default function EmployerAppliedCandidatesPage() {
  // State for view mode (grid or list)
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");

  // Media query for responsive design
  const isMobile = useMediaQuery("(max-width: 768px)");

  // Theme helpers
  const { colorScheme } = useMantineColorScheme();
  const isDark = colorScheme === "dark";

  // Use the candidates hook
  const {
    candidates,
    isLoading,
    error,
    totalCandidates,
    page,
    setPage,
    jobId,
    setJobId,
    status,
    setStatus,
    searchQuery,
    setSearchQuery,
    dateRange,
    setDateRange,
    sortBy,
    setSortBy,
    refreshCandidates,
    updateCandidateStatus,
    jobs,
  } = useCandidates();

  // Function to clear all filters
  const clearAllFilters = () => {
    setJobId("");
    setStatus("");
    setDateRange([null, null]);
    setSortBy("newest");
    setSearchQuery("");
  };

  // Function to apply filters
  const applyFilters = () => {
    // Reset to page 1 when filters change
    setPage(1);
    refreshCandidates();
  };

  // Function to handle status update
  const handleUpdateStatus = async (candidateId: string, newStatus: string) => {
    const success = await updateCandidateStatus(candidateId, newStatus);

    if (success) {
      notifications.show({
        title: "Status Updated",
        message: `Candidate status has been updated to ${newStatus}`,
        color: "green",
      });
    }

    return success;
  };

  // Show error notification if there's an error
  useEffect(() => {
    if (error) {
      notifications.show({
        title: "Error",
        message: error,
        color: "red",
      });
    }
  }, [error]);

  return (
    <PageContainer
      breadcrumbItems={[{ title: "Home", href: "/" }, { title: "Candidates" }]}
      variant="employer"
      className="pb-12"
    >
      {/* Hero Section */}
      <Box
        className="mb-8 rounded-lg px-4 py-8 md:px-6 md:py-10"
        mx={{ base: -4, sm: -6 }}
        style={{
          background: isDark
            ? "linear-gradient(to right, #1a1b2e, #141b2d, #1a1b2e)"
            : "linear-gradient(to right, #f0f4ff, #e6f0ff, #f0f4ff)",
        }}
      >
        <PageHeading
          title="Candidates"
          subtitle="View and manage applicants for your job postings"
          className="mb-0 text-center"
          variant="employer"
        />
      </Box>

      {/* Filters and Search Bar */}
      <Card withBorder radius="md" className="mb-6 shadow-sm" p="md">
        <CandidatesSearchFilter
          jobs={jobs}
          selectedJob={jobId}
          setSelectedJob={setJobId}
          selectedStatus={status}
          setSelectedStatus={setStatus}
          dateRange={dateRange}
          setDateRange={setDateRange}
          sortBy={sortBy}
          setSortBy={setSortBy}
          searchQuery={searchQuery}
          setSearchQuery={setSearchQuery}
          onApplyFilters={applyFilters}
        />

        {(jobId || status || searchQuery || dateRange[0] || dateRange[1]) && (
          <>
            <Divider my="sm" />
            <Group justify="space-between" align="center">
              <Group gap="xs">
                {jobId && (
                  <Button
                    variant="light"
                    size="xs"
                    radius="xl"
                    rightSection="×"
                    onClick={() => setJobId("")}
                  >
                    Job: {jobs.find((j) => j._id === jobId)?.title || jobId}
                  </Button>
                )}
                {status && (
                  <Button
                    variant="light"
                    size="xs"
                    radius="xl"
                    rightSection="×"
                    onClick={() => setStatus("")}
                  >
                    Status: {status}
                  </Button>
                )}
                {searchQuery && (
                  <Button
                    variant="light"
                    size="xs"
                    radius="xl"
                    rightSection="×"
                    onClick={() => setSearchQuery("")}
                  >
                    Search: {searchQuery}
                  </Button>
                )}
                {(dateRange[0] || dateRange[1]) && (
                  <Button
                    variant="light"
                    size="xs"
                    radius="xl"
                    rightSection="×"
                    onClick={() => setDateRange([null, null])}
                  >
                    Date Filter
                  </Button>
                )}
              </Group>
              <Button variant="subtle" size="xs" onClick={clearAllFilters}>
                Clear All
              </Button>
            </Group>
          </>
        )}
      </Card>

      {/* Results Header */}
      <Card withBorder radius="md" className="mb-6 shadow-sm" p="md">
        <Group justify="space-between" align="center">
          <Text size="sm" c="dimmed">
            {totalCandidates > 0
              ? `Showing ${Math.min((page - 1) * 10 + 1, totalCandidates)}-${Math.min(page * 10, totalCandidates)} of ${totalCandidates} candidates`
              : "No candidates found"}
          </Text>
          <SegmentedControl
            value={viewMode}
            onChange={(value) => setViewMode(value as "grid" | "list")}
            data={[
              {
                value: "grid",
                label: (
                  <div className="flex items-center gap-2">
                    <FaThLarge size={16} />
                    <Text size="sm" className="hidden sm:inline">
                      Grid
                    </Text>
                  </div>
                ),
              },
              {
                value: "list",
                label: (
                  <div className="flex items-center gap-2">
                    <FaThList size={16} />
                    <Text size="sm" className="hidden sm:inline">
                      List
                    </Text>
                  </div>
                ),
              },
            ]}
          />
        </Group>
      </Card>

      {/* Candidates Grid/List */}
      <CandidatesList
        viewMode={viewMode}
        candidates={candidates}
        isLoading={isLoading}
        onResetFilters={clearAllFilters}
        onUpdateStatus={handleUpdateStatus}
      />

      {/* Pagination */}
      {totalCandidates > 0 && (
        <Group justify="center" mt="xl">
          <Pagination
            total={Math.ceil(totalCandidates / 10)}
            value={page}
            onChange={setPage}
            size={isMobile ? "sm" : "md"}
            radius="md"
          />
        </Group>
      )}
    </PageContainer>
  );
}
