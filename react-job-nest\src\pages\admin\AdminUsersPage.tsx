"use client";

import AdminNavbar from "@/components/admin/AdminNavbar";
import UserFilters from "@/components/admin/users/UserFilters";
import UsersTable from "@/components/admin/users/UsersTable";
import PageContainer from "@/design-system/components/layout/PageContainer";
import PageHeading from "@/design-system/components/typography/PageHeading";
import { userFilterSchema } from "@/schemas/admin/user-management-schema";
import { useUserActionsStore } from "@/stores/admin-store";
import {
  useDeleteUserModal,
  useUserDetailsModal,
  useUserEditModal,
} from "@/stores/modal-store";
import {
  Badge,
  Button,
  Card,
  Group,
  Modal,
  Pagination,
  Tabs,
  Text,
  TextInput,
} from "@mantine/core";
import { useForm } from "@mantine/form";
import { notifications } from "@mantine/notifications";
import { zodResolver } from "mantine-form-zod-resolver";
import { useCallback, useEffect, useState } from "react";
import { FaFilter, FaPlus, FaSearch } from "react-icons/fa";

export default function AdminUsersPage() {
  const [selectedUserId, setSelectedUserId] = useState<number | null>(null);
  const [showFilters, setShowFilters] = useState(false);

  const filterForm = useForm({
    initialValues: {
      search: "",
      role: "all",
      status: "all",
      sortBy: "name",
      sortOrder: "asc",
    },
    validate: zodResolver(userFilterSchema),
  });

  const handleFilterSubmit = () => {
    console.log("Filter values:", filterForm.values);
    // In a real application, you would fetch filtered users here
  };

  const handleFilterReset = () => {
    filterForm.reset();
  };

  const userDetailsModal = useUserDetailsModal();
  const userEditModal = useUserEditModal();
  const deleteUserModal = useDeleteUserModal();

  const handleViewUser = (userId: number) => {
    setSelectedUserId(userId);
    userDetailsModal.open();
  };

  const handleEditUser = (userId: number) => {
    setSelectedUserId(userId);
    userEditModal.open();
  };

  const handleDeleteUser = (userId: number) => {
    setSelectedUserId(userId);
    deleteUserModal.open();
  };

  // This function will be used by the DeleteUserModal
  const confirmDeleteUser = useCallback(() => {
    // In a real application, you would delete the user here
    console.log("Deleting user:", selectedUserId);

    notifications.show({
      title: "User Deleted",
      message: "The user has been successfully deleted",
      color: "green",
    });
  }, [selectedUserId]);

  // Update the user actions store when the component mounts
  const setDeleteUserCallback = useUserActionsStore(
    (state) => state.setDeleteUserCallback,
  );

  useEffect(() => {
    // Set the delete callback in the store
    setDeleteUserCallback(confirmDeleteUser);

    return () => {
      // Clear the callback when the component unmounts
      setDeleteUserCallback(null);
    };
  }, [confirmDeleteUser, setDeleteUserCallback]);

  return (
    <>
      <AdminNavbar />
      <PageContainer variant="admin">
        {/* Page Title */}
        <PageHeading
          title="User Management"
          subtitle="View and manage all users on the platform"
          variant="admin"
        />

        <div className="mt-6">
          {/* Filter Modal */}
          <Modal
            opened={showFilters}
            onClose={() => setShowFilters(false)}
            title="Filter Users"
            size="lg"
            padding="md"
            classNames={{
              header: "mb-2",
              title: "font-semibold text-lg",
              body: "pt-0",
            }}
            centered
            overlayProps={{
              backgroundOpacity: 0.55,
              blur: 3,
            }}
          >
            <UserFilters
              form={filterForm}
              onSubmit={() => {
                handleFilterSubmit();
                setShowFilters(false);
              }}
              onReset={() => {
                handleFilterReset();
                setShowFilters(false);
              }}
            />
          </Modal>

          {/* Content Section */}
          <div>
            <Card shadow="sm" padding="md" radius="md" withBorder>
              {/* Table Header with Actions */}
              <div className="mb-4 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                <div className="flex flex-1 flex-col gap-4 sm:flex-row sm:items-center">
                  <TextInput
                    placeholder="Search users..."
                    leftSection={<FaSearch size={14} />}
                    className="flex-1"
                    value={filterForm.values.search}
                    onChange={(e) => {
                      filterForm.setFieldValue("search", e.target.value);
                      // Auto-submit after a short delay
                      if (
                        e.target.value.length > 2 ||
                        e.target.value.length === 0
                      ) {
                        setTimeout(() => handleFilterSubmit(), 500);
                      }
                    }}
                  />
                </div>

                <Group gap="sm">
                  <Button
                    leftSection={<FaFilter size={14} />}
                    variant="light"
                    onClick={() => setShowFilters(!showFilters)}
                  >
                    Filters
                  </Button>
                  <Button
                    leftSection={<FaPlus size={14} />}
                    className="bg-primary-color hover:bg-primary-color/90"
                  >
                    Add User
                  </Button>
                </Group>
              </div>

              {/* User Type Tabs */}
              <Tabs defaultValue="all" mb="md">
                <Tabs.List>
                  <Tabs.Tab value="all">
                    All Users
                    <Badge
                      size="xs"
                      variant="filled"
                      ml={6}
                      className="bg-gray-500"
                    >
                      250
                    </Badge>
                  </Tabs.Tab>
                  <Tabs.Tab value="candidates">
                    Candidates
                    <Badge size="xs" variant="filled" color="blue" ml={6}>
                      180
                    </Badge>
                  </Tabs.Tab>
                  <Tabs.Tab value="employers">
                    Employers
                    <Badge size="xs" variant="filled" color="green" ml={6}>
                      45
                    </Badge>
                  </Tabs.Tab>
                  <Tabs.Tab value="admins">
                    Admins
                    <Badge size="xs" variant="filled" color="purple" ml={6}>
                      25
                    </Badge>
                  </Tabs.Tab>
                </Tabs.List>
              </Tabs>

              {/* Users Table */}
              <UsersTable
                onView={handleViewUser}
                onEdit={handleEditUser}
                onDelete={handleDeleteUser}
              />

              {/* Pagination */}
              <Group justify="space-between" mt="xl">
                <Text size="sm" c="dimmed">
                  Showing 1-5 of 5 users
                </Text>
                <Pagination total={1} />
              </Group>
            </Card>
          </div>
        </div>
      </PageContainer>
    </>
  );
}
