import express from "express";
import {
  getAllCompanies,
  getCompanyById,
  createCompany,
  updateCompany,
  deleteCompany,
  getMyCompanies,
  verifyCompany,
  uploadCompanyLogo,
  removeCompanyLogo,
  getCompanyStats,
  getCompanyVerificationStatus,
  requestCompanyVerification,
} from "../controllers/company.controller";
import { protect, restrictTo } from "../middleware/auth.middleware";
import {
  createCompanyValidator,
  updateCompanyValidator,
} from "../validators/company.validator";
import { validationMiddleware } from "../validators/validation.middleware";
import { uploadTypes } from "../services/file-upload.service";

const router = express.Router();

// Public routes
router.get("/", getAllCompanies);
router.get("/:id", getCompanyById);

// Protected routes
router.use(protect);

// User routes
router.get("/my/companies", getMyCompanies);

// Employer and admin routes
router.use(restrictTo("employer", "admin"));
router.post("/", createCompanyValidator, validationMiddleware, createCompany);
router.patch(
  "/:id",
  updateCompanyValidator,
  validationMiddleware,
  updateCompany
);
router.delete("/:id", deleteCompany);
router.post(
  "/:id/upload-logo",
  uploadTypes.companyLogo.single("logo"),
  uploadCompanyLogo
);
router.delete("/:id/remove-logo", removeCompanyLogo);

// Company stats and verification routes
router.get("/:id/stats", getCompanyStats);
router.get("/:id/verification-status", getCompanyVerificationStatus);
router.post("/:id/request-verification", requestCompanyVerification);

// Admin only routes
router.use(restrictTo("admin"));
router.patch("/:id/verify", verifyCompany);

export default router;
