"use client";

import AdminNavbar from "@/components/admin/AdminNavbar";
import CompaniesTable from "@/components/admin/companies/CompaniesTable";
import CompanyBlockModal from "@/components/admin/companies/CompanyBlockModal";
import CompanyDetailsModal from "@/components/admin/companies/CompanyDetailsModal";
import CompanyFilters from "@/components/admin/companies/CompanyFilters";
import PageContainer from "@/design-system/components/layout/PageContainer";
import PageHeading from "@/design-system/components/typography/PageHeading";
import { companyFilterSchema } from "@/schemas/admin/company-management-schema";
import {
  Badge,
  Button,
  Card,
  Divider,
  Group,
  Modal,
  Pagination,
  Paper,
  SegmentedControl,
  Tabs,
  Text,
  TextInput,
} from "@mantine/core";
import { useForm } from "@mantine/form";
import { notifications } from "@mantine/notifications";
import { zodResolver } from "mantine-form-zod-resolver";
import { useState } from "react";
import {
  FaBuilding,
  FaChartBar,
  <PERSON>a<PERSON><PERSON><PERSON>,
  Fa<PERSON>ilter,
  FaPlus,
  FaSearch,
  FaTrash,
} from "react-icons/fa";

export default function AdminCompaniesPage() {
  const [selectedCompanyId, setSelectedCompanyId] = useState<number | null>(
    null,
  );
  const [detailsModalOpened, setDetailsModalOpened] = useState(false);
  const [blockModalOpened, setBlockModalOpened] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [selectedCompanies, setSelectedCompanies] = useState<number[]>([]);

  const filterForm = useForm({
    initialValues: {
      search: "",
      status: "all",
      industry: "",
      size: "",
      sortBy: "name",
      sortOrder: "asc",
    },
    validate: zodResolver(companyFilterSchema),
  });

  const handleFilterSubmit = () => {
    console.log("Filter values:", filterForm.values);
    // In a real application, you would fetch filtered companies here
    notifications.show({
      title: "Filters Applied",
      message: "The company list has been filtered",
      color: "blue",
    });
  };

  const handleFilterReset = () => {
    filterForm.reset();
    notifications.show({
      title: "Filters Reset",
      message: "All filters have been cleared",
      color: "gray",
    });
  };

  const handleViewCompany = (companyId: number) => {
    setSelectedCompanyId(companyId);
    setDetailsModalOpened(true);
  };

  const handleApproveCompany = (companyId: number) => {
    // In a real application, you would approve the company here
    console.log("Approving company:", companyId);

    notifications.show({
      title: "Company Approved",
      message: "The company has been successfully approved",
      color: "green",
    });
  };

  const handleBlockCompanyClick = (companyId: number) => {
    setSelectedCompanyId(companyId);
    setBlockModalOpened(true);
  };

  const confirmBlockCompany = (reason: string) => {
    // In a real application, you would block the company with the reason here
    console.log("Blocking company:", selectedCompanyId, "Reason:", reason);

    notifications.show({
      title: "Company Blocked",
      message: "The company has been blocked",
      color: "red",
    });
  };

  const handleBulkAction = (action: string) => {
    if (selectedCompanies.length === 0) {
      notifications.show({
        title: "No Companies Selected",
        message: "Please select at least one company to perform this action",
        color: "yellow",
      });
      return;
    }

    notifications.show({
      title: "Bulk Action",
      message: `${action} action applied to ${selectedCompanies.length} companies`,
      color: "blue",
    });
  };

  return (
    <>
      <AdminNavbar />
      <PageContainer variant="admin">
        {/* Page Header with Stats */}
        <div className="mb-6">
          <PageHeading
            title="Company Management"
            subtitle="View and manage all companies on the platform"
            variant="admin"
          />

          <div className="mt-6 grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
            <Paper withBorder p="md" radius="md">
              <Group justify="space-between">
                <div>
                  <Text c="dimmed" size="xs" tt="uppercase" fw={700}>
                    Total Companies
                  </Text>
                  <Text fw={700} size="xl">
                    45
                  </Text>
                </div>
                <FaBuilding size={24} className="text-blue-500" />
              </Group>
              <Text c="dimmed" size="xs" mt="md">
                <Text component="span" c="green" size="xs">
                  +12%
                </Text>{" "}
                from last month
              </Text>
            </Paper>

            <Paper withBorder p="md" radius="md">
              <Group justify="space-between">
                <div>
                  <Text c="dimmed" size="xs" tt="uppercase" fw={700}>
                    Verified Companies
                  </Text>
                  <Text fw={700} size="xl">
                    30
                  </Text>
                </div>
                <FaCheck size={24} className="text-green-500" />
              </Group>
              <Text c="dimmed" size="xs" mt="md">
                <Text component="span" c="green" size="xs">
                  +8%
                </Text>{" "}
                from last month
              </Text>
            </Paper>

            <Paper withBorder p="md" radius="md">
              <Group justify="space-between">
                <div>
                  <Text c="dimmed" size="xs" tt="uppercase" fw={700}>
                    Pending Approval
                  </Text>
                  <Text fw={700} size="xl">
                    10
                  </Text>
                </div>
                <FaFilter size={24} className="text-yellow-500" />
              </Group>
              <Text c="dimmed" size="xs" mt="md">
                <Text component="span" c="red" size="xs">
                  +5
                </Text>{" "}
                new this week
              </Text>
            </Paper>

            <Paper withBorder p="md" radius="md">
              <Group justify="space-between">
                <div>
                  <Text c="dimmed" size="xs" tt="uppercase" fw={700}>
                    Total Jobs Posted
                  </Text>
                  <Text fw={700} size="xl">
                    120
                  </Text>
                </div>
                <FaChartBar size={24} className="text-purple-500" />
              </Group>
              <Text c="dimmed" size="xs" mt="md">
                <Text component="span" c="green" size="xs">
                  +18%
                </Text>{" "}
                from last month
              </Text>
            </Paper>
          </div>
        </div>

        <div className="mt-6">
          {/* Filter Modal */}
          <Modal
            opened={showFilters}
            onClose={() => setShowFilters(false)}
            title="Filter Companies"
            size="lg"
            padding="md"
            classNames={{
              header: "mb-2",
              title: "font-semibold text-lg",
              body: "pt-0",
            }}
            centered
            overlayProps={{
              backgroundOpacity: 0.55,
              blur: 3,
            }}
          >
            <CompanyFilters
              form={filterForm}
              onSubmit={() => {
                handleFilterSubmit();
                setShowFilters(false);
              }}
              onReset={() => {
                handleFilterReset();
                setShowFilters(false);
              }}
            />
          </Modal>

          {/* Content Section */}
          <div>
            <Card shadow="sm" padding="md" radius="md" withBorder>
              {/* Table Header with Actions */}
              <div className="mb-4 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                <div className="flex flex-1 flex-col gap-4 sm:flex-row sm:items-center">
                  <TextInput
                    placeholder="Search companies..."
                    leftSection={<FaSearch size={14} />}
                    className="flex-1"
                    value={filterForm.values.search}
                    onChange={(e) => {
                      filterForm.setFieldValue("search", e.target.value);
                      // Auto-submit after a short delay
                      if (
                        e.target.value.length > 2 ||
                        e.target.value.length === 0
                      ) {
                        setTimeout(() => handleFilterSubmit(), 500);
                      }
                    }}
                  />
                </div>

                <Group gap="sm">
                  <Button
                    leftSection={<FaFilter size={14} />}
                    variant="light"
                    onClick={() => setShowFilters(!showFilters)}
                  >
                    Filters
                  </Button>
                  <Button
                    leftSection={<FaPlus size={14} />}
                    className="bg-primary-color hover:bg-primary-color/90"
                  >
                    Add Company
                  </Button>
                </Group>
              </div>

              {/* Bulk Actions (visible when companies are selected) */}
              {selectedCompanies.length > 0 && (
                <div className="mb-4 flex items-center justify-between rounded-md bg-blue-50 p-2">
                  <Text size="sm">
                    <strong>{selectedCompanies.length}</strong> companies
                    selected
                  </Text>
                  <Group>
                    <Button
                      size="xs"
                      variant="light"
                      color="green"
                      onClick={() => handleBulkAction("Approve")}
                    >
                      Approve All
                    </Button>
                    <Button
                      size="xs"
                      variant="light"
                      color="red"
                      onClick={() => handleBulkAction("Block")}
                    >
                      Block All
                    </Button>
                    <Button
                      size="xs"
                      variant="subtle"
                      color="gray"
                      leftSection={<FaTrash size={12} />}
                      onClick={() => setSelectedCompanies([])}
                    >
                      Clear Selection
                    </Button>
                  </Group>
                </div>
              )}

              {/* Company Status Tabs */}
              <Tabs defaultValue="all" mb="md">
                <Tabs.List>
                  <Tabs.Tab value="all">
                    All Companies
                    <Badge
                      size="xs"
                      variant="filled"
                      ml={6}
                      className="bg-gray-500"
                    >
                      45
                    </Badge>
                  </Tabs.Tab>
                  <Tabs.Tab value="verified">
                    Verified
                    <Badge size="xs" variant="filled" color="green" ml={6}>
                      30
                    </Badge>
                  </Tabs.Tab>
                  <Tabs.Tab value="pending">
                    Pending
                    <Badge size="xs" variant="filled" color="yellow" ml={6}>
                      10
                    </Badge>
                  </Tabs.Tab>
                  <Tabs.Tab value="blocked">
                    Blocked
                    <Badge size="xs" variant="filled" color="red" ml={6}>
                      5
                    </Badge>
                  </Tabs.Tab>
                </Tabs.List>
              </Tabs>

              {/* Companies Table */}
              <CompaniesTable
                onView={handleViewCompany}
                onApprove={handleApproveCompany}
                onBlock={handleBlockCompanyClick}
              />

              {/* Pagination and Results Info */}
              <Divider my="md" />
              <Group justify="space-between" mt="md">
                <Group gap="xs">
                  <Text size="sm" c="dimmed">
                    Showing 1-5 of 45 companies
                  </Text>
                  <SegmentedControl
                    size="xs"
                    data={[
                      { label: "5", value: "5" },
                      { label: "10", value: "10" },
                      { label: "25", value: "25" },
                    ]}
                    defaultValue="5"
                  />
                </Group>
                <Pagination total={9} size="sm" />
              </Group>
            </Card>
          </div>
        </div>

        {/* Modals */}
        <CompanyDetailsModal
          opened={detailsModalOpened}
          onClose={() => setDetailsModalOpened(false)}
          companyId={selectedCompanyId}
          onApprove={() => handleApproveCompany(selectedCompanyId!)}
          onBlock={() => setBlockModalOpened(true)}
        />

        <CompanyBlockModal
          opened={blockModalOpened}
          onClose={() => setBlockModalOpened(false)}
          onConfirm={confirmBlockCompany}
          companyId={selectedCompanyId}
        />
      </PageContainer>
    </>
  );
}
