import "./chunk-G3PMV62Z.js";

// node_modules/mantine-form-zod-resolver/dist/esm/index.mjs
function zodResolver(schema, options) {
  return (values) => {
    const parsed = schema.safeParse(values);
    if (parsed.success) {
      return {};
    }
    const results = {};
    if ("error" in parsed) {
      if ((options == null ? void 0 : options.errorPriority) === "first") {
        parsed.error.errors.reverse();
      }
      parsed.error.errors.forEach((error) => {
        results[error.path.join(".")] = error.message;
      });
    }
    return results;
  };
}
export {
  zodResolver
};
//# sourceMappingURL=mantine-form-zod-resolver.js.map
