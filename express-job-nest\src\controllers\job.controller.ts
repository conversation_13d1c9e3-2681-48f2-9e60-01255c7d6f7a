import { Request, Response, NextFunction } from "express";
import Job from "../models/job.model";
import Company from "../models/company.model";
import { AppError } from "../utils/app-error";
import { catchAsync } from "../utils/catch-async";

// Get all jobs with filtering, sorting, and pagination
export const getAllJobs = catchAsync(
  async (req: Request, res: Response, _next: NextFunction) => {
    // Build query
    const queryObj = { ...req.query };
    const excludedFields = ["page", "sort", "limit", "fields", "search"];
    excludedFields.forEach((field) => delete queryObj[field]);

    // Advanced filtering
    let queryStr = JSON.stringify(queryObj);
    queryStr = queryStr.replace(/\b(gte|gt|lte|lt)\b/g, (match) => `$${match}`);

    let query = Job.find(JSON.parse(queryStr)).populate({
      path: "company",
      select: "name logo location",
    });

    // Search functionality
    if (req.query.search) {
      query = query.find({
        $text: { $search: req.query.search as string },
      });
    }

    // Sorting
    if (req.query.sort) {
      const sortBy = (req.query.sort as string).split(",").join(" ");
      query = query.sort(sortBy);
    } else {
      query = query.sort("-createdAt");
    }

    // Field limiting
    if (req.query.fields) {
      const fields = (req.query.fields as string).split(",").join(" ");
      query = query.select(fields) as any;
    } else {
      query = query.select("-__v") as any;
    }

    // Pagination
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const skip = (page - 1) * limit;

    query = query.skip(skip).limit(limit);

    // Execute query
    const jobs = await query;
    const total = await Job.countDocuments(JSON.parse(queryStr));

    res.status(200).json({
      success: true,
      count: jobs.length,
      total,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      jobs,
    });
  }
);

// Get job by ID
export const getJobById = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    const job = await Job.findById(req.params.id).populate({
      path: "company",
      select: "name logo location description website socialLinks",
    });

    if (!job) {
      return next(new AppError("No job found with that ID", 404));
    }

    res.status(200).json({
      success: true,
      job,
    });
  }
);

// Create new job
export const createJob = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    // Check if the company exists and belongs to the user
    const company = await Company.findOne({
      _id: req.body.company,
      owner: req.user._id as unknown as string,
    });

    if (!company) {
      return next(
        new AppError("You can only create jobs for your own company", 403)
      );
    }

    const newJob = await Job.create({
      ...req.body,
      company: company._id,
    });

    res.status(201).json({
      success: true,
      job: newJob,
    });
  }
);

// Update job
export const updateJob = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    // Find the job
    const job = await Job.findById(req.params.id);

    if (!job) {
      return next(new AppError("No job found with that ID", 404));
    }

    // Check if the job belongs to the user's company
    const company = await Company.findOne({
      _id: job.company,
      owner: req.user._id,
    });

    if (!company) {
      return next(
        new AppError("You can only update jobs for your own company", 403)
      );
    }

    // Update the job
    const updatedJob = await Job.findByIdAndUpdate(req.params.id, req.body, {
      new: true,
      runValidators: true,
    });

    res.status(200).json({
      success: true,
      job: updatedJob,
    });
  }
);

// Delete job
export const deleteJob = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    // Find the job
    const job = await Job.findById(req.params.id);

    if (!job) {
      return next(new AppError("No job found with that ID", 404));
    }

    // Check if the job belongs to the user's company
    const company = await Company.findOne({
      _id: job.company,
      owner: req.user._id,
    });

    if (!company) {
      return next(
        new AppError("You can only delete jobs for your own company", 403)
      );
    }

    // Delete the job
    await Job.findByIdAndDelete(req.params.id);

    res.status(204).json({
      success: true,
      data: null,
    });
  }
);

// Get jobs by company
export const getJobsByCompany = catchAsync(
  async (req: Request, res: Response, _next: NextFunction) => {
    const jobs = await Job.find({ company: req.params.companyId });

    res.status(200).json({
      success: true,
      count: jobs.length,
      jobs,
    });
  }
);

// Get jobs posted by the current user
export const getMyJobs = catchAsync(
  async (req: Request, res: Response, _next: NextFunction) => {
    // Find companies owned by the user
    const companies = await Company.find({
      owner: req.user._id as unknown as string,
    });
    const companyIds = companies.map((company) => company._id);

    // Find jobs posted by these companies
    const jobs = await Job.find({ company: { $in: companyIds } }).populate({
      path: "company",
      select: "name logo",
    });

    res.status(200).json({
      success: true,
      count: jobs.length,
      jobs,
    });
  }
);
