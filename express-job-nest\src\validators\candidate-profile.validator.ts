import { body } from "express-validator";

export const updateProfileValidator = [
  body("headline")
    .optional()
    .isString()
    .withMessage("Headline must be a string")
    .isLength({ max: 100 })
    .withMessage("Headline cannot exceed 100 characters"),

  body("summary")
    .optional()
    .isString()
    .withMessage("Summary must be a string")
    .isLength({ max: 2000 })
    .withMessage("Summary cannot exceed 2000 characters"),

  body("phone").optional().isString().withMessage("Phone must be a string"),

  body("location")
    .optional()
    .isString()
    .withMessage("Location must be a string"),

  body("skills").optional().isArray().withMessage("Skills must be an array"),

  body("skills.*")
    .optional()
    .isString()
    .withMessage("Each skill must be a string"),

  body("preferredJobTypes")
    .optional()
    .isArray()
    .withMessage("Preferred job types must be an array"),

  body("preferredJobTypes.*")
    .optional()
    .isString()
    .withMessage("Each job type must be a string"),

  body("preferredLocations")
    .optional()
    .isArray()
    .withMessage("Preferred locations must be an array"),

  body("preferredLocations.*")
    .optional()
    .isString()
    .withMessage("Each location must be a string"),

  body("preferredSalary.min")
    .optional()
    .isNumeric()
    .withMessage("Minimum salary must be a number")
    .custom((value, { req }) => {
      if (
        req.body.preferredSalary?.max &&
        value > req.body.preferredSalary.max
      ) {
        throw new Error("Minimum salary cannot be greater than maximum salary");
      }
      return true;
    }),

  body("preferredSalary.max")
    .optional()
    .isNumeric()
    .withMessage("Maximum salary must be a number"),

  body("preferredSalary.currency")
    .optional()
    .isString()
    .withMessage("Currency must be a string")
    .isLength({ min: 3, max: 3 })
    .withMessage("Currency must be a 3-letter code"),

  body("availability")
    .optional()
    .isIn(["Immediate", "2 weeks", "1 month", "3 months", "Not available"])
    .withMessage("Invalid availability option"),

  body("willingToRelocate")
    .optional()
    .isBoolean()
    .withMessage("Willing to relocate must be a boolean"),

  body("socialLinks.linkedin")
    .optional()
    .isURL()
    .withMessage("LinkedIn URL must be valid"),

  body("socialLinks.github")
    .optional()
    .isURL()
    .withMessage("GitHub URL must be valid"),

  body("socialLinks.twitter")
    .optional()
    .isURL()
    .withMessage("Twitter URL must be valid"),

  body("socialLinks.website")
    .optional()
    .isURL()
    .withMessage("Website URL must be valid"),

  body("portfolioLinks")
    .optional()
    .isArray()
    .withMessage("Portfolio links must be an array"),

  body("portfolioLinks.*.title")
    .optional()
    .isString()
    .withMessage("Portfolio link title must be a string"),

  body("portfolioLinks.*.url")
    .optional()
    .isURL()
    .withMessage("Portfolio link URL must be valid"),

  body("languages")
    .optional()
    .isArray()
    .withMessage("Languages must be an array"),

  body("languages.*.language")
    .optional()
    .isString()
    .withMessage("Language name must be a string"),

  body("languages.*.proficiency")
    .optional()
    .isIn(["Beginner", "Intermediate", "Advanced", "Native/Fluent"])
    .withMessage("Invalid language proficiency level"),
];

export const educationValidator = [
  body("institution")
    .notEmpty()
    .withMessage("Institution name is required")
    .isString()
    .withMessage("Institution name must be a string"),

  body("degree")
    .notEmpty()
    .withMessage("Degree is required")
    .isString()
    .withMessage("Degree must be a string"),

  body("fieldOfStudy")
    .optional()
    .isString()
    .withMessage("Field of study must be a string"),

  body("startDate")
    .notEmpty()
    .withMessage("Start date is required")
    .isISO8601()
    .withMessage("Start date must be a valid date"),

  body("endDate")
    .optional()
    .isISO8601()
    .withMessage("End date must be a valid date")
    .custom((value, { req }) => {
      if (value && new Date(value) < new Date(req.body.startDate)) {
        throw new Error("End date cannot be before start date");
      }
      return true;
    }),

  body("description")
    .optional()
    .isString()
    .withMessage("Description must be a string"),

  body("isCurrentlyStudying")
    .optional()
    .isBoolean()
    .withMessage("Is currently studying must be a boolean")
    .custom((value, { req }) => {
      if (value === true && req.body.endDate) {
        throw new Error(
          "End date should not be provided if currently studying"
        );
      }
      return true;
    }),
];

export const experienceValidator = [
  body("company")
    .notEmpty()
    .withMessage("Company name is required")
    .isString()
    .withMessage("Company name must be a string"),

  body("position")
    .notEmpty()
    .withMessage("Position is required")
    .isString()
    .withMessage("Position must be a string"),

  body("location")
    .optional()
    .isString()
    .withMessage("Location must be a string"),

  body("startDate")
    .notEmpty()
    .withMessage("Start date is required")
    .isISO8601()
    .withMessage("Start date must be a valid date"),

  body("endDate")
    .optional()
    .isISO8601()
    .withMessage("End date must be a valid date")
    .custom((value, { req }) => {
      if (value && new Date(value) < new Date(req.body.startDate)) {
        throw new Error("End date cannot be before start date");
      }
      return true;
    }),

  body("description")
    .optional()
    .isString()
    .withMessage("Description must be a string"),

  body("isCurrentlyWorking")
    .optional()
    .isBoolean()
    .withMessage("Is currently working must be a boolean")
    .custom((value, { req }) => {
      if (value === true && req.body.endDate) {
        throw new Error("End date should not be provided if currently working");
      }
      return true;
    }),
];
