"use client";

import { <PERSON><PERSON><PERSON> } from "@mantine/charts";
import EmployerMainCard from "../EmployerMainCard";
import EmployerSectionHeading from "../EmployerSectionHeading";

const candidateProgressData = [
  { label: "Hired", value: 30 },
  { label: "Interviewed", value: 20 },
  { label: "Applied", value: 70 },
];

export default function CandidateProgress() {
  return (
    <EmployerMainCard>
      <EmployerSectionHeading>Candidate Progress</EmployerSectionHeading>
      <BarChart
        data={candidateProgressData}
        dataKey="label"
        series={[{ name: "value", color: "blue" }]}
        withTooltip
        withLegend
        h={300} // Fixed height
        w="100%" // Full width
      />
    </EmployerMainCard>
  );
}
