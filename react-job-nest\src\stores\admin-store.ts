"use client";

import { create } from "zustand";

// Define the admin navigation store type
interface AdminNavigationStore {
  activePath: string;
  setActivePath: (path: string) => void;
}

// Create the admin navigation store
export const useAdminNavigationStore = create<AdminNavigationStore>((set) => ({
  activePath: "",
  setActivePath: (path) => set({ activePath: path }),
}));

// Define the user actions store type
interface UserActionsStore {
  deleteUserCallback: (() => void) | null;
  setDeleteUserCallback: (callback: (() => void) | null) => void;
}

// Create the user actions store
export const useUserActionsStore = create<UserActionsStore>((set) => ({
  deleteUserCallback: null,
  setDeleteUserCallback: (callback) => set({ deleteUserCallback: callback }),
}));
