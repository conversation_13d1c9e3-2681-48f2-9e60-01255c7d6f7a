import mongoose from "mongoose";
import dotenv from "dotenv";
import SubscriptionPlan from "../models/subscription-plan.model";

// Load environment variables
dotenv.config();

// Connect to MongoDB
const connectDB = async () => {
  try {
    const conn = await mongoose.connect(
      process.env.MONGODB_URI || "mongodb://localhost:27017/job-nest"
    );
    console.log(`MongoDB Connected: ${conn.connection.host}`);
    return conn;
  } catch (error) {
    console.error(`Error connecting to MongoDB: ${error}`);
    process.exit(1);
  }
};

// Seed data
const subscriptionPlans = [
  {
    name: "Basic",
    description: "Perfect for startups and small businesses",
    price: 29,
    annualPrice: 295,
    currency: "USD",
    features: {
      activeJobs: 2,
      jobVisibilityDays: 30,
      cvViews: 50,
      featuredJobs: 0,
      priorityPlacement: false,
      featuredCompanyProfile: false,
      candidateMatching: false,
      advancedAnalytics: false,
      dedicatedSupport: false,
      customFeatures: [],
    },
    isActive: true,
    isPopular: false,
    sortOrder: 1,
  },
  {
    name: "Professional",
    description: "Ideal for growing businesses",
    price: 59,
    annualPrice: 565,
    currency: "USD",
    features: {
      activeJobs: 5,
      jobVisibilityDays: 45,
      cvViews: 200,
      featuredJobs: 2,
      priorityPlacement: true,
      featuredCompanyProfile: false,
      candidateMatching: true,
      advancedAnalytics: false,
      dedicatedSupport: false,
      customFeatures: [],
    },
    isActive: true,
    isPopular: true,
    sortOrder: 2,
  },
  {
    name: "Enterprise",
    description: "For large organizations with high-volume hiring needs",
    price: 149,
    annualPrice: 1341,
    currency: "USD",
    features: {
      activeJobs: "unlimited",
      jobVisibilityDays: 60,
      cvViews: "unlimited",
      featuredJobs: 10,
      priorityPlacement: true,
      featuredCompanyProfile: true,
      candidateMatching: true,
      advancedAnalytics: true,
      dedicatedSupport: true,
      customFeatures: [
        "Custom job application forms",
        "API access",
        "Bulk job posting",
      ],
    },
    isActive: true,
    isPopular: false,
    sortOrder: 3,
  },
];

// Seed function
const seedSubscriptionPlans = async () => {
  try {
    // Connect to the database
    await connectDB();

    // Delete existing subscription plans
    await SubscriptionPlan.deleteMany({});
    console.log("Deleted existing subscription plans");

    // Insert new subscription plans
    const createdPlans = await SubscriptionPlan.insertMany(subscriptionPlans);
    console.log(`Inserted ${createdPlans.length} subscription plans`);

    // Close the connection
    await mongoose.disconnect();
    console.log("MongoDB disconnected");

    console.log("Seed completed successfully");
    process.exit(0);
  } catch (error) {
    console.error("Error seeding subscription plans:", error);
    process.exit(1);
  }
};

// Run the seed function
seedSubscriptionPlans();
