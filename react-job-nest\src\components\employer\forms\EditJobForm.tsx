"use client";

import useEditJobForm from "@/hooks/employer/use-edit-job-form";
import {
  ActionIcon,
  Badge,
  Button,
  Card,
  FileInput,
  Grid,
  Group,
  MultiSelect,
  NumberInput,
  Paper,
  RingProgress,
  Select,
  Switch,
  Tabs,
  TagsInput,
  Text,
  TextInput,
  Textarea,
  Title,
} from "@mantine/core";
import { DatePickerInput } from "@mantine/dates";
import { notifications } from "@mantine/notifications";
import { useEffect, useState } from "react";
import {
  FaArrowLeft,
  FaBriefcase,
  FaCalendar,
  FaCheckCircle,
  FaDollarSign,
  FaFileAlt,
  FaGift,
  FaMapMarkerAlt,
  FaPaperPlane,
  FaQuestionCircle,
  FaSave,
  FaTags,
  FaTrash,
} from "react-icons/fa";
import { Link } from "react-router";

const predefinedQuestions = [
  "Why do you want to work for our company?",
  "What are your strengths and weaknesses?",
  "Where do you see yourself in 5 years?",
  "Describe a challenging situation you faced and how you handled it.",
];

const jobCategories = [
  { value: "software", label: "Software Development" },
  { value: "web-development", label: "Web Development" },
  { value: "mobile-development", label: "Mobile Development" },
  { value: "data-science", label: "Data Science" },
  {
    value: "ai-ml",
    label: "Artificial Intelligence & Machine Learning",
  },
  { value: "devops", label: "DevOps" },
  { value: "cloud-computing", label: "Cloud Computing" },
  { value: "cybersecurity", label: "Cybersecurity" },
  { value: "it-support", label: "IT Support" },
  { value: "networking", label: "Networking" },
  { value: "ui-ux-design", label: "UI/UX Design" },
  { value: "graphic-design", label: "Graphic Design" },
  { value: "marketing", label: "Marketing" },
  { value: "digital-marketing", label: "Digital Marketing" },
  { value: "content-writing", label: "Content Writing" },
  { value: "social-media", label: "Social Media Management" },
  { value: "sales", label: "Sales" },
  { value: "customer-support", label: "Customer Support" },
  { value: "finance", label: "Finance" },
  { value: "accounting", label: "Accounting" },
  { value: "human-resources", label: "Human Resources" },
  { value: "project-management", label: "Project Management" },
  { value: "product-management", label: "Product Management" },
  {
    value: "business-development",
    label: "Business Development",
  },
  { value: "consulting", label: "Consulting" },
  { value: "education", label: "Education" },
  { value: "healthcare", label: "Healthcare" },
  { value: "legal", label: "Legal" },
  { value: "engineering", label: "Engineering" },
  {
    value: "mechanical-engineering",
    label: "Mechanical Engineering",
  },
  { value: "civil-engineering", label: "Civil Engineering" },
  {
    value: "electrical-engineering",
    label: "Electrical Engineering",
  },
  { value: "manufacturing", label: "Manufacturing" },
  { value: "logistics", label: "Logistics" },
  { value: "supply-chain", label: "Supply Chain Management" },
  { value: "retail", label: "Retail" },
  { value: "hospitality", label: "Hospitality" },
  { value: "tourism", label: "Tourism" },
  { value: "gaming", label: "Gaming" },
  { value: "media", label: "Media & Entertainment" },
  { value: "non-profit", label: "Non-Profit" },
  { value: "government", label: "Government" },
  { value: "other", label: "Other" },
];

interface EditJobFormProps {
  jobId: string;
}

export default function EditJobForm({ jobId }: EditJobFormProps) {
  const { form, handleSubmit, isLoading, isSubmitting, job } =
    useEditJobForm(jobId);

  // State for active tab
  const [activeTab, setActiveTab] = useState<string | null>("basic");

  // State for form completion
  const [formCompletion, setFormCompletion] = useState(75);

  // State for job preview

  // State for questions
  const [questions, setQuestions] = useState<string[]>([""]);

  // Calculate form completion percentage
  useEffect(() => {
    const requiredFields = [
      "jobTitle",
      "requiredExperience",
      "location",
      "jobType",
      "jobDescription",
      "jobRequirements",
      "jobCategory",
    ];

    const filledFields = requiredFields.filter(
      (field) => form.values[field as keyof typeof form.values],
    );

    const completionPercentage = Math.round(
      (filledFields.length / requiredFields.length) * 100,
    );

    setFormCompletion(completionPercentage);
  }, [form, form.values]);

  const handleQuestionChange = (index: number, value: string) => {
    const newQuestions = [...questions];
    newQuestions[index] = value;
    setQuestions(newQuestions);
  };

  const addQuestion = () => {
    setQuestions([...questions, ""]);
  };

  const removeQuestion = (index: number) => {
    const newQuestions = questions.filter((_, i) => i !== index);
    setQuestions(newQuestions);
  };

  const addPredefinedQuestion = (question: string) => {
    setQuestions([...questions, question]);
  };

  // Handle form submission
  const onSubmit = (values: any) => {
    // Add questions to the form values
    const formValues = {
      ...values,
      questions: questions.filter((q) => q.trim() !== ""),
    };

    console.log("Form values:", formValues);
    handleSubmit(formValues);
  };

  // Handle save as draft
  const handleSaveAsDraft = () => {
    // In a real implementation, we would save the form as a draft
    // For now, just show a notification
    console.log("Saving as draft:", form.values);

    notifications.show({
      title: "Draft Saved",
      message: "Your job posting has been saved as a draft",
      color: "blue",
      icon: <FaSave />,
    });
  };

  // Add debug logging
  console.log("EditJobForm render state:", {
    isLoading,
    job,
    formValues: form.values,
  });

  return (
    <form
      noValidate
      onSubmit={form.onSubmit(onSubmit)}
      className="flex flex-col gap-8"
    >
      {/* Loading overlay */}
      {isLoading && (
        <Card withBorder radius="md" className="mb-6 shadow-sm" p="lg">
          <Text ta="center" size="lg" fw={500} className="py-8">
            Loading job data...
          </Text>
        </Card>
      )}

      {!isLoading && !job && (
        <Card withBorder radius="md" className="mb-6 shadow-sm" p="lg">
          <Text ta="center" size="lg" fw={500} className="py-4">
            Job not found or there was an error loading the job data.
          </Text>
          <Group justify="center" mt="md">
            <Button
              variant="filled"
              leftSection={<FaArrowLeft size={14} />}
              component={Link}
              to="/employer/manage-jobs"
            >
              Back to Jobs
            </Button>
          </Group>
        </Card>
      )}

      {!isLoading && job && (
        <>
          {/* Form Header with Progress */}
          <Card withBorder radius="md" className="mb-6 shadow-sm" p="md">
            <Group justify="space-between" align="center">
              <div>
                <Title order={4} className="mb-1">
                  Job Posting Completion
                </Title>
                <Text size="sm" c="dimmed">
                  Complete all required fields to publish your job posting
                </Text>
              </div>
              <Group>
                <RingProgress
                  size={80}
                  thickness={8}
                  roundCaps
                  sections={[{ value: formCompletion, color: "blue" }]}
                  label={
                    <Text ta="center" size="sm" fw={700}>
                      {formCompletion}%
                    </Text>
                  }
                />
                {formCompletion === 100 && (
                  <Badge color="green" size="lg" variant="filled">
                    <Group gap={6}>
                      <FaCheckCircle size={14} />
                      <span>Ready to Publish</span>
                    </Group>
                  </Badge>
                )}
              </Group>
            </Group>
          </Card>

          {/* Tabbed Interface */}
          <Tabs value={activeTab} onChange={setActiveTab} className="mb-6">
            <Tabs.List grow>
              <Tabs.Tab value="basic" leftSection={<FaBriefcase size={16} />}>
                Basic Information
              </Tabs.Tab>
              <Tabs.Tab value="details" leftSection={<FaFileAlt size={16} />}>
                Job Details
              </Tabs.Tab>
              <Tabs.Tab value="requirements" leftSection={<FaGift size={16} />}>
                Requirements & Benefits
              </Tabs.Tab>
              <Tabs.Tab
                value="application"
                leftSection={<FaCalendar size={16} />}
              >
                Application Settings
              </Tabs.Tab>
            </Tabs.List>

            {/* Basic Information Tab */}
            <Tabs.Panel value="basic" pt="xl">
              <Card withBorder radius="md" className="mb-6 shadow-sm" p="lg">
                <Title order={3} className="mb-4 text-blue-800">
                  Basic Information
                </Title>
                <Text c="dimmed" className="mb-6">
                  Provide the essential information about the job position
                </Text>

                <Grid gutter="md">
                  <Grid.Col span={{ base: 12, md: 6 }}>
                    <TextInput
                      label="Job Title"
                      placeholder="Enter job title"
                      required
                      leftSection={<FaBriefcase size={18} />}
                      {...form.getInputProps("jobTitle")}
                      error={form.errors.jobTitle}
                    />
                  </Grid.Col>
                  <Grid.Col span={{ base: 12, md: 6 }}>
                    <Select
                      label="Required Experience"
                      placeholder="Select required experience"
                      data={[
                        { value: "internship", label: "Internship" },
                        { value: "1-2 years", label: "1-2 years" },
                        { value: "3-5 years", label: "3-5 years" },
                        { value: "5-10 years", label: "5-10 years" },
                        { value: "10+ years", label: "10+ years" },
                      ]}
                      required
                      leftSection={<FaBriefcase size={18} />}
                      {...form.getInputProps("requiredExperience")}
                      error={form.errors.requiredExperience}
                    />
                  </Grid.Col>
                  <Grid.Col span={{ base: 12, md: 6 }}>
                    <Select
                      label="Location"
                      placeholder="Select location"
                      data={[
                        { value: "remote", label: "Remote" },
                        { value: "onsite", label: "Onsite" },
                        { value: "hybrid", label: "Hybrid" },
                      ]}
                      required
                      leftSection={<FaMapMarkerAlt size={18} />}
                      {...form.getInputProps("location")}
                      error={form.errors.location}
                    />
                  </Grid.Col>
                  <Grid.Col span={{ base: 12, md: 6 }}>
                    <Select
                      label="Job Type"
                      placeholder="Select job type"
                      data={[
                        { value: "full-time", label: "Full Time" },
                        { value: "part-time", label: "Part Time" },
                        { value: "internship", label: "Internship" },
                        { value: "contract", label: "Contract" },
                        { value: "temporary", label: "Temporary" },
                      ]}
                      required
                      leftSection={<FaBriefcase size={18} />}
                      {...form.getInputProps("jobType")}
                      error={form.errors.jobType}
                    />
                  </Grid.Col>
                </Grid>
              </Card>

              <Card withBorder radius="md" className="shadow-sm" p="lg">
                <Title order={3} className="mb-4 text-blue-800">
                  Salary Information
                </Title>
                <Text c="dimmed" className="mb-6">
                  Provide salary details to attract qualified candidates
                </Text>

                <Grid gutter="md">
                  <Grid.Col span={{ base: 12, md: 4 }}>
                    <NumberInput
                      label="Minimum Salary"
                      placeholder="Enter minimum salary"
                      min={0}
                      step={100}
                      leftSection={<FaDollarSign size={18} />}
                      {...form.getInputProps("minSalary")}
                      error={form.errors.minSalary}
                    />
                  </Grid.Col>
                  <Grid.Col span={{ base: 12, md: 4 }}>
                    <NumberInput
                      label="Maximum Salary"
                      placeholder="Enter maximum salary"
                      min={0}
                      step={100}
                      leftSection={<FaDollarSign size={18} />}
                      {...form.getInputProps("maxSalary")}
                      error={form.errors.maxSalary}
                    />
                  </Grid.Col>
                  <Grid.Col span={{ base: 12, md: 4 }}>
                    <Select
                      label="Currency"
                      placeholder="Select currency"
                      data={[
                        { value: "EGP", label: "EGP (Egyptian Pound)" },
                        { value: "SAR", label: "SAR (Saudi Riyal)" },
                        { value: "USD", label: "USD (US Dollar)" },
                        { value: "EUR", label: "EUR (Euro)" },
                        { value: "GBP", label: "GBP (British Pound)" },
                      ]}
                      leftSection={<FaDollarSign size={18} />}
                      {...form.getInputProps("currency")}
                      error={form.errors.currency}
                    />
                  </Grid.Col>
                  <Grid.Col span={12}>
                    <Switch
                      label="Show salary range to applicants"
                      {...form.getInputProps("showSalary")}
                      error={form.errors.showSalary}
                      className="mt-2"
                    />
                  </Grid.Col>
                </Grid>
              </Card>
            </Tabs.Panel>

            {/* Job Details Tab */}
            <Tabs.Panel value="details" pt="xl">
              <Card withBorder radius="md" className="shadow-sm" p="lg">
                <Title order={3} className="mb-4 text-blue-800">
                  Job Description
                </Title>
                <Text c="dimmed" className="mb-6">
                  Provide a detailed description of the job position
                </Text>

                <Textarea
                  label="Job Description"
                  placeholder="Enter job description"
                  required
                  minRows={8}
                  {...form.getInputProps("jobDescription")}
                  error={form.errors.jobDescription}
                  className="mb-6"
                />

                <Textarea
                  label="Job Requirements"
                  placeholder="Enter job requirements"
                  required
                  minRows={8}
                  {...form.getInputProps("jobRequirements")}
                  error={form.errors.jobRequirements}
                />
              </Card>
            </Tabs.Panel>

            {/* Requirements & Benefits Tab */}
            <Tabs.Panel value="requirements" pt="xl">
              <Card withBorder radius="md" className="mb-6 shadow-sm" p="lg">
                <Title order={3} className="mb-4 text-blue-800">
                  Job Category and Tags
                </Title>
                <Text c="dimmed" className="mb-6">
                  Categorize your job to help candidates find it more easily
                </Text>

                <Grid gutter="md">
                  <Grid.Col span={{ base: 12, md: 6 }}>
                    <Select
                      label="Job Category"
                      placeholder="Select job category"
                      data={jobCategories}
                      required
                      leftSection={<FaTags size={18} />}
                      {...form.getInputProps("jobCategory")}
                      error={form.errors.jobCategory}
                      searchable
                    />
                  </Grid.Col>
                  <Grid.Col span={{ base: 12, md: 6 }}>
                    <TagsInput
                      label="Job Tags"
                      placeholder="Add tags (e.g., React, Python, Marketing)"
                      leftSection={<FaTags size={18} />}
                      {...form.getInputProps("jobTags")}
                      error={form.errors.jobTags}
                    />
                  </Grid.Col>
                </Grid>
              </Card>

              <Card withBorder radius="md" className="shadow-sm" p="lg">
                <Title order={3} className="mb-4 text-blue-800">
                  Benefits and Perks
                </Title>
                <Text c="dimmed" className="mb-6">
                  Highlight the benefits and perks offered with this position
                </Text>

                <MultiSelect
                  label="Benefits and Perks"
                  placeholder="Select benefits and perks"
                  data={[
                    { value: "health-insurance", label: "Health Insurance" },
                    { value: "dental-insurance", label: "Dental Insurance" },
                    { value: "remote-work", label: "Remote Work" },
                    { value: "flexible-hours", label: "Flexible Hours" },
                    { value: "paid-time-off", label: "Paid Time Off" },
                    { value: "bonuses", label: "Bonuses" },
                    { value: "stock-options", label: "Stock Options" },
                    {
                      value: "professional-development",
                      label: "Professional Development",
                    },
                    { value: "gym-membership", label: "Gym Membership" },
                    { value: "parental-leave", label: "Parental Leave" },
                    { value: "retirement-plan", label: "Retirement Plan" },
                    { value: "company-events", label: "Company Events" },
                  ]}
                  leftSection={<FaGift size={18} />}
                  {...form.getInputProps("benefits")}
                  error={form.errors.benefits}
                  searchable
                  clearable
                />
              </Card>
            </Tabs.Panel>

            {/* Application Settings Tab */}
            <Tabs.Panel value="application" pt="xl">
              <Card withBorder radius="md" className="mb-6 shadow-sm" p="lg">
                <Title order={3} className="mb-4 text-blue-800">
                  Application Settings
                </Title>
                <Text c="dimmed" className="mb-6">
                  Configure how candidates can apply for this position
                </Text>

                <Grid gutter="md">
                  <Grid.Col span={{ base: 12, md: 6 }}>
                    <DatePickerInput
                      label="Application Deadline"
                      placeholder="Select deadline"
                      leftSection={<FaCalendar size={18} />}
                      {...form.getInputProps("applicationDeadline")}
                      error={form.errors.applicationDeadline}
                      clearable
                    />
                  </Grid.Col>
                  <Grid.Col span={{ base: 12, md: 6 }}>
                    <FileInput
                      label="Job Attachment (Optional)"
                      placeholder="Upload job attachment"
                      accept=".pdf,.doc,.docx"
                      leftSection={<FaFileAlt size={18} />}
                      {...form.getInputProps("jobAttachment")}
                      error={form.errors.jobAttachment}
                    />
                  </Grid.Col>
                </Grid>
              </Card>

              <Card withBorder radius="md" className="shadow-sm" p="lg">
                <Title order={3} className="mb-4 text-blue-800">
                  Screening Questions
                </Title>
                <Text c="dimmed" className="mb-6">
                  Add questions to screen candidates during the application
                  process
                </Text>

                <Group justify="space-between" className="mb-4">
                  <Select
                    label="Predefined Questions"
                    placeholder="Select a predefined question"
                    data={predefinedQuestions.map((question) => ({
                      value: question,
                      label: question,
                    }))}
                    onChange={(value) => value && addPredefinedQuestion(value)}
                    className="flex-grow"
                  />
                  <Button
                    onClick={addQuestion}
                    leftSection={<FaQuestionCircle size={14} />}
                    className="self-end"
                  >
                    Add Question
                  </Button>
                </Group>

                {questions.map((question, index) => (
                  <Paper
                    withBorder
                    p="md"
                    radius="md"
                    key={index}
                    className="mb-4"
                  >
                    <Group justify="space-between" align="flex-start">
                      <TextInput
                        label={`Question ${index + 1}`}
                        placeholder="Enter a question"
                        value={question}
                        onChange={(event) =>
                          handleQuestionChange(index, event.currentTarget.value)
                        }
                        className="flex-grow"
                      />
                      <ActionIcon
                        color="red"
                        variant="subtle"
                        onClick={() => removeQuestion(index)}
                        className="self-end"
                      >
                        <FaTrash size={16} />
                      </ActionIcon>
                    </Group>
                  </Paper>
                ))}
              </Card>
            </Tabs.Panel>
          </Tabs>

          {/* Action Buttons */}
          <Group justify="space-between" mt="xl">
            <Button
              variant="default"
              leftSection={<FaArrowLeft size={14} />}
              component={Link}
              to="/employer/manage-jobs"
            >
              Back to Jobs
            </Button>

            <Group>
              <Button
                variant="outline"
                leftSection={<FaSave size={14} />}
                onClick={handleSaveAsDraft}
              >
                Save as Draft
              </Button>
              <Button
                type="submit"
                leftSection={<FaPaperPlane size={14} />}
                loading={isSubmitting}
                disabled={isSubmitting}
              >
                Update Job
              </Button>
            </Group>
          </Group>
        </>
      )}
    </form>
  );
}
