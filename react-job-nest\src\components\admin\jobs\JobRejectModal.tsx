"use client";

import { jobStatusUpdateSchema } from "@/schemas/admin/job-management-schema";
import { Button, Group, Modal, Textarea } from "@mantine/core";
import { useForm } from "@mantine/form";
import { zodResolver } from "mantine-form-zod-resolver";

interface JobRejectModalProps {
  opened: boolean;
  onClose: () => void;
  onConfirm: (reason: string) => void;
  jobId: number | null;
}

export default function JobRejectModal({
  opened,
  onClose,
  onConfirm,
  // jobId,
}: JobRejectModalProps) {
  const form = useForm({
    initialValues: {
      status: "rejected" as const,
      rejectionReason: "",
    },
    validate: zod<PERSON><PERSON>olver(jobStatusUpdateSchema),
  });

  const handleSubmit = (values: typeof form.values) => {
    onConfirm(values.rejectionReason);
    form.reset();
    onClose();
  };

  return (
    <Modal opened={opened} onClose={onClose} title="Reject Job" centered>
      <form onSubmit={form.onSubmit(handleSubmit)}>
        <Textarea
          label="Rejection Reason"
          placeholder="Please provide a reason for rejecting this job"
          required
          minRows={4}
          {...form.getInputProps("rejectionReason")}
        />
        <Group mt="md" justify="flex-end">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button color="red" type="submit">
            Reject Job
          </Button>
        </Group>
      </form>
    </Modal>
  );
}
