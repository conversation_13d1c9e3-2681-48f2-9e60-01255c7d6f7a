import { cn } from "@/design-system/utils";
import { Progress } from "@mantine/core";
import { type IconType } from "react-icons";
import { FaArrowDown, FaArrowUp } from "react-icons/fa";
import Card from "./Card";

export interface MetricCardProps {
  icon: IconType;
  title: string;
  value: number | string;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  progress?: number;
  className?: string;
  variant?: "default" | "admin" | "employer" | "candidate";
}

/**
 * Reusable metric card component for dashboards
 *
 * @param variant - Different styling variants based on user role
 */
export default function MetricCard({
  icon: Icon,
  title,
  value,
  trend,
  progress,
  className = "",
  variant = "default",
}: MetricCardProps) {
  // Apply variant-specific styling
  const iconSize = {
    default: { base: 20, sm: 24 },
    admin: { base: 20, sm: 24 },
    employer: { base: 24, sm: 24 },
    candidate: { base: 20, sm: 24 },
  };

  const titleSize = {
    default: "text-base sm:text-lg",
    admin: "text-base sm:text-lg",
    employer: "text-lg",
    candidate: "text-base sm:text-lg",
  };

  const valueSize = {
    default: "text-lg font-bold sm:text-xl",
    admin: "text-lg font-bold sm:text-xl",
    employer: "text-xl font-bold",
    candidate: "text-lg font-bold sm:text-xl",
  };

  const trendSize = {
    default: "text-xs sm:text-sm",
    admin: "text-xs sm:text-sm",
    employer: "text-sm",
    candidate: "text-xs sm:text-sm",
  };

  const arrowSize = {
    default: { base: 10, sm: 12 },
    admin: { base: 10, sm: 12 },
    employer: { base: 12, sm: 12 },
    candidate: { base: 10, sm: 12 },
  };

  return (
    <Card
      className={cn("flex flex-col justify-between", className)}
      variant={variant}
    >
      <div className="flex items-center gap-2">
        <Icon
          size={iconSize[variant].base}
          className={`text-primary-color ${variant !== "employer" ? "sm:text-[" + iconSize[variant].sm + "px]" : ""}`}
        />
        <p className={`text-primary-color ${titleSize[variant]}`}>{title}</p>
      </div>
      <p className={`my-2 ${valueSize[variant]}`}>{value}</p>
      {trend && (
        <p className={`text-[#868e96] ${trendSize[variant]}`}>
          {trend.isPositive ? (
            <FaArrowUp
              size={arrowSize[variant].base}
              className={`inline text-green-500 ${variant !== "employer" ? "sm:text-[" + arrowSize[variant].sm + "px]" : ""}`}
            />
          ) : (
            <FaArrowDown
              size={arrowSize[variant].base}
              className={`inline text-red-500 ${variant !== "employer" ? "sm:text-[" + arrowSize[variant].sm + "px]" : ""}`}
            />
          )}{" "}
          {Math.abs(trend.value)}% from last month
        </p>
      )}
      {progress !== undefined && <Progress value={progress} />}
    </Card>
  );
}
