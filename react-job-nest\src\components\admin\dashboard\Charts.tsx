import SectionHeading from "@/design-system/components/typography/SectionHeading";
import { useThemeClasses } from "@/design-system/utils/theme-utils";
import {
  Grid,
  SegmentedControl,
  Tabs,
  Text,
  useMantineColorScheme,
} from "@mantine/core";
import { useState } from "react";

export default function Charts() {
  const [timeRange, setTimeRange] = useState("month");
  const { colorScheme } = useMantineColorScheme();
  const isDark = colorScheme === "dark";

  return (
    <>
      <div className="flex flex-col justify-between gap-4 sm:flex-row sm:items-center">
        <SectionHeading variant="admin">Analytics</SectionHeading>
        <SegmentedControl
          value={timeRange}
          onChange={setTimeRange}
          data={[
            { label: "Week", value: "week" },
            { label: "Month", value: "month" },
            { label: "Quarter", value: "quarter" },
            { label: "Year", value: "year" },
          ]}
          size="xs"
          styles={{
            root: {
              backgroundColor: isDark
                ? "var(--mantine-color-dark-6)"
                : undefined,
              borderColor: isDark ? "var(--mantine-color-dark-4)" : undefined,
            },
          }}
        />
      </div>

      <Tabs
        defaultValue="users"
        mt="md"
        styles={{
          tab: {
            color: isDark ? "var(--mantine-color-gray-4)" : undefined,
            "&[data-active]": {
              color: isDark ? "var(--mantine-color-white)" : undefined,
            },
          },
        }}
      >
        <Tabs.List>
          <Tabs.Tab value="users">Users</Tabs.Tab>
          <Tabs.Tab value="jobs">Jobs</Tabs.Tab>
          <Tabs.Tab value="companies">Companies</Tabs.Tab>
          <Tabs.Tab value="applications">Applications</Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="users" pt="xl">
          <Grid gutter="md">
            <Grid.Col span={{ base: 12, md: 8 }}>
              <div
                className={useThemeClasses(
                  "flex h-[250px] items-center justify-center rounded-lg bg-gray-50 p-4 sm:h-[300px]",
                  "flex h-[250px] items-center justify-center rounded-lg bg-dark-6 p-4 sm:h-[300px]",
                )}
              >
                <div className="text-center">
                  <Text fw={600} size="lg" mb={8}>
                    User Registration Trends
                  </Text>
                  <Text c="dimmed" size="sm">
                    Chart showing user registration trends over time
                  </Text>
                </div>
              </div>
            </Grid.Col>
            <Grid.Col span={{ base: 12, md: 4 }}>
              <div
                className={useThemeClasses(
                  "flex h-[250px] items-center justify-center rounded-lg bg-gray-50 p-4 sm:h-[300px]",
                  "flex h-[250px] items-center justify-center rounded-lg bg-dark-6 p-4 sm:h-[300px]",
                )}
              >
                <div className="text-center">
                  <Text fw={600} size="lg" mb={8}>
                    User Distribution
                  </Text>
                  <Text c="dimmed" size="sm">
                    Pie chart showing user role distribution
                  </Text>
                </div>
              </div>
            </Grid.Col>
          </Grid>
        </Tabs.Panel>

        <Tabs.Panel value="jobs" pt="xl">
          <Grid gutter="md">
            <Grid.Col span={{ base: 12, md: 8 }}>
              <div
                className={useThemeClasses(
                  "flex h-[250px] items-center justify-center rounded-lg bg-gray-50 p-4 sm:h-[300px]",
                  "flex h-[250px] items-center justify-center rounded-lg bg-dark-6 p-4 sm:h-[300px]",
                )}
              >
                <div className="text-center">
                  <Text fw={600} size="lg" mb={8}>
                    Job Posting Trends
                  </Text>
                  <Text c="dimmed" size="sm">
                    Chart showing job posting trends over time
                  </Text>
                </div>
              </div>
            </Grid.Col>
            <Grid.Col span={{ base: 12, md: 4 }}>
              <div
                className={useThemeClasses(
                  "flex h-[250px] items-center justify-center rounded-lg bg-gray-50 p-4 sm:h-[300px]",
                  "flex h-[250px] items-center justify-center rounded-lg bg-dark-6 p-4 sm:h-[300px]",
                )}
              >
                <div className="text-center">
                  <Text fw={600} size="lg" mb={8}>
                    Job Categories
                  </Text>
                  <Text c="dimmed" size="sm">
                    Distribution of jobs by category
                  </Text>
                </div>
              </div>
            </Grid.Col>
          </Grid>
        </Tabs.Panel>

        <Tabs.Panel value="companies" pt="xl">
          <Grid gutter="md">
            <Grid.Col span={{ base: 12, md: 8 }}>
              <div
                className={useThemeClasses(
                  "flex h-[250px] items-center justify-center rounded-lg bg-gray-50 p-4 sm:h-[300px]",
                  "flex h-[250px] items-center justify-center rounded-lg bg-dark-6 p-4 sm:h-[300px]",
                )}
              >
                <div className="text-center">
                  <Text fw={600} size="lg" mb={8}>
                    Company Registration Trends
                  </Text>
                  <Text c="dimmed" size="sm">
                    Chart showing company registration trends
                  </Text>
                </div>
              </div>
            </Grid.Col>
            <Grid.Col span={{ base: 12, md: 4 }}>
              <div
                className={useThemeClasses(
                  "flex h-[250px] items-center justify-center rounded-lg bg-gray-50 p-4 sm:h-[300px]",
                  "flex h-[250px] items-center justify-center rounded-lg bg-dark-6 p-4 sm:h-[300px]",
                )}
              >
                <div className="text-center">
                  <Text fw={600} size="lg" mb={8}>
                    Company Industries
                  </Text>
                  <Text c="dimmed" size="sm">
                    Distribution of companies by industry
                  </Text>
                </div>
              </div>
            </Grid.Col>
          </Grid>
        </Tabs.Panel>

        <Tabs.Panel value="applications" pt="xl">
          <Grid gutter="md">
            <Grid.Col span={{ base: 12, md: 8 }}>
              <div
                className={useThemeClasses(
                  "flex h-[250px] items-center justify-center rounded-lg bg-gray-50 p-4 sm:h-[300px]",
                  "flex h-[250px] items-center justify-center rounded-lg bg-dark-6 p-4 sm:h-[300px]",
                )}
              >
                <div className="text-center">
                  <Text fw={600} size="lg" mb={8}>
                    Application Trends
                  </Text>
                  <Text c="dimmed" size="sm">
                    Chart showing job application trends
                  </Text>
                </div>
              </div>
            </Grid.Col>
            <Grid.Col span={{ base: 12, md: 4 }}>
              <div
                className={useThemeClasses(
                  "flex h-[250px] items-center justify-center rounded-lg bg-gray-50 p-4 sm:h-[300px]",
                  "flex h-[250px] items-center justify-center rounded-lg bg-dark-6 p-4 sm:h-[300px]",
                )}
              >
                <div className="text-center">
                  <Text fw={600} size="lg" mb={8}>
                    Application Status
                  </Text>
                  <Text c="dimmed" size="sm">
                    Distribution of applications by status
                  </Text>
                </div>
              </div>
            </Grid.Col>
          </Grid>
        </Tabs.Panel>
      </Tabs>
    </>
  );
}
