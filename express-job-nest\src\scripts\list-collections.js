/**
 * <PERSON><PERSON><PERSON> to list all collections in the MongoDB database
 * Run this script with: node src/scripts/list-collections.js
 */

require('dotenv').config();
const mongoose = require('mongoose');

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/job-nest')
  .then(() => {
    console.log('MongoDB Connected');
    
    // List all collections
    mongoose.connection.db.listCollections().toArray((err, collections) => {
      if (err) {
        console.error('Error listing collections:', err);
        process.exit(1);
      }
      
      console.log('Collections in the database:');
      collections.forEach(collection => {
        console.log(`- ${collection.name}`);
      });
      
      // Close connection and exit
      mongoose.connection.close();
      process.exit(0);
    });
  })
  .catch(err => {
    console.error('Error connecting to MongoDB:', err);
    process.exit(1);
  });
