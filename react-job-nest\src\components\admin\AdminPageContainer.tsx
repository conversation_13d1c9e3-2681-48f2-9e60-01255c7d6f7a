import PageContainer, {
  type PageContainerProps,
} from "@/design-system/components/layout/PageContainer";

/**
 * @deprecated Use PageContainer from design-system/components with variant="admin" instead
 */
export default function AdminPageContainer({
  children,
  breadcrumbItems,
  showBreadcrumbs = true,
  className,
}: PageContainerProps) {
  return (
    <PageContainer
      breadcrumbItems={breadcrumbItems}
      showBreadcrumbs={showBreadcrumbs}
      className={className}
      variant="admin"
    >
      {children}
    </PageContainer>
  );
}
