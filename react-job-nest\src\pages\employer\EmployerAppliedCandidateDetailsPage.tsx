"use client";

import CandidateProfilePageContent from "@/components/employer/candidate-profile/CandidateProfilePageContent";
import EmployerPageContainer from "@/components/employer/EmployerPageContainer";
import { candidateData } from "@/data/candidate-data";
import { useParams } from "react-router";

export default function EmployerAppliedCandidateDetailsPage() {
  const params = useParams();
  const candidateId = params.candidate_id;

  console.log(candidateId);

  // In a real app, you would fetch the candidate data based on the ID
  // For now, we'll use the mock data

  return (
    <EmployerPageContainer
      breadcrumbItems={[
        { title: "Home", href: "/" },
        { title: "Candidates", href: "/employer/candidates" },
        { title: candidateData.name },
      ]}
    >
      <CandidateProfilePageContent />
    </EmployerPageContainer>
  );
}
