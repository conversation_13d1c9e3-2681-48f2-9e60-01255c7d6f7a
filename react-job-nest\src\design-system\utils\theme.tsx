import { createTheme } from "@mantine/core";

export const theme = createTheme({
  // Default radius for components
  defaultRadius: "md",

  // Color palette
  colors: {
    // Primary color - blue shade
    primary: [
      "#e6f7ff", // 0: Lightest
      "#bae7ff", // 1
      "#91d5ff", // 2
      "#69c0ff", // 3
      "#40a9ff", // 4
      "#1890ff", // 5: Base
      "#096dd9", // 6
      "#0050b3", // 7
      "#003a8c", // 8
      "#002766", // 9: Darkest
    ],

    // Secondary color - green shade
    secondary: [
      "#e6fffb", // 0: Lightest
      "#b5f5ec", // 1
      "#87e8de", // 2
      "#5cdbd3", // 3
      "#36cfc9", // 4
      "#13c2c2", // 5: Base
      "#08979c", // 6
      "#006d75", // 7
      "#00474f", // 8
      "#002329", // 9: Darkest
    ],

    // Accent color - purple shade
    accent: [
      "#f9f0ff", // 0: Lightest
      "#efdbff", // 1
      "#d3adf7", // 2
      "#b37feb", // 3
      "#9254de", // 4
      "#722ed1", // 5: Base
      "#531dab", // 6
      "#391085", // 7
      "#22075e", // 8
      "#120338", // 9: Darkest
    ],

    // Gray shades for text, backgrounds, etc.
    gray: [
      "#fafafa", // 0: Lightest
      "#f5f5f5", // 1
      "#e8e8e8", // 2
      "#d9d9d9", // 3
      "#bfbfbf", // 4
      "#8c8c8c", // 5
      "#595959", // 6
      "#434343", // 7
      "#262626", // 8
      "#141414", // 9: Darkest
    ],

    // Keep the original primary_color for backward compatibility
    primary_color: [
      "#e6f7ff", // 0: Lightest
      "#bae7ff", // 1
      "#91d5ff", // 2
      "#69c0ff", // 3
      "#40a9ff", // 4
      "#1890ff", // 5: Base
      "#096dd9", // 6
      "#0050b3", // 7
      "#003a8c", // 8
      "#002766", // 9: Darkest
    ],
  },

  // Set primary color key
  primaryColor: "primary",

  // Typography settings
  fontFamily:
    "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif",
  fontFamilyMonospace: "Monaco, Courier, monospace",

  // Heading styles
  headings: {
    fontFamily:
      "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif",
    fontWeight: "600",
    sizes: {
      h1: { fontSize: "2.5rem", lineHeight: "1.2" },
      h2: { fontSize: "2rem", lineHeight: "1.3" },
      h3: { fontSize: "1.5rem", lineHeight: "1.4" },
      h4: { fontSize: "1.25rem", lineHeight: "1.4" },
      h5: { fontSize: "1rem", lineHeight: "1.5" },
      h6: { fontSize: "0.875rem", lineHeight: "1.5" },
    },
  },

  // Other design tokens
  spacing: {
    xs: "0.5rem",
    sm: "0.75rem",
    md: "1rem",
    lg: "1.5rem",
    xl: "2rem",
  },

  // Shadows
  shadows: {
    xs: "0 1px 2px 0 rgba(0, 0, 0, 0.05)",
    sm: "0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)",
    md: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
    lg: "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
    xl: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
  },

  // Component-specific overrides
  components: {
    Button: {
      defaultProps: {
        radius: "md",
      },
      styles: {
        root: {
          transition: "all 0.2s ease",
        },
      },
    },
    Card: {
      defaultProps: {
        radius: "md",
        shadow: "sm",
      },
    },
    Input: {
      defaultProps: {
        radius: "md",
      },
    },
    ActionIcon: {
      styles: {
        root: {
          transition: "all 0.2s ease",
        },
      },
    },
  },

  // Default gradient
  defaultGradient: {
    from: "primary",
    to: "accent",
    deg: 45,
  },
});
