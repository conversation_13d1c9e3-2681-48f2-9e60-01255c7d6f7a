import express from "express";
import {
  getDashboardStats,
  getDashboardActivities,
} from "../controllers/employer-dashboard.controller";
import { protect, restrictTo } from "../middleware/auth.middleware";

const router = express.Router();

// Enable authentication for production
router.use(protect);
router.use(restrictTo("employer", "admin"));

// Dashboard routes
router.get("/stats", getDashboardStats);
router.get("/activities", getDashboardActivities);

// Health check route
router.get("/health", (_req, res) => {
  res
    .status(200)
    .json({ success: true, message: "Employer dashboard routes are working" });
});

export default router;
