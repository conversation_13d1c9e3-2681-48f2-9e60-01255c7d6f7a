"use client";

import { getCandidateById } from "@/data/job-applications";
import { useSelectedCandidateStore } from "@/stores/employer-store";
import { useCandidateDetailsModal } from "@/stores/modal-store";
import {
  Badge,
  Button,
  Divider,
  Group,
  Modal,
  Select,
  Stack,
  Text,
} from "@mantine/core";
import { useEffect, useState } from "react";
import {
  FaBriefcase,
  FaCalendar,
  FaDownload,
  FaEnvelope,
  FaGraduationCap,
  FaPhone,
  FaQuestionCircle,
  FaStar,
  FaUser,
} from "react-icons/fa";
import { Link } from "react-router";
import EmployerMainCard from "../EmployerMainCard";

// Default candidate data (will be replaced with actual data)
const defaultCandidate = {
  id: 1,
  name: "<PERSON>",
  email: "<EMAIL>",
  phone: "************",
  status: "Applied",
  jobId: 1,
  appliedDate: "2023-10-01",
  resumeUrl: "/resumes/john-doe-resume.pdf",
  coverLetter: "I am excited to apply for the Software Engineer position...",
  skills: ["JavaScript", "React", "Node.js", "TypeScript"],
  experience: [
    {
      company: "Tech Corp",
      role: "Frontend Developer",
      duration: "2020 - Present",
    },
    {
      company: "Innovate Solutions",
      role: "Junior Developer",
      duration: "2018 - 2020",
    },
  ],
  education: [
    {
      institution: "University of Tech",
      degree: "Bachelor of Science in Computer Science",
      duration: "2014 - 2018",
    },
  ],
  questions: [
    {
      question: "Why do you want to work for our company?",
      answer:
        "I admire your company's innovative approach to solving real-world problems.",
    },
    {
      question: "What are your strengths and weaknesses?",
      answer:
        "My strengths include problem-solving and teamwork. My weakness is perfectionism.",
    },
  ],
};

export default function CandidateDetailsModal() {
  const { close, isOpen } = useCandidateDetailsModal();

  const closeModal = () => {
    close();
  };

  // Get the candidate ID from the store
  const candidateId = useSelectedCandidateStore(
    (state) => state.selectedCandidateId,
  );

  // Use a more flexible type for candidate that can handle both the default and fetched data
  const [candidate, setCandidate] = useState<any>(defaultCandidate);
  const [status, setStatus] = useState(candidate.status);

  // Fetch candidate data when modal opens or candidateId changes
  useEffect(() => {
    if (isOpen) {
      const fetchedCandidate = getCandidateById(candidateId);
      if (fetchedCandidate) {
        setCandidate(fetchedCandidate);
        setStatus(fetchedCandidate.status);
      }
    }
  }, [candidateId, isOpen]);

  const handleStatusChange = (newStatus: string) => {
    setStatus(newStatus);
    // You can add logic here to update the status in your backend or state management
    console.log("Updated status:", newStatus);
  };

  return (
    <Modal
      opened={isOpen}
      onClose={closeModal}
      title="Candidate Details"
      size="xl"
      padding="xl"
    >
      {/* Candidate Information */}
      <Stack gap="lg">
        {/* Name and Status */}
        <Group justify="space-between" align="center">
          <Text size="xl" fw={600}>
            {candidate.name}
          </Text>
          <Badge
            color={
              status === "Applied"
                ? "blue"
                : status === "Shortlisted"
                  ? "green"
                  : status === "Interviewed"
                    ? "yellow"
                    : status === "Rejected"
                      ? "red"
                      : status === "Hired"
                        ? "green"
                        : "purple"
            }
            size="lg"
          >
            {status}
          </Badge>
        </Group>
        {/* Status Control */}
        <EmployerMainCard>
          <Text size="lg" fw={600} mb="sm">
            Update Status
          </Text>
          <Select
            value={status}
            onChange={(value) => handleStatusChange(value || "Applied")}
            data={[
              { value: "Applied", label: "Applied" },
              { value: "Shortlisted", label: "Shortlisted" },
              { value: "Interviewed", label: "Interviewed" },
              { value: "Rejected", label: "Rejected" },
              { value: "Hired", label: "Hired" },
            ]}
            placeholder="Select status"
          />
        </EmployerMainCard>
        {/* Contact Information */}
        <EmployerMainCard>
          <Stack gap="sm">
            <Group>
              <FaEnvelope size={16} color="#4A90E2" /> {/* Blue */}
              <Text>{candidate.email}</Text>
            </Group>
            {candidate.phone && (
              <Group>
                <FaPhone size={16} color="#50E3C2" /> {/* Teal */}
                <Text>{candidate.phone}</Text>
              </Group>
            )}
            <Group>
              <FaUser size={16} color="#F5A623" /> {/* Orange */}
              <Text>
                Applied for:{" "}
                <strong>{(candidate as any).jobTitle || "Job Position"}</strong>
              </Text>
            </Group>
            <Group>
              <FaCalendar size={16} color="#BD10E0" /> {/* Purple */}
              <Text>Applied on: {candidate.appliedDate}</Text>
            </Group>
          </Stack>
        </EmployerMainCard>
        {/* Skills */}
        {candidate.skills && candidate.skills.length > 0 && (
          <EmployerMainCard>
            <Text size="lg" fw={600} mb="sm">
              <FaStar size={18} color="#FFD700" className="mr-2 inline-block" />{" "}
              {/* Gold */}
              Skills
            </Text>
            <Group gap="sm">
              {candidate.skills.map((skill: string, index: number) => (
                <Badge key={index} color="blue" variant="light">
                  {skill}
                </Badge>
              ))}
            </Group>
          </EmployerMainCard>
        )}
        {/* Experience */}
        {candidate.experience && candidate.experience.length > 0 && (
          <EmployerMainCard>
            <Text size="lg" fw={600} mb="sm">
              <FaBriefcase
                size={18}
                color="#7ED321"
                className="mr-2 inline-block"
              />{" "}
              {/* Green */}
              Experience
            </Text>
            <Stack gap="sm">
              {candidate.experience.map((exp: any, index: number) => (
                <div key={index}>
                  <Text fw={500}>{exp.role}</Text>
                  <Text size="sm" c="dimmed">
                    {exp.company} • {exp.duration}
                  </Text>
                  {/* Add a Divider between items, except after the last one */}
                  {index < candidate.experience.length - 1 && (
                    <Divider my="sm" />
                  )}
                </div>
              ))}
            </Stack>
          </EmployerMainCard>
        )}
        {/* Education */}
        {candidate.education && candidate.education.length > 0 && (
          <EmployerMainCard>
            <Text size="lg" fw={600} mb="sm">
              <FaGraduationCap
                size={18}
                color="#9013FE"
                className="mr-2 inline-block"
              />{" "}
              {/* Purple */}
              Education
            </Text>
            <Stack gap="sm">
              {candidate.education.map((edu: any, index: number) => (
                <div key={index}>
                  <Text fw={500}>{edu.degree}</Text>
                  <Text size="sm" c="dimmed">
                    {edu.institution} • {edu.duration}
                  </Text>
                  {/* Add a Divider between items, except after the last one */}
                  {index < candidate.education.length - 1 && (
                    <Divider my="sm" />
                  )}
                </div>
              ))}
            </Stack>
          </EmployerMainCard>
        )}
        {/* Questions and Answers */}
        {candidate.questions && candidate.questions.length > 0 && (
          <EmployerMainCard>
            <Text size="lg" fw={600} mb="sm">
              <FaQuestionCircle
                size={18}
                color="#4A90E2"
                className="mr-2 inline-block"
              />{" "}
              Application Questions
            </Text>
            <Stack gap="sm">
              {candidate.questions.map((qa: any, index: number) => (
                <div key={index}>
                  <Text fw={500}>{qa.question}</Text>
                  <Text size="sm" c="dimmed">
                    {qa.answer}
                  </Text>
                  {/* Add a Divider between items, except after the last one */}
                  {index < candidate.questions.length - 1 && (
                    <Divider my="sm" />
                  )}
                </div>
              ))}
            </Stack>
          </EmployerMainCard>
        )}
        {/* Cover Letter */}
        {/* {candidate.coverLetter && (
          <EmployerMainCard>
            <Text size="lg" fw={600} mb="sm">
              <FaStickyNote
                size={18}
                color="#D0021B"
                className="mr-2 inline-block"
              />{" "}
              Cover Letter
            </Text>
            <Text>{candidate.coverLetter}</Text>
          </EmployerMainCard>
        )} */}
        {/* Notes */}
        {/* {candidate.notes && (
          <EmployerMainCard>
            <Text size="lg" fw={600} mb="sm">
              <FaStickyNote
                size={18}
                color="#D0021B"
                className="mr-2 inline-block"
              />{" "}
              Notes
            </Text>
            <Text>{candidate.notes}</Text>
          </EmployerMainCard>
        )} */}
        {/* Resume Download */}
        {candidate.resumeUrl && (
          <EmployerMainCard>
            <div className="flex items-center justify-between">
              <Text size="lg" fw={600} mb="sm">
                <FaDownload
                  size={18}
                  color="#4A90E2"
                  className="mr-2 inline-block"
                />
                Resume
              </Text>
              <Button
                leftSection={<FaDownload size={16} color="#4A90E2" />}
                component={Link}
                to={candidate.resumeUrl}
                download
                variant="outline"
              >
                Download Resume
              </Button>
            </div>
          </EmployerMainCard>
        )}
        {/* Profile Navigation Button */}
        <EmployerMainCard>
          <Button
            component={Link}
            to={`/employer/candidates/${candidate.id}`} // Replace with the actual profile URL
            variant="outline"
            target="_blank"
            leftSection={<FaUser size={16} />}
          >
            View Full Profile
          </Button>
        </EmployerMainCard>
      </Stack>
    </Modal>
  );
}
