import { Request, Response, NextFunction } from "express";
import { catchAsync } from "../utils/catch-async";
import { AppError } from "../utils/app-error";
import Job from "../models/job.model";
import CandidateProfile from "../models/candidate-profile.model";
import Application from "../models/application.model";

// Get recommended jobs for the current candidate
export const getRecommendedJobs = catchAsync(
  async (req: Request, res: Response, _next: NextFunction) => {
    // Get the candidate's profile
    const candidateProfile = await CandidateProfile.findOne({
      user: req.user._id,
    });

    // Get jobs the candidate has already applied to
    const appliedJobs = await Application.find({
      candidate: req.user._id,
    }).select("job");

    const appliedJobIds = appliedJobs.map((application) => application.job);

    // Base query: active jobs that the candidate hasn't applied to
    const baseQuery = {
      isActive: true,
      _id: { $nin: appliedJobIds },
    };

    let recommendedJobs: any[] = [];
    let scoredJobs: { job: any; score: number }[] = [];

    // If candidate has a profile with skills, use them for recommendations
    if (candidateProfile) {
      // Get all active jobs that the candidate hasn't applied to
      const allEligibleJobs = await Job.find(baseQuery)
        .populate({
          path: "company",
          select: "name logo location",
        })
        .sort({ createdAt: -1 })
        .limit(100); // Limit to 100 jobs for performance

      // Score each job based on various factors
      scoredJobs = allEligibleJobs.map((job) => {
        let score = 0;

        // Skills match (highest weight)
        if (candidateProfile.skills && candidateProfile.skills.length > 0) {
          const jobTags = job.tags || [];
          const matchingSkills = jobTags.filter((tag: string) =>
            candidateProfile.skills.includes(tag)
          );

          // Calculate skill match percentage and add to score
          if (jobTags.length > 0) {
            const skillMatchPercentage =
              (matchingSkills.length / jobTags.length) * 100;
            score += skillMatchPercentage * 0.5; // 50% weight for skills
          }
        }

        // Location match
        if (
          candidateProfile.preferredLocations &&
          candidateProfile.preferredLocations.length > 0 &&
          job.location
        ) {
          if (candidateProfile.preferredLocations.includes(job.location)) {
            score += 20; // 20 points for location match
          }
        }

        // Job type match
        if (
          candidateProfile.preferredJobTypes &&
          candidateProfile.preferredJobTypes.length > 0 &&
          job.jobType
        ) {
          if (candidateProfile.preferredJobTypes.includes(job.jobType)) {
            score += 15; // 15 points for job type match
          }
        }

        // Salary match
        if (
          candidateProfile.preferredSalary &&
          (candidateProfile.preferredSalary.min ||
            candidateProfile.preferredSalary.max) &&
          (job.minSalary || job.maxSalary)
        ) {
          // Calculate average job salary
          let jobSalary = 0;
          if (job.minSalary && job.maxSalary) {
            jobSalary = (job.minSalary + job.maxSalary) / 2;
          } else if (job.minSalary) {
            jobSalary = job.minSalary;
          } else if (job.maxSalary) {
            jobSalary = job.maxSalary;
          }

          const preferredMin = candidateProfile.preferredSalary.min || 0;
          const preferredMax = candidateProfile.preferredSalary.max || Infinity;

          if (jobSalary >= preferredMin && jobSalary <= preferredMax) {
            score += 10; // 10 points for salary match
          }
        }

        // Recency bonus (newer jobs get higher scores)
        const jobAge =
          (new Date().getTime() - new Date(job.createdAt).getTime()) /
          (1000 * 60 * 60 * 24); // age in days
        score += Math.max(0, 10 - jobAge); // Up to 10 points for very recent jobs

        return { job, score };
      });

      // Sort jobs by score (descending)
      scoredJobs.sort((a, b) => b.score - a.score);

      // Take the top 10 jobs
      recommendedJobs = scoredJobs.slice(0, 10).map((item) => item.job);
    }

    // If we still don't have enough recommendations, add recent jobs
    if (recommendedJobs.length < 10) {
      const recentJobsQuery = {
        ...baseQuery,
        _id: {
          $nin: [...appliedJobIds, ...recommendedJobs.map((job) => job._id)],
        },
      };

      const recentJobs = await Job.find(recentJobsQuery)
        .populate({
          path: "company",
          select: "name logo",
        })
        .sort({ createdAt: -1 })
        .limit(10 - recommendedJobs.length);

      recommendedJobs = [...recommendedJobs, ...recentJobs];
    }

    // Add match score to the response
    const jobsWithScore = recommendedJobs.map((job) => {
      const scoredJob = scoredJobs.find(
        (sj) => sj.job._id.toString() === job._id.toString()
      );
      return {
        ...job.toObject(),
        matchScore: scoredJob ? Math.round(scoredJob.score) : 0,
      };
    });

    res.status(200).json({
      success: true,
      count: jobsWithScore.length,
      jobs: jobsWithScore,
    });
  }
);

// Get similar jobs to a specific job
export const getSimilarJobs = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    const { jobId } = req.params;

    // Find the job
    const job = await Job.findById(jobId);

    if (!job) {
      return next(new AppError("Job not found", 404));
    }

    // Get jobs the candidate has already applied to
    const appliedJobs = await Application.find({
      candidate: req.user._id,
    }).select("job");

    const appliedJobIds = appliedJobs.map((application) => application.job);

    // Get all active jobs that aren't the current job and haven't been applied to
    const baseQuery = {
      _id: { $ne: job._id, $nin: appliedJobIds },
      isActive: true,
    };

    // Get all eligible jobs
    const allEligibleJobs = await Job.find(baseQuery)
      .populate({
        path: "company",
        select: "name logo location",
      })
      .limit(50); // Limit for performance

    // Score each job based on similarity
    const scoredJobs = allEligibleJobs.map((otherJob) => {
      let similarityScore = 0;

      // Category match (highest weight)
      if (otherJob.category === job.category) {
        similarityScore += 30;
      }

      // Job type match
      if (otherJob.jobType === job.jobType) {
        similarityScore += 20;
      }

      // Location match
      if (otherJob.location === job.location) {
        similarityScore += 15;
      }

      // Tags/skills match
      const jobTags = job.tags || [];
      const otherJobTags = otherJob.tags || [];

      if (jobTags.length > 0 && otherJobTags.length > 0) {
        const matchingTags = jobTags.filter((tag) =>
          otherJobTags.includes(tag)
        );

        const tagMatchPercentage = (matchingTags.length / jobTags.length) * 100;
        similarityScore += tagMatchPercentage * 0.25; // 25% weight for tags
      }

      // Salary range similarity
      if (
        (job.minSalary || job.maxSalary) &&
        (otherJob.minSalary || otherJob.maxSalary)
      ) {
        // Calculate average salaries
        const jobAvgSalary =
          job.minSalary && job.maxSalary
            ? (job.minSalary + job.maxSalary) / 2
            : job.minSalary || job.maxSalary || 0;

        const otherJobAvgSalary =
          otherJob.minSalary && otherJob.maxSalary
            ? (otherJob.minSalary + otherJob.maxSalary) / 2
            : otherJob.minSalary || otherJob.maxSalary || 0;

        // Calculate salary similarity (within 20% range)
        if (jobAvgSalary > 0 && otherJobAvgSalary > 0) {
          const salaryDiffPercentage =
            (Math.abs(jobAvgSalary - otherJobAvgSalary) / jobAvgSalary) * 100;
          if (salaryDiffPercentage <= 20) {
            similarityScore += 10;
          }
        }
      }

      // Same company bonus (but lower priority)
      if (otherJob.company._id.toString() === job.company.toString()) {
        similarityScore += 5;
      }

      return { job: otherJob, score: similarityScore };
    });

    // Sort by similarity score (descending)
    scoredJobs.sort((a, b) => b.score - a.score);

    // Take the top 5 most similar jobs
    const similarJobs = scoredJobs.slice(0, 5).map((item) => {
      return {
        ...item.job.toObject(),
        similarityScore: Math.round(item.score),
      };
    });

    res.status(200).json({
      success: true,
      count: similarJobs.length,
      jobs: similarJobs,
    });
  }
);

// Get job application statistics for the candidate dashboard
export const getJobApplicationStats = catchAsync(
  async (req: Request, res: Response, _next: NextFunction) => {
    // Get all applications for the current user
    const applications = await Application.find({
      candidate: req.user._id,
    });

    // Count applications by status
    const statusCounts = {
      total: applications.length,
      pending: 0,
      reviewing: 0,
      shortlisted: 0,
      interview: 0,
      offered: 0,
      hired: 0,
      rejected: 0,
    };

    applications.forEach((application) => {
      statusCounts[application.status]++;
    });

    // Get recent applications
    const recentApplications = await Application.find({
      candidate: req.user._id,
    })
      .populate({
        path: "job",
        select: "title company category jobType location minSalary maxSalary",
        populate: {
          path: "company",
          select: "name logo",
        },
      })
      .sort({ createdAt: -1 })
      .limit(5);

    // Get application trends (applications per week for the last 8 weeks)
    const now = new Date();
    const eightWeeksAgo = new Date(now.getTime() - 8 * 7 * 24 * 60 * 60 * 1000);

    const applicationTrends = await Application.aggregate([
      {
        $match: {
          candidate: (req.user as any)._id,
          createdAt: { $gte: eightWeeksAgo },
        },
      },
      {
        $group: {
          _id: {
            year: { $year: "$createdAt" },
            week: { $week: "$createdAt" },
          },
          count: { $sum: 1 },
          date: { $first: "$createdAt" },
        },
      },
      {
        $sort: { date: 1 },
      },
      {
        $project: {
          _id: 0,
          year: "$_id.year",
          week: "$_id.week",
          count: 1,
          date: 1,
        },
      },
    ]);

    // Get application success rate
    const successRate =
      applications.length > 0
        ? ((statusCounts.shortlisted +
            statusCounts.interview +
            statusCounts.offered +
            statusCounts.hired) /
            applications.length) *
          100
        : 0;

    // Get top job categories applied to
    const jobCategories = await Application.aggregate([
      {
        $match: {
          candidate: (req.user as any)._id,
        },
      },
      {
        $lookup: {
          from: "jobs",
          localField: "job",
          foreignField: "_id",
          as: "jobDetails",
        },
      },
      {
        $unwind: "$jobDetails",
      },
      {
        $group: {
          _id: "$jobDetails.category",
          count: { $sum: 1 },
        },
      },
      {
        $sort: { count: -1 },
      },
      {
        $limit: 5,
      },
      {
        $project: {
          _id: 0,
          category: "$_id",
          count: 1,
        },
      },
    ]);

    res.status(200).json({
      success: true,
      stats: {
        statusCounts,
        recentApplications,
        applicationTrends,
        successRate: Math.round(successRate),
        topJobCategories: jobCategories,
      },
    });
  }
);
