"use client";

import { Anchor, Breadcrumbs as MantineBreadcrumbs, Text } from "@mantine/core";
import { useMemo } from "react";
import { Link, useLocation } from "react-router";

interface BreadcrumbsProps {
  items?: Array<{
    title: string;
    href?: string;
  }>;
  showHome?: boolean;
}

export default function Breadcrumbs({
  items,
  showHome = true,
}: BreadcrumbsProps) {
  const { pathname } = useLocation();

  const breadcrumbItems = useMemo(() => {
    // If items are provided, use them
    if (items) {
      return items;
    }

    // Otherwise, generate from pathname
    const pathSegments = pathname.split("/").filter(Boolean);
    const generatedItems = [];

    if (showHome) {
      generatedItems.push({
        title: "Home",
        href: "/",
      });
    }

    let currentPath = "";
    pathSegments.forEach((segment, index) => {
      currentPath += `/${segment}`;

      // Format the segment for display (capitalize, replace hyphens with spaces)
      const formattedSegment = segment
        .replace(/-/g, " ")
        .replace(/\b\w/g, (char) => char.toUpperCase());

      generatedItems.push({
        title: formattedSegment,
        href: index === pathSegments.length - 1 ? undefined : currentPath,
      });
    });

    return generatedItems;
  }, [items, pathname, showHome]);

  if (breadcrumbItems.length <= 1) {
    return null;
  }

  return (
    <MantineBreadcrumbs className="mb-4">
      {breadcrumbItems.map((item, index) => {
        const isLast = index === breadcrumbItems.length - 1;

        if (isLast || !item.href) {
          return (
            <Text key={index} size="sm" c="dimmed">
              {item.title}
            </Text>
          );
        }

        return (
          <Anchor
            key={index}
            component={Link}
            to={item.href}
            size="sm"
            className="text-primary-color"
          >
            {item.title}
          </Anchor>
        );
      })}
    </MantineBreadcrumbs>
  );
}
