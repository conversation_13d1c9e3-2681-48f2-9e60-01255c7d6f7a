import candidateApi from "@/services/candidate-api";
import type { Job } from "@/types";
import { useForm } from "@mantine/form";
import { notifications } from "@mantine/notifications";
import { useEffect, useState } from "react";
import useJobDetails from "./use-job-details";

interface ApplicationForm {
  fullName: string;
  email: string;
  phone: string;
  resume: File | null;
  coverLetter: string;
  questions: {
    [key: string]: string | string[];
  };
}

interface Question {
  id: string;
  question: string;
  type: "text" | "radio" | "checkbox";
  required: boolean;
  options?: string[];
}

interface UseJobApplicationReturn {
  jobDetails: Job | null;
  isLoading: boolean;
  isSubmitting: boolean;
  error: string | null;
  form: ReturnType<typeof useForm<ApplicationForm>>;
  handleSubmit: (values: ApplicationForm) => Promise<void>;
  questions: Question[];
}

export default function useJobApplication(
  jobId: string | undefined,
): UseJobApplicationReturn {
  const { jobDetails, isLoading, error } = useJobDetails(jobId);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [questions, setQuestions] = useState<Question[]>([]);

  // Initialize form
  const form = useForm<ApplicationForm>({
    initialValues: {
      fullName: "",
      email: "",
      phone: "",
      resume: null,
      coverLetter: "",
      questions: {},
    },
    validate: {
      fullName: (value) =>
        value.length < 3 ? "Name must be at least 3 characters" : null,
      email: (value) =>
        /^\S+@\S+$/.test(value) ? null : "Invalid email address",
      phone: (value) =>
        value.length < 10 ? "Phone number must be at least 10 digits" : null,
      resume: (value) => (!value ? "Resume is required" : null),
      questions: (value: { [key: string]: string | string[] }) => {
        for (const q of questions) {
          if (q.required && !value[q.id]) {
            return "All required questions must be answered";
          }
        }
        return null;
      },
    },
  });

  // Update questions when job details are loaded
  useEffect(() => {
    if (jobDetails && jobDetails.questions) {
      // Convert questions from the backend format to our format
      const formattedQuestions: Question[] = Array.isArray(jobDetails.questions)
        ? jobDetails.questions.map((q: any, index: number) => {
            // If questions is just an array of strings, convert to our format
            if (typeof q === "string") {
              return {
                id: `question_${index}`,
                question: q,
                type: "text",
                required: true,
              };
            }
            // If questions is already in the right format, use it
            return {
              id: q.id || `question_${index}`,
              question: q.question || q,
              type: q.type || "text",
              required: q.required !== undefined ? q.required : true,
              options: q.options || [],
            };
          })
        : [];

      setQuestions(formattedQuestions);
    }
  }, [jobDetails]);

  // Handle form submission
  const handleSubmit = async (values: ApplicationForm) => {
    if (!jobId) {
      notifications.show({
        title: "Error",
        message: "Job ID is required",
        color: "red",
      });
      return;
    }

    try {
      setIsSubmitting(true);

      // Upload resume file
      let resumeUrl = "";
      if (values.resume) {
        // In a real implementation, you would upload the file to a server
        // For now, we'll simulate a successful upload
        resumeUrl = "https://example.com/resume.pdf";

        // Simulate file upload delay
        await new Promise((resolve) => setTimeout(resolve, 1000));
      }

      // Prepare question answers
      const questionAnswers = Object.entries(values.questions).map(
        ([questionId, answer]) => {
          // Find the original question text
          const question = questions.find((q) => q.id === questionId);
          return {
            question: question?.question || "Unknown question",
            answer: Array.isArray(answer) ? answer.join(", ") : answer,
          };
        },
      );

      // Submit the application
      const applicationData = {
        job: jobId,
        resume: resumeUrl,
        coverLetter: values.coverLetter,
        questionAnswers,
      };

      console.log("Submitting application:", applicationData);

      const response =
        await candidateApi.applications.submitApplication(applicationData);
      console.log("Application response:", response);

      // Show success notification
      notifications.show({
        title: "Success",
        message: "Your application has been submitted successfully",
        color: "green",
      });

      // Reset form
      form.reset();
    } catch (error: any) {
      console.error("Error submitting application:", error);

      // Show error notification
      notifications.show({
        title: "Error",
        message: error.message || "Failed to submit application",
        color: "red",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    jobDetails,
    isLoading,
    isSubmitting,
    error,
    form,
    handleSubmit,
    questions,
  };
}
