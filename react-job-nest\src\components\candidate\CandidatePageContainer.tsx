import PageContainer, {
  type PageContainerProps,
} from "@/design-system/components/layout/PageContainer";

/**
 * @deprecated Use PageContainer from design-system/components with variant="candidate" instead
 */
export default function CandidatePageContainer({
  children,
  breadcrumbItems,
  showBreadcrumbs = true,
  className,
}: PageContainerProps) {
  return (
    <PageContainer
      breadcrumbItems={breadcrumbItems}
      showBreadcrumbs={showBreadcrumbs}
      className={className}
      variant="candidate"
    >
      {children}
    </PageContainer>
  );
}
