import { Badge, useMantineColorScheme } from "@mantine/core";

export default function About() {
  const { colorScheme } = useMantineColorScheme();
  const isDark = colorScheme === "dark";

  return (
    <section className="mb-16">
      <div className="container">
        <div className="mx-auto max-w-3xl text-center">
          <Badge color="primary_color" size="lg" radius="sm" className="mb-4">
            About Us
          </Badge>
          <h1
            className={`mb-6 text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl ${isDark ? "text-white" : ""}`}
          >
            Connecting <span className="text-primary-color">Talent</span> with{" "}
            <span className="text-primary-color">Opportunity</span>
          </h1>
          <p
            className={`m-auto mb-8 text-lg leading-relaxed ${isDark ? "text-gray-300" : "text-gray-600"}`}
          >
            JobNest is dedicated to connecting employers with top talent and
            helping job seekers find their dream jobs. Our platform is designed
            to simplify the hiring process, making it easier for employers to
            post job openings and for candidates to find the right
            opportunities.
          </p>
          <div className="flex flex-wrap justify-center gap-4">
            <div
              className={`flex items-center gap-2 rounded-full px-4 py-2 ${
                isDark ? "bg-blue-900/30" : "bg-blue-50"
              }`}
            >
              <span className="h-3 w-3 rounded-full bg-blue-500"></span>
              <span
                className={`text-sm font-medium ${isDark ? "text-blue-200" : ""}`}
              >
                Trusted by 2,500+ companies
              </span>
            </div>
            <div
              className={`flex items-center gap-2 rounded-full px-4 py-2 ${
                isDark ? "bg-green-900/30" : "bg-green-50"
              }`}
            >
              <span className="h-3 w-3 rounded-full bg-green-500"></span>
              <span
                className={`text-sm font-medium ${isDark ? "text-green-200" : ""}`}
              >
                10,000+ active users
              </span>
            </div>
            <div
              className={`flex items-center gap-2 rounded-full px-4 py-2 ${
                isDark ? "bg-purple-900/30" : "bg-purple-50"
              }`}
            >
              <span className="h-3 w-3 rounded-full bg-purple-500"></span>
              <span
                className={`text-sm font-medium ${isDark ? "text-purple-200" : ""}`}
              >
                5,000+ job postings
              </span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
