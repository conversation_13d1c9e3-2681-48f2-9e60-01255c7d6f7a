"use client";

import { PageContainer } from "@/design-system/components";
import { useThemeClasses } from "@/design-system/utils/theme-utils";
import employerApi from "@/services/employer-api";
import {
  ActionIcon,
  Avatar,
  Badge,
  Button,
  Card,
  Checkbox,
  Divider,
  Grid,
  Group,
  LoadingOverlay,
  Menu,
  Paper,
  Progress,
  SegmentedControl,
  Select,
  Stack,
  Tabs,
  Text,
  TextInput,
  Title,
  Tooltip,
} from "@mantine/core";
import { notifications } from "@mantine/notifications";
import { useEffect, useState } from "react";
import {
  FaBriefcase,
  FaBuilding,
  FaCheckCircle,
  FaDownload,
  FaEllipsisV,
  FaEnvelope,
  FaEye,
  FaFilter,
  FaHourglassHalf,
  FaListUl,
  FaMapMarkerAlt,
  FaSearch,
  FaThLarge,
  FaTimesCircle,
  FaUserCheck,
  FaUserTimes,
  FaUsers,
} from "react-icons/fa";
import { useNavigate, useParams } from "react-router";

// Define interfaces for the API data
interface JobApplication {
  _id: string;
  job:
    | string
    | {
        _id: string;
        title: string;
        company: string;
        location: string;
        jobType: string;
      };
  candidate: {
    _id: string;
    name: string;
    email: string;
  };
  resume: string;
  coverLetter?: string;
  status:
    | "pending"
    | "reviewing"
    | "shortlisted"
    | "interview"
    | "offered"
    | "hired"
    | "rejected";
  questionAnswers: {
    question: string;
    answer: string;
  }[];
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

interface Job {
  _id: string;
  title: string;
  company: {
    _id: string;
    name: string;
    logo?: string;
  };
  description: string;
  requirements: string;
  jobType: string;
  location: string;
  category: string;
  tags: string[];
  minSalary?: number;
  maxSalary?: number;
  currency?: string;
  showSalary: boolean;
  benefits: string[];
  applicationDeadline?: string;
  isActive: boolean;
  questions: string[];
  createdAt: string;
  updatedAt: string;
  requiredExperience: string;
}

export default function EmployerJobApplicationsPage() {
  const navigate = useNavigate();
  const params = useParams();
  const jobId = params.job_id;

  const [isLoading, setIsLoading] = useState(true);
  const [isError, setIsError] = useState<string | null>(null);
  const [job, setJob] = useState<Job | null>(null);
  const [applications, setApplications] = useState<JobApplication[]>([]);
  const [viewMode, setViewMode] = useState<"cards" | "table">("cards");
  const [selectedApplications, setSelectedApplications] = useState<string[]>(
    [],
  );
  const [filteredApplications, setFilteredApplications] = useState<
    JobApplication[]
  >([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<string | null>("all");
  const [isUpdating, setIsUpdating] = useState(false);

  // Fetch job and applications data
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      setIsError(null);

      try {
        // Fetch job details
        const jobResponse = await employerApi.jobs.getJobById(jobId as string);
        if (jobResponse.data?.job) {
          setJob(jobResponse.data.job);
        }

        // Fetch applications for this job
        const applicationsResponse =
          await employerApi.candidates.getJobApplications(jobId as string);
        if (applicationsResponse.data?.applications) {
          setApplications(applicationsResponse.data.applications);
        }
      } catch (err) {
        console.error("Error fetching data:", err);
        setIsError("Failed to load job applications. Please try again later.");
      } finally {
        setIsLoading(false);
      }
    };

    if (jobId) {
      fetchData();
    }
  }, [jobId]);

  // Calculate statistics
  const totalApplications = applications.length;
  const pendingApplications = applications.filter(
    (app) => app.status === "pending",
  ).length;
  const reviewedApplications = applications.filter(
    (app) => app.status === "reviewing",
  ).length;
  const acceptedApplications = applications.filter(
    (app) =>
      app.status === "shortlisted" ||
      app.status === "interview" ||
      app.status === "offered" ||
      app.status === "hired",
  ).length;
  const rejectedApplications = applications.filter(
    (app) => app.status === "rejected",
  ).length;

  // Apply filters to applications
  useEffect(() => {
    if (!applications.length) {
      setFilteredApplications([]);
      return;
    }

    let filtered = [...applications];

    // Filter by tab
    if (activeTab !== "all") {
      if (activeTab === "reviewed") {
        filtered = filtered.filter((app) => app.status === "reviewing");
      } else if (activeTab === "accepted") {
        filtered = filtered.filter(
          (app) =>
            app.status === "shortlisted" ||
            app.status === "interview" ||
            app.status === "offered" ||
            app.status === "hired",
        );
      } else {
        filtered = filtered.filter((app) => app.status === activeTab);
      }
    }

    // Apply search filter
    if (searchQuery) {
      filtered = filtered.filter((app) => {
        const candidateName = app.candidate?.name || "";
        const candidateEmail = app.candidate?.email || "";
        const jobTitle = typeof app.job === "object" ? app.job.title : "";

        return (
          candidateName.toLowerCase().includes(searchQuery.toLowerCase()) ||
          candidateEmail.toLowerCase().includes(searchQuery.toLowerCase()) ||
          jobTitle.toLowerCase().includes(searchQuery.toLowerCase())
        );
      });
    }

    // Apply status filter (if not already filtered by tab)
    if (statusFilter && activeTab === "all") {
      if (statusFilter === "reviewed") {
        filtered = filtered.filter((app) => app.status === "reviewing");
      } else if (statusFilter === "accepted") {
        filtered = filtered.filter(
          (app) =>
            app.status === "shortlisted" ||
            app.status === "interview" ||
            app.status === "offered" ||
            app.status === "hired",
        );
      } else {
        filtered = filtered.filter((app) => app.status === statusFilter);
      }
    }

    setFilteredApplications(filtered);
  }, [applications, searchQuery, statusFilter, activeTab]);

  const handleSelectApplication = (id: string) => {
    if (selectedApplications.includes(id)) {
      setSelectedApplications(
        selectedApplications.filter((appId) => appId !== id),
      );
    } else {
      setSelectedApplications([...selectedApplications, id]);
    }
  };

  const handleSelectAll = () => {
    if (selectedApplications.length === filteredApplications.length) {
      setSelectedApplications([]);
    } else {
      setSelectedApplications(filteredApplications.map((app) => app._id));
    }
  };

  const handleUpdateStatus = async (
    applicationId: string,
    newStatus: string,
  ) => {
    try {
      setIsUpdating(true);
      await employerApi.candidates.updateApplicationStatus(
        applicationId,
        newStatus,
      );

      // Update the local state
      setApplications(
        applications.map((app) =>
          app._id === applicationId
            ? { ...app, status: newStatus as any }
            : app,
        ),
      );

      notifications.show({
        title: "Status Updated",
        message: `Application status has been updated to ${newStatus}`,
        color: "green",
      });
    } catch (err) {
      console.error("Error updating application status:", err);
      notifications.show({
        title: "Update Failed",
        message: "Failed to update application status. Please try again.",
        color: "red",
      });
    } finally {
      setIsUpdating(false);
    }
  };

  const handleBulkAction = async (
    action: "shortlist" | "reject" | "download",
  ) => {
    if (selectedApplications.length === 0) return;

    if (action === "download") {
      // Handle resume downloads
      notifications.show({
        title: "Download Started",
        message: `Downloading ${selectedApplications.length} resumes`,
        color: "blue",
      });
      // Implement actual download functionality here
    } else {
      // Update status for multiple applications
      try {
        setIsUpdating(true);
        const newStatus = action === "shortlist" ? "shortlisted" : "rejected";

        // Process each application update
        for (const appId of selectedApplications) {
          await employerApi.candidates.updateApplicationStatus(
            appId,
            newStatus,
          );
        }

        // Update local state
        setApplications(
          applications.map((app) =>
            selectedApplications.includes(app._id)
              ? { ...app, status: newStatus as any }
              : app,
          ),
        );

        notifications.show({
          title: "Bulk Update Completed",
          message: `Updated ${selectedApplications.length} applications to ${newStatus}`,
          color: "green",
        });

        // Reset selection after action
        setSelectedApplications([]);
      } catch (err) {
        console.error("Error performing bulk action:", err);
        notifications.show({
          title: "Bulk Update Failed",
          message: "Failed to update some applications. Please try again.",
          color: "red",
        });
      } finally {
        setIsUpdating(false);
      }
    }
  };

  const handleViewCandidate = (candidateId: string) => {
    navigate(`/employer/candidates/${candidateId}`);
  };

  return (
    <PageContainer
      breadcrumbItems={[
        { title: "Home", href: "/" },
        { title: "Manage Jobs", href: "/employer/manage-jobs" },
        { title: job?.title || "Job Applications" },
      ]}
      variant="employer"
    >
      {/* Loading overlays */}
      <LoadingOverlay visible={isLoading || isUpdating} />

      {/* Error message */}
      {isError && (
        <Paper withBorder p="xl" radius="md" className="mb-6 text-center">
          <Title order={3} className="mb-2 text-red-500">
            Error
          </Title>
          <Text className="text-gray-700">{isError}</Text>
        </Paper>
      )}

      {/* Job Overview Section */}
      <Card withBorder radius="md" className="mb-8 overflow-hidden">
        <div className="relative">
          {/* Background gradient */}
          <div
            className={useThemeClasses(
              "absolute inset-0 bg-gradient-to-r from-blue-50 to-indigo-50",
              "absolute inset-0 bg-gradient-to-r from-dark-6 to-dark-5",
            )}
          ></div>

          <div className="relative p-6 md:p-8">
            <Grid>
              <Grid.Col span={{ base: 12, md: 8 }}>
                <Group align="flex-start" className="mb-4">
                  <Avatar
                    size={80}
                    radius="md"
                    color="blue"
                    className="border-4 border-white shadow-md"
                  >
                    {job?.company?.name
                      ? job.company.name.substring(0, 2).toUpperCase()
                      : "JN"}
                  </Avatar>

                  <div>
                    <Title order={1} className="text-2xl font-bold">
                      {job?.title}
                    </Title>

                    <Group gap="xs" className="mt-1">
                      <Group gap="xs">
                        <FaBuilding className="text-blue-500" size={14} />
                        <Text
                          size="sm"
                          className={useThemeClasses(
                            "text-gray-700",
                            "text-gray-300",
                          )}
                        >
                          {job?.company?.name || "Company"}
                        </Text>
                      </Group>

                      <Divider orientation="vertical" />

                      <Group gap="xs">
                        <FaMapMarkerAlt className="text-blue-500" size={14} />
                        <Text
                          size="sm"
                          className={useThemeClasses(
                            "text-gray-700",
                            "text-gray-300",
                          )}
                        >
                          {job?.location || "Location"}
                        </Text>
                      </Group>

                      <Divider orientation="vertical" />

                      <Group gap="xs">
                        <FaBriefcase className="text-blue-500" size={14} />
                        <Text
                          size="sm"
                          className={useThemeClasses(
                            "text-gray-700",
                            "text-gray-300",
                          )}
                        >
                          {job?.jobType || "Job Type"}
                        </Text>
                      </Group>
                    </Group>

                    <Group gap="md" className="mt-4">
                      <Badge color="blue" size="lg">
                        {job?.jobType || "Job Type"}
                      </Badge>
                      {job?.showSalary && job?.minSalary && job?.maxSalary && (
                        <Badge color="cyan" size="lg">
                          {job.currency || "$"}
                          {job.minSalary} - {job.currency || "$"}
                          {job.maxSalary}
                        </Badge>
                      )}
                      <Badge color="indigo" size="lg">
                        {job?.requiredExperience || "Experience"} Experience
                      </Badge>
                    </Group>
                  </div>
                </Group>
              </Grid.Col>

              <Grid.Col span={{ base: 12, md: 4 }}>
                <Card
                  withBorder
                  radius="md"
                  className={useThemeClasses(
                    "bg-white shadow-sm",
                    "bg-dark-6 shadow-dark-lg",
                  )}
                >
                  <Title order={4} className="mb-3 text-center">
                    Applications Summary
                  </Title>

                  <Group justify="space-between" className="mb-2">
                    <Text size="sm">Total Applications</Text>
                    <Badge size="lg" color="blue">
                      {totalApplications}
                    </Badge>
                  </Group>

                  {/* Using multiple progress bars instead of sections */}
                  <div className="mb-3">
                    <Progress
                      value={(pendingApplications / totalApplications) * 100}
                      color="yellow"
                      size="xl"
                      className="mb-1"
                    />
                    <Progress
                      value={(reviewedApplications / totalApplications) * 100}
                      color="blue"
                      size="xl"
                      className="mb-1"
                    />
                    <Progress
                      value={(acceptedApplications / totalApplications) * 100}
                      color="green"
                      size="xl"
                      className="mb-1"
                    />
                    <Progress
                      value={(rejectedApplications / totalApplications) * 100}
                      color="red"
                      size="xl"
                    />
                  </div>

                  <Group justify="space-between" className="text-sm">
                    <Group gap="xs">
                      <div className="h-3 w-3 rounded-full bg-yellow-500"></div>
                      <Text size="xs">Pending ({pendingApplications})</Text>
                    </Group>

                    <Group gap="xs">
                      <div className="h-3 w-3 rounded-full bg-blue-500"></div>
                      <Text size="xs">Reviewed ({reviewedApplications})</Text>
                    </Group>

                    <Group gap="xs">
                      <div className="h-3 w-3 rounded-full bg-green-500"></div>
                      <Text size="xs">Accepted ({acceptedApplications})</Text>
                    </Group>

                    <Group gap="xs">
                      <div className="h-3 w-3 rounded-full bg-red-500"></div>
                      <Text size="xs">Rejected ({rejectedApplications})</Text>
                    </Group>
                  </Group>
                </Card>
              </Grid.Col>
            </Grid>
          </div>
        </div>
      </Card>

      {/* Tabs and Controls */}
      <div className="mb-6">
        <Grid>
          <Grid.Col span={{ base: 12, md: 8 }}>
            <Tabs value={activeTab} onChange={setActiveTab}>
              <Tabs.List>
                <Tabs.Tab value="all" leftSection={<FaUsers size={16} />}>
                  All Applications ({totalApplications})
                </Tabs.Tab>
                <Tabs.Tab
                  value="pending"
                  leftSection={<FaHourglassHalf size={16} />}
                >
                  Pending ({pendingApplications})
                </Tabs.Tab>
                <Tabs.Tab value="reviewed" leftSection={<FaEye size={16} />}>
                  Reviewed ({reviewedApplications})
                </Tabs.Tab>
                <Tabs.Tab
                  value="accepted"
                  leftSection={<FaCheckCircle size={16} />}
                >
                  Accepted ({acceptedApplications})
                </Tabs.Tab>
                <Tabs.Tab
                  value="rejected"
                  leftSection={<FaTimesCircle size={16} />}
                >
                  Rejected ({rejectedApplications})
                </Tabs.Tab>
              </Tabs.List>
            </Tabs>
          </Grid.Col>

          <Grid.Col
            span={{ base: 12, md: 4 }}
            className="flex items-center justify-end"
          >
            <Group>
              <SegmentedControl
                value={viewMode}
                onChange={(value) => setViewMode(value as "cards" | "table")}
                data={[
                  {
                    value: "cards",
                    label: (
                      <div className="flex items-center gap-2">
                        <FaThLarge size={16} />
                        <span>Cards</span>
                      </div>
                    ),
                  },
                  {
                    value: "table",
                    label: (
                      <div className="flex items-center gap-2">
                        <FaListUl size={16} />
                        <span>Table</span>
                      </div>
                    ),
                  },
                ]}
              />
            </Group>
          </Grid.Col>
        </Grid>
      </div>

      {/* Search and Filter Bar */}
      <Paper withBorder p="md" radius="md" className="mb-6">
        <Grid>
          <Grid.Col span={{ base: 12, md: 6 }}>
            <TextInput
              placeholder="Search by candidate name, email or job title"
              leftSection={<FaSearch size={16} className="text-gray-500" />}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </Grid.Col>

          <Grid.Col span={{ base: 12, md: 3 }}>
            <Select
              placeholder="Filter by status"
              data={[
                { value: "pending", label: "Pending" },
                { value: "reviewed", label: "Reviewed" },
                { value: "accepted", label: "Accepted" },
                { value: "rejected", label: "Rejected" },
              ]}
              leftSection={<FaFilter size={16} className="text-gray-500" />}
              value={statusFilter}
              onChange={setStatusFilter}
              clearable
              disabled={activeTab !== "all"}
            />
          </Grid.Col>

          <Grid.Col span={{ base: 12, md: 3 }}>
            <Group justify="flex-end">
              {selectedApplications.length > 0 && (
                <Menu position="bottom-end" shadow="md">
                  <Menu.Target>
                    <div className="cursor-pointer">
                      <Button variant="light">
                        Bulk Actions ({selectedApplications.length})
                      </Button>
                    </div>
                  </Menu.Target>
                  <Menu.Dropdown>
                    <Menu.Item
                      leftSection={<FaUserCheck size={14} />}
                      onClick={() => handleBulkAction("shortlist")}
                    >
                      Shortlist Selected
                    </Menu.Item>
                    <Menu.Item
                      leftSection={<FaUserTimes size={14} />}
                      onClick={() => handleBulkAction("reject")}
                      color="red"
                    >
                      Reject Selected
                    </Menu.Item>
                    <Menu.Divider />
                    <Menu.Item
                      leftSection={<FaDownload size={14} />}
                      onClick={() => handleBulkAction("download")}
                    >
                      Download Resumes
                    </Menu.Item>
                  </Menu.Dropdown>
                </Menu>
              )}
            </Group>
          </Grid.Col>
        </Grid>
      </Paper>

      {/* Applications List */}
      {filteredApplications.length === 0 ? (
        <Paper withBorder p="xl" radius="md" className="text-center">
          <Title order={3} className="mb-2 text-gray-700">
            No applications found
          </Title>
          <Text className="text-gray-500">
            Try adjusting your search or filter criteria
          </Text>
        </Paper>
      ) : viewMode === "cards" ? (
        <div className="mb-6">
          <Grid>
            {filteredApplications.map((application) => (
              <Grid.Col key={application._id} span={{ base: 12, sm: 6, lg: 4 }}>
                <Card
                  withBorder
                  shadow="sm"
                  radius="md"
                  padding="md"
                  className="h-full"
                >
                  <Card.Section className="relative bg-gradient-to-r from-blue-50 to-indigo-50 p-3">
                    <Checkbox
                      checked={selectedApplications.includes(application._id)}
                      onChange={() => handleSelectApplication(application._id)}
                      className="absolute top-3 right-3 z-10"
                    />

                    <Group justify="space-between" className="mb-2">
                      <Group>
                        <Avatar radius="xl" color="blue">
                          {application.candidate.name
                            .substring(0, 2)
                            .toUpperCase()}
                        </Avatar>
                        <div>
                          <Text fw={600}>{application.candidate.name}</Text>
                          <Text size="xs" c="dimmed">
                            {application.candidate.email}
                          </Text>
                        </div>
                      </Group>
                    </Group>
                  </Card.Section>

                  <Stack gap="xs" mt="md">
                    <Group justify="space-between">
                      <Text size="sm" c="dimmed">
                        Application Status
                      </Text>
                      <Badge
                        color={
                          application.status === "pending"
                            ? "yellow"
                            : application.status === "reviewing"
                              ? "blue"
                              : application.status === "rejected"
                                ? "red"
                                : "green"
                        }
                      >
                        {application.status.charAt(0).toUpperCase() +
                          application.status.slice(1)}
                      </Badge>
                    </Group>

                    <Group justify="space-between">
                      <Text size="sm" c="dimmed">
                        Applied Date
                      </Text>
                      <Text size="sm">
                        {new Date(application.createdAt).toLocaleDateString()}
                      </Text>
                    </Group>

                    {application.questionAnswers &&
                      application.questionAnswers.length > 0 && (
                        <div className="mt-3 mb-5">
                          <Text size="sm" c="dimmed" mb={5}>
                            Questions
                          </Text>
                          <Group>
                            <Badge color="gray" variant="light" size="sm">
                              {application.questionAnswers.length} answered
                            </Badge>
                          </Group>
                        </div>
                      )}
                  </Stack>

                  <Group justify="space-between" mt="md" className="!mt-auto">
                    <Button
                      variant="light"
                      color="blue"
                      leftSection={<FaEye size={14} />}
                      onClick={() =>
                        handleViewCandidate(application.candidate._id)
                      }
                      fullWidth
                    >
                      View Details
                    </Button>
                  </Group>
                </Card>
              </Grid.Col>
            ))}
          </Grid>
        </div>
      ) : (
        <Paper withBorder radius="md" className="mb-6 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="p-3 text-left">
                    <Checkbox
                      checked={
                        selectedApplications.length ===
                          filteredApplications.length &&
                        filteredApplications.length > 0
                      }
                      indeterminate={
                        selectedApplications.length > 0 &&
                        selectedApplications.length <
                          filteredApplications.length
                      }
                      onChange={handleSelectAll}
                    />
                  </th>
                  <th className="p-3 text-left">Candidate</th>
                  <th className="p-3 text-left">Applied Date</th>
                  <th className="p-3 text-left">Resume</th>
                  <th className="p-3 text-left">Status</th>
                  <th className="p-3 text-left">Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredApplications.map((application) => (
                  <tr
                    key={application._id}
                    className="border-t border-gray-200 hover:bg-gray-50"
                  >
                    <td className="p-3">
                      <Checkbox
                        checked={selectedApplications.includes(application._id)}
                        onChange={() =>
                          handleSelectApplication(application._id)
                        }
                      />
                    </td>
                    <td className="p-3">
                      <Group>
                        <Avatar radius="xl" color="blue" size="sm">
                          {application.candidate.name
                            .substring(0, 2)
                            .toUpperCase()}
                        </Avatar>
                        <div>
                          <Text size="sm" fw={500}>
                            {application.candidate.name}
                          </Text>
                          <Text size="xs" c="dimmed">
                            {application.candidate.email}
                          </Text>
                        </div>
                      </Group>
                    </td>
                    <td className="p-3">
                      <Text size="sm">
                        {new Date(application.createdAt).toLocaleDateString()}
                      </Text>
                    </td>
                    <td className="p-3">
                      <Button
                        size="xs"
                        variant="light"
                        component="a"
                        href={application.resume}
                        target="_blank"
                        leftSection={<FaDownload size={12} />}
                      >
                        View Resume
                      </Button>
                    </td>
                    <td className="p-3">
                      <Badge
                        color={
                          application.status === "pending"
                            ? "yellow"
                            : application.status === "reviewing"
                              ? "blue"
                              : application.status === "rejected"
                                ? "red"
                                : "green"
                        }
                      >
                        {application.status.charAt(0).toUpperCase() +
                          application.status.slice(1)}
                      </Badge>
                    </td>
                    <td className="p-3">
                      <Group>
                        <Tooltip label="View Details">
                          <ActionIcon
                            color="blue"
                            variant="light"
                            onClick={() =>
                              handleViewCandidate(application.candidate._id)
                            }
                          >
                            <FaEye size={16} />
                          </ActionIcon>
                        </Tooltip>

                        <Menu position="bottom-end" shadow="md">
                          <Menu.Target>
                            <ActionIcon variant="subtle">
                              <FaEllipsisV size={16} />
                            </ActionIcon>
                          </Menu.Target>
                          <Menu.Dropdown>
                            <Menu.Item
                              leftSection={<FaUserCheck size={14} />}
                              onClick={() =>
                                handleUpdateStatus(
                                  application._id,
                                  "shortlisted",
                                )
                              }
                            >
                              Shortlist
                            </Menu.Item>
                            <Menu.Item
                              leftSection={<FaUserTimes size={14} />}
                              color="red"
                              onClick={() =>
                                handleUpdateStatus(application._id, "rejected")
                              }
                            >
                              Reject
                            </Menu.Item>
                            <Menu.Divider />
                            <Menu.Item
                              leftSection={<FaDownload size={14} />}
                              component="a"
                              href={application.resume}
                              target="_blank"
                            >
                              Download Resume
                            </Menu.Item>
                            <Menu.Item
                              leftSection={<FaEnvelope size={14} />}
                              component="a"
                              href={`mailto:${application.candidate.email}`}
                            >
                              Contact Candidate
                            </Menu.Item>
                          </Menu.Dropdown>
                        </Menu>
                      </Group>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </Paper>
      )}
    </PageContainer>
  );
}
