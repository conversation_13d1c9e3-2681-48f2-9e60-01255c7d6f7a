"use client";

import {
  ActionIcon,
  Avatar,
  Badge,
  Checkbox,
  Group,
  Menu,
  Table,
  Text,
  useMantineColorScheme,
} from "@mantine/core";
import { useState } from "react";
import {
  FaBan,
  FaCheck,
  FaEdit,
  FaEllipsisV,
  FaEnvelope,
  FaEye,
  FaTrash,
} from "react-icons/fa";

// Mock data for users with avatar
const mockUsers = [
  {
    id: 1,
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "candidate",
    status: "active",
    createdAt: "2023-10-10",
    avatar: "/images/avatar-placeholder.png",
  },
  {
    id: 2,
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "employer",
    status: "active",
    createdAt: "2023-10-09",
    avatar: "/images/avatar-placeholder.png",
  },
  {
    id: 3,
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "candidate",
    status: "inactive",
    createdAt: "2023-10-08",
    avatar: "/images/avatar-placeholder.png",
  },
  {
    id: 4,
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "employer",
    status: "blocked",
    createdAt: "2023-10-07",
    avatar: "/images/avatar-placeholder.png",
  },
  {
    id: 5,
    name: "Admin User",
    email: "<EMAIL>",
    role: "admin",
    status: "active",
    createdAt: "2023-10-06",
    avatar: "/images/avatar-placeholder.png",
  },
];

interface UsersTableProps {
  onView: (userId: number) => void;
  onEdit: (userId: number) => void;
  onDelete: (userId: number) => void;
}

export default function UsersTable({
  onView,
  onEdit,
  onDelete,
}: UsersTableProps) {
  const [users] = useState(mockUsers);
  const [selectedUsers, setSelectedUsers] = useState<number[]>([]);
  const [selectAll, setSelectAll] = useState(false);
  const { colorScheme } = useMantineColorScheme();
  const isDark = colorScheme === "dark";

  const toggleSelectAll = () => {
    if (selectAll) {
      setSelectedUsers([]);
    } else {
      setSelectedUsers(users.map((user) => user.id));
    }
    setSelectAll(!selectAll);
  };

  const toggleSelectUser = (userId: number) => {
    if (selectedUsers.includes(userId)) {
      setSelectedUsers(selectedUsers.filter((id) => id !== userId));
      setSelectAll(false);
    } else {
      setSelectedUsers([...selectedUsers, userId]);
      if (selectedUsers.length + 1 === users.length) {
        setSelectAll(true);
      }
    }
  };

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case "admin":
        return "purple";
      case "employer":
        return "green";
      case "candidate":
        return "blue";
      default:
        return "gray";
    }
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case "active":
        return "green";
      case "inactive":
        return "yellow";
      case "blocked":
        return "red";
      default:
        return "gray";
    }
  };

  return (
    <div className="overflow-x-auto">
      <Table
        striped
        highlightOnHover
        styles={{
          table: {
            backgroundColor: isDark ? "var(--mantine-color-dark-7)" : undefined,
          },
          thead: {
            backgroundColor: isDark ? "var(--mantine-color-dark-6)" : undefined,
          },
          tr: {
            "&:hover": {
              backgroundColor: isDark
                ? "var(--mantine-color-dark-5) !important"
                : undefined,
            },
          },
        }}
      >
        <Table.Thead>
          <Table.Tr>
            <Table.Th style={{ width: 40 }}>
              <Checkbox
                checked={selectAll}
                onChange={toggleSelectAll}
                aria-label="Select all users"
              />
            </Table.Th>
            <Table.Th>User</Table.Th>
            <Table.Th className="hidden md:table-cell">Email</Table.Th>
            <Table.Th>Role</Table.Th>
            <Table.Th className="hidden sm:table-cell">Status</Table.Th>
            <Table.Th className="hidden lg:table-cell">
              Registration Date
            </Table.Th>
            <Table.Th>Actions</Table.Th>
          </Table.Tr>
        </Table.Thead>
        <Table.Tbody>
          {users.map((user) => (
            <Table.Tr key={user.id} className="group">
              <Table.Td>
                <Checkbox
                  checked={selectedUsers.includes(user.id)}
                  onChange={() => toggleSelectUser(user.id)}
                  aria-label={`Select ${user.name}`}
                />
              </Table.Td>
              <Table.Td>
                <Group gap="sm">
                  <Avatar src={user.avatar} radius="xl" size="sm" />
                  <div>
                    <Text size="sm" fw={500}>
                      {user.name}
                    </Text>
                    <Text size="xs" c="dimmed" className="md:hidden">
                      {user.email}
                    </Text>
                  </div>
                </Group>
              </Table.Td>
              <Table.Td className="hidden md:table-cell">{user.email}</Table.Td>
              <Table.Td>
                <Badge
                  color={getRoleBadgeColor(user.role)}
                  variant="light"
                  size="sm"
                >
                  {user.role}
                </Badge>
              </Table.Td>
              <Table.Td className="hidden sm:table-cell">
                <Badge
                  color={getStatusBadgeColor(user.status)}
                  variant="light"
                  size="sm"
                >
                  {user.status}
                </Badge>
              </Table.Td>
              <Table.Td className="hidden lg:table-cell">
                {user.createdAt}
              </Table.Td>
              <Table.Td>
                <Group gap="xs" className="hidden sm:flex">
                  <ActionIcon
                    variant="subtle"
                    color="blue"
                    onClick={() => onView(user.id)}
                    aria-label="View user details"
                  >
                    <FaEye size={16} />
                  </ActionIcon>
                  <ActionIcon
                    variant="subtle"
                    color="green"
                    onClick={() => onEdit(user.id)}
                    aria-label="Edit user"
                  >
                    <FaEdit size={16} />
                  </ActionIcon>
                  <ActionIcon
                    variant="subtle"
                    color="red"
                    onClick={() => onDelete(user.id)}
                    aria-label="Delete user"
                  >
                    <FaTrash size={16} />
                  </ActionIcon>
                </Group>

                {/* Mobile menu */}
                <div className="sm:hidden">
                  <Menu position="bottom-end" shadow="md" width={160}>
                    <Menu.Target>
                      <div className="cursor-pointer">
                        <ActionIcon variant="subtle">
                          <FaEllipsisV size={16} />
                        </ActionIcon>
                      </div>
                    </Menu.Target>
                    <Menu.Dropdown>
                      <Menu.Item
                        leftSection={<FaEye size={14} />}
                        onClick={() => onView(user.id)}
                      >
                        View Details
                      </Menu.Item>
                      <Menu.Item
                        leftSection={<FaEdit size={14} />}
                        onClick={() => onEdit(user.id)}
                      >
                        Edit User
                      </Menu.Item>
                      <Menu.Item leftSection={<FaEnvelope size={14} />}>
                        Send Email
                      </Menu.Item>
                      {user.status === "active" ? (
                        <Menu.Item
                          leftSection={<FaBan size={14} />}
                          color="orange"
                        >
                          Deactivate
                        </Menu.Item>
                      ) : user.status === "inactive" ? (
                        <Menu.Item
                          leftSection={<FaCheck size={14} />}
                          color="green"
                        >
                          Activate
                        </Menu.Item>
                      ) : null}
                      <Menu.Divider />
                      <Menu.Item
                        leftSection={<FaTrash size={14} />}
                        color="red"
                        onClick={() => onDelete(user.id)}
                      >
                        Delete
                      </Menu.Item>
                    </Menu.Dropdown>
                  </Menu>
                </div>
              </Table.Td>
            </Table.Tr>
          ))}
        </Table.Tbody>
      </Table>
    </div>
  );
}
