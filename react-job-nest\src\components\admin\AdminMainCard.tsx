import Card, { type CardProps } from "@/design-system/components/card/Card";
import { type PropsWithChildren } from "react";

/**
 * @deprecated Use Card from design-system/components with variant="admin" instead
 */
export default function AdminMainCard({
  children,
  className = "",
  withBorder = true,
  withShadow = true,
  padding = "md",
  radius = "lg",
  ...rest
}: PropsWithChildren<CardProps>) {
  return (
    <Card
      className={className}
      withBorder={withBorder}
      withShadow={withShadow}
      padding={padding}
      radius={radius}
      variant="admin"
      {...rest}
    >
      {children}
    </Card>
  );
}
