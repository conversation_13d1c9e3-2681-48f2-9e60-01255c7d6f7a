"use client";

import { type JobApplication } from "@/data/job-applications";
import { Text } from "@mantine/core";
import ApplicationCard from "./ApplicationCard";

type ApplicationsListProps = {
  applications: JobApplication[];
};

export default function ApplicationsList({
  applications,
}: ApplicationsListProps) {
  if (applications.length === 0) {
    return (
      <Text className="mt-8 text-center text-lg">No applications found.</Text>
    );
  }

  return (
    <div className="mt-6 grid grid-cols-1 gap-6">
      {applications.map((application) => (
        <ApplicationCard key={application.id} application={application} />
      ))}
    </div>
  );
}
