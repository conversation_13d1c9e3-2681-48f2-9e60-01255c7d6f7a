"use client";

import { type PropsWithChildren, type ReactNode } from "react";
import AdminMainHeading from "./AdminMainHeading";
import AdminPageContainer from "./AdminPageContainer";

interface AdminPageWithFiltersProps extends PropsWithChildren {
  title: string;
  subtitle?: string;
  filters: ReactNode;
  className?: string;
}

/**
 * Admin page layout with filters on the left side
 */
export default function AdminPageWithFilters({
  children,
  title,
  subtitle,
  filters,
  className,
}: AdminPageWithFiltersProps) {
  return (
    <AdminPageContainer className={className}>
      <AdminMainHeading title={title} subtitle={subtitle} />

      <div className="mt-6 flex flex-col lg:flex-row lg:gap-6">
        {/* Filters Section - Left Side */}
        <div className="mb-6 w-full lg:mb-0 lg:w-1/4 lg:max-w-[300px] lg:min-w-[250px]">
          {filters}
        </div>

        {/* Content Section - Right Side */}
        <div className="flex-1">{children}</div>
      </div>
    </AdminPageContainer>
  );
}
