import mongoose, { Document, Schema } from 'mongoose';

export interface ISubscriptionPlan extends Document {
  name: string;
  description: string;
  price: number;
  annualPrice: number;
  currency: string;
  billingPeriod: 'month' | 'year';
  features: {
    activeJobs: number | 'unlimited';
    jobVisibilityDays: number;
    cvViews: number | 'unlimited';
    featuredJobs: number;
    priorityPlacement: boolean;
    featuredCompanyProfile: boolean;
    candidateMatching: boolean;
    advancedAnalytics: boolean;
    dedicatedSupport: boolean;
    customFeatures: string[];
  };
  isActive: boolean;
  isPopular: boolean;
  sortOrder: number;
  createdAt: Date;
  updatedAt: Date;
}

const subscriptionPlanSchema = new Schema<ISubscriptionPlan>(
  {
    name: {
      type: String,
      required: [true, 'Plan name is required'],
      trim: true,
      unique: true,
    },
    description: {
      type: String,
      required: [true, 'Plan description is required'],
    },
    price: {
      type: Number,
      required: [true, 'Plan price is required'],
      min: [0, 'Price cannot be negative'],
    },
    annualPrice: {
      type: Number,
      required: [true, 'Annual price is required'],
      min: [0, 'Annual price cannot be negative'],
    },
    currency: {
      type: String,
      default: 'USD',
    },
    billingPeriod: {
      type: String,
      enum: {
        values: ['month', 'year'],
        message: 'Billing period must be either month or year',
      },
      default: 'month',
    },
    features: {
      activeJobs: {
        type: Schema.Types.Mixed,
        required: [true, 'Active jobs limit is required'],
        validate: {
          validator: function(value: any) {
            return value === 'unlimited' || (typeof value === 'number' && value > 0);
          },
          message: 'Active jobs must be a positive number or "unlimited"',
        },
      },
      jobVisibilityDays: {
        type: Number,
        required: [true, 'Job visibility days is required'],
        min: [1, 'Job visibility days must be at least 1'],
      },
      cvViews: {
        type: Schema.Types.Mixed,
        required: [true, 'CV views limit is required'],
        validate: {
          validator: function(value: any) {
            return value === 'unlimited' || (typeof value === 'number' && value > 0);
          },
          message: 'CV views must be a positive number or "unlimited"',
        },
      },
      featuredJobs: {
        type: Number,
        default: 0,
        min: [0, 'Featured jobs cannot be negative'],
      },
      priorityPlacement: {
        type: Boolean,
        default: false,
      },
      featuredCompanyProfile: {
        type: Boolean,
        default: false,
      },
      candidateMatching: {
        type: Boolean,
        default: false,
      },
      advancedAnalytics: {
        type: Boolean,
        default: false,
      },
      dedicatedSupport: {
        type: Boolean,
        default: false,
      },
      customFeatures: {
        type: [String],
        default: [],
      },
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    isPopular: {
      type: Boolean,
      default: false,
    },
    sortOrder: {
      type: Number,
      default: 0,
    },
  },
  {
    timestamps: true, // Automatically add createdAt and updatedAt fields
  }
);

const SubscriptionPlan = mongoose.model<ISubscriptionPlan>('SubscriptionPlan', subscriptionPlanSchema);

export default SubscriptionPlan;
