"use client";

import { useAuthStore } from "@/stores/auth-store";
import { URLS } from "@/utils/urls";
import {
  Button,
  Group,
  LoadingOverlay,
  PasswordInput,
  Select,
  Textarea,
  TextInput,
} from "@mantine/core";
import { useForm } from "@mantine/form";
import { zodResolver } from "mantine-form-zod-resolver";
import { useState } from "react";
import {
  FaBriefcase,
  FaBuilding,
  FaEnvelope,
  FaGlobe,
  FaLock,
  FaPhone,
} from "react-icons/fa";
import { Link, useNavigate } from "react-router";
import { z } from "zod";

// Create a simplified schema for employer registration
const employerSimpleSchema = z
  .object({
    // Company information as the main user data
    companyName: z
      .string()
      .min(2, "Company name must be at least 2 characters"),
    email: z.string().email("Invalid email address"),
    password: z
      .string()
      .min(8, "Password must be at least 8 characters")
      .regex(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
        "Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character",
      ),
    passwordConfirmation: z.string(),
    phone: z.string().min(10, "Phone number must be at least 10 digits"),
    website: z.string().url("Invalid URL").optional().or(z.literal("")),
    description: z
      .string()
      .min(10, "Description must be at least 10 characters"),
    industry: z.string().min(1, "Industry is required"),
    companySize: z.string().min(1, "Company size is required"),
    role: z.literal("employer"),
  })
  .refine((data) => data.password === data.passwordConfirmation, {
    message: "Passwords do not match",
    path: ["passwordConfirmation"],
  });

export default function EmployerRegisterForm() {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);

  // Simplified form for employer registration
  const form = useForm({
    initialValues: {
      companyName: "",
      email: "",
      password: "",
      passwordConfirmation: "",
      phone: "",
      website: "",
      description: "",
      industry: "",
      companySize: "",
      role: "employer" as const,
    },
    validate: zodResolver(employerSimpleSchema),
  });

  // Handle form submission
  const handleSubmit = async (values: any) => {
    setIsLoading(true);

    try {
      // Get register function from auth store
      const { register } = useAuthStore.getState();

      // Map form values to registration data
      const registrationData = {
        // Use company name as the user name
        name: values.companyName,
        email: values.email,
        password: values.password,
        passwordConfirmation: values.passwordConfirmation,
        role: values.role,
        // Company information
        companyName: values.companyName,
        industry: values.industry,
        companySize: values.companySize,
        phone: values.phone,
        website: values.website,
        description: values.description,
      };

      // Register the employer
      const success = await register(registrationData);

      if (success) {
        // Get the updated user after registration
        const { user } = useAuthStore.getState();

        if (user) {
          // Redirect to employer profile page
          navigate(URLS.employer.profile);
        } else {
          // If user is not available yet, redirect to home page
          navigate("/");
        }
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="relative">
      <LoadingOverlay visible={isLoading} overlayProps={{ blur: 2 }} />

      <form
        onSubmit={form.onSubmit(handleSubmit)}
        className="flex flex-col gap-4"
      >
        <TextInput
          label="Company Name"
          placeholder="Enter your company name"
          required
          leftSection={<FaBuilding />}
          {...form.getInputProps("companyName")}
        />

        <TextInput
          label="Email"
          placeholder="<EMAIL>"
          required
          leftSection={<FaEnvelope />}
          {...form.getInputProps("email")}
        />

        <PasswordInput
          label="Password"
          placeholder="Your password"
          required
          leftSection={<FaLock />}
          {...form.getInputProps("password")}
        />

        <PasswordInput
          label="Confirm Password"
          placeholder="Confirm your password"
          required
          leftSection={<FaLock />}
          {...form.getInputProps("passwordConfirmation")}
        />

        <TextInput
          label="Phone Number"
          placeholder="Enter your phone number"
          required
          leftSection={<FaPhone />}
          {...form.getInputProps("phone")}
        />

        <TextInput
          label="Website"
          placeholder="https://yourcompany.com"
          leftSection={<FaGlobe />}
          {...form.getInputProps("website")}
        />

        <Textarea
          label="Company Description"
          placeholder="Describe your company (min 10 characters)"
          required
          minRows={3}
          {...form.getInputProps("description")}
        />

        <Select
          label="Industry"
          placeholder="Select your industry"
          required
          data={[
            { value: "technology", label: "Technology" },
            { value: "healthcare", label: "Healthcare" },
            { value: "finance", label: "Finance" },
            { value: "education", label: "Education" },
            { value: "retail", label: "Retail" },
            { value: "manufacturing", label: "Manufacturing" },
            { value: "design", label: "Design" },
            { value: "marketing", label: "Marketing" },
            { value: "consulting", label: "Consulting" },
            { value: "other", label: "Other" },
          ]}
          leftSection={<FaBriefcase />}
          {...form.getInputProps("industry")}
        />

        <Select
          label="Company Size"
          placeholder="Select company size"
          required
          data={[
            { value: "1-10", label: "1-10 employees" },
            { value: "11-50", label: "11-50 employees" },
            { value: "51-200", label: "51-200 employees" },
            { value: "201-500", label: "201-500 employees" },
            { value: "501+", label: "501+ employees" },
          ]}
          leftSection={<FaBriefcase />}
          {...form.getInputProps("companySize")}
        />

        <Button type="submit" loading={isLoading} mt="md">
          Complete Registration
        </Button>

        <Group mt="md" justify="center">
          <Link
            to="/auth/login"
            className="text-sm text-blue-600 hover:underline"
          >
            Already have an account? Login
          </Link>
        </Group>
      </form>
    </div>
  );
}
