# Subscription Features for Employer Module

This document outlines the subscription features implemented for the employer module in the Job Nest application.

## Overview

The subscription system allows employers to subscribe to different plans that provide varying levels of access to job posting and candidate management features. The system includes:

1. Subscription plans with different tiers (Basic, Professional, Enterprise)
2. User subscriptions that track active subscriptions and usage
3. Middleware to enforce subscription limits
4. API endpoints to manage subscriptions

## Subscription Plans

Each subscription plan includes the following features:

- **Active Jobs**: Number of job postings an employer can have active at once
- **Job Visibility Days**: How long job postings remain visible to candidates
- **CV Views**: Number of candidate CVs an employer can view per month
- **Featured Jobs**: Number of jobs that can be featured in search results
- **Priority Placement**: Whether jobs appear higher in search results
- **Featured Company Profile**: Whether the company profile is featured
- **Candidate Matching**: Access to AI-powered candidate matching
- **Advanced Analytics**: Access to detailed hiring analytics
- **Dedicated Support**: Access to dedicated customer support
- **Custom Features**: Additional features specific to the plan

## API Endpoints

### Public Endpoints

- `GET /api/subscriptions/plans` - Get all active subscription plans
- `GET /api/subscriptions/plans/:id` - Get a specific subscription plan

### Employer Endpoints (Protected)

- `GET /api/subscriptions/current` - Get the current user's active subscription
- `POST /api/subscriptions/subscribe` - Subscribe to a plan
- `PATCH /api/subscriptions/cancel` - Cancel the current subscription
- `GET /api/subscriptions/check-job-posting-eligibility` - Check if user can post a new job

### Admin Endpoints (Protected)

- `POST /api/subscriptions/plans` - Create a new subscription plan
- `PATCH /api/subscriptions/plans/:id` - Update a subscription plan
- `DELETE /api/subscriptions/plans/:id` - Delete a subscription plan (marks as inactive)

## Middleware

The subscription system includes middleware to enforce subscription limits:

1. `checkSubscriptionForJobPosting`: Ensures the user has an active subscription and hasn't reached their job posting limit before allowing them to create a new job.

2. `checkSubscriptionForCVViewing`: Ensures the user has an active subscription and hasn't reached their CV viewing limit before allowing them to view candidate details.

3. `incrementCVViewCount`: Increments the user's CV view count when they view a candidate's details.

## Database Models

### SubscriptionPlan

Stores information about available subscription plans:

- Name, description, price
- Features (active jobs, CV views, etc.)
- Status (active/inactive)
- Sort order for display

### UserSubscription

Tracks user subscriptions:

- User reference
- Plan reference
- Status (active, canceled, expired, pending)
- Start and end dates
- Billing information
- Usage statistics

## Usage Examples

### Subscribing to a Plan

```javascript
// Example request to subscribe to a plan
const response = await fetch('/api/subscriptions/subscribe', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer YOUR_TOKEN'
  },
  body: JSON.stringify({
    planId: 'plan_id_here',
    billingPeriod: 'month', // or 'year'
    paymentMethod: 'credit_card',
    transactionId: 'transaction_id_from_payment_processor'
  })
});

const data = await response.json();
console.log(data.subscription);
```

### Checking Job Posting Eligibility

```javascript
// Example request to check if user can post a new job
const response = await fetch('/api/subscriptions/check-job-posting-eligibility', {
  method: 'GET',
  headers: {
    'Authorization': 'Bearer YOUR_TOKEN'
  }
});

const data = await response.json();
if (data.canPostJob) {
  // Proceed to job posting form
} else {
  // Show upgrade message
  console.log(data.reason);
}
```

## Setting Up

1. Run the seed script to populate the database with initial subscription plans:

```bash
npm run seed:subscription-plans
```

2. Ensure the subscription middleware is properly integrated with the job and application routes.

3. Test the subscription endpoints to verify they're working correctly.

## Integration with Frontend

The frontend should:

1. Display subscription plans on the pricing page
2. Allow users to select and purchase plans
3. Show subscription status in the user dashboard
4. Handle subscription-related errors (e.g., job posting limit reached)
5. Provide upgrade prompts when users hit limits
