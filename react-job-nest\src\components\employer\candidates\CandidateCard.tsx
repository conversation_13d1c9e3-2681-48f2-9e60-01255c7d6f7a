"use client";

import { useSelectedCandidateStore } from "@/stores/employer-store";
import { useCandidateDetailsModal } from "@/stores/modal-store";
import {
  Avatar,
  Badge,
  Button,
  Card,
  Group,
  Menu,
  Stack,
  Text,
} from "@mantine/core";
import {
  FaBriefcase,
  FaCalendarAlt,
  FaCheck,
  FaEye,
  FaTimes,
  FaUserCheck,
  FaUserClock,
  FaUserEdit,
  FaUserTie,
} from "react-icons/fa";

// Define the candidate interface for this component
interface Candidate {
  _id: string;
  name: string;
  email: string;
  status: string;
  job: {
    _id: string;
    title: string;
  };
  appliedDate: string;
  skills?: string[];
  resume?: string;
  coverLetter?: string;
}

type CandidateCardProps = {
  candidate: Candidate;
  job: {
    _id: string;
    title: string;
  };
  onUpdateStatus?: (candidateId: string, newStatus: string) => Promise<boolean>;
};

export default function CandidateCard({
  candidate,
  job,
  onUpdateStatus,
}: CandidateCardProps) {
  const candidateDetailsModal = useCandidateDetailsModal();
  const setSelectedCandidateId = useSelectedCandidateStore(
    (state) => state.setSelectedCandidateId,
  );

  const openModal = () => {
    setSelectedCandidateId(Number(candidate._id));
    candidateDetailsModal.open();
  };

  // Function to update candidate status
  const updateStatus = async (newStatus: string) => {
    if (onUpdateStatus) {
      await onUpdateStatus(candidate._id, newStatus);
    }
  };

  // Function to get status color
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "pending":
        return "blue";
      case "reviewing":
        return "indigo";
      case "shortlisted":
        return "green";
      case "interview":
        return "yellow";
      case "offered":
        return "cyan";
      case "hired":
        return "violet";
      case "rejected":
        return "red";
      default:
        return "gray";
    }
  };

  // Function to get status icon
  // const getStatusIcon = (status: string) => {
  //   switch (status.toLowerCase()) {
  //     case "pending":
  //       return <FaUserClock size={14} />;
  //     case "reviewing":
  //       return <FaUserEdit size={14} />;
  //     case "shortlisted":
  //       return <FaCheck size={14} />;
  //     case "interview":
  //       return <FaUserTie size={14} />;
  //     case "offered":
  //       return <FaUserCheck size={14} />;
  //     case "hired":
  //       return <FaUserCheck size={14} />;
  //     case "rejected":
  //       return <FaTimes size={14} />;
  //     default:
  //       return <FaUserClock size={14} />;
  //   }
  // };

  // Function to get initials from name
  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase();
  };

  return (
    <Card withBorder radius="md" className="h-full shadow-sm" p="lg">
      <Stack gap="md" className="h-full">
        {/* Header with name and status */}
        <Group justify="space-between" align="flex-start">
          <Group wrap="nowrap">
            <Avatar
              radius="xl"
              size="md"
              color={getStatusColor(candidate.status)}
            >
              {getInitials(candidate.name)}
            </Avatar>
            <div>
              <Text fw={600} size="lg" className="line-clamp-1">
                {candidate.name}
              </Text>
              <Text size="sm" c="dimmed" className="line-clamp-1">
                {candidate.email}
              </Text>
            </div>
          </Group>
          <Badge color={getStatusColor(candidate.status)} size="md">
            {candidate.status}
          </Badge>
        </Group>

        {/* Job and application details */}
        <Stack gap="xs" className="flex-grow">
          <Group gap="xs" wrap="nowrap">
            <FaBriefcase size={14} className="flex-shrink-0 text-blue-500" />
            <Text size="sm" className="line-clamp-1">
              Applied for: <strong>{job?.title}</strong>
            </Text>
          </Group>
          <Group gap="xs" wrap="nowrap">
            <FaCalendarAlt size={14} className="flex-shrink-0 text-blue-500" />
            <Text size="sm">Applied on: {candidate.appliedDate}</Text>
          </Group>
        </Stack>

        {/* Skills */}
        {candidate.skills && candidate.skills.length > 0 && (
          <Group gap="xs">
            {candidate.skills.slice(0, 3).map((skill, index) => (
              <Badge key={index} color="blue" variant="light" size="sm">
                {skill}
              </Badge>
            ))}
            {candidate.skills.length > 3 && (
              <Badge color="gray" variant="light" size="sm">
                +{candidate.skills.length - 3} more
              </Badge>
            )}
          </Group>
        )}

        {/* Action buttons */}
        <Group grow>
          <Button
            variant="light"
            leftSection={<FaEye size={16} />}
            onClick={() => openModal()}
          >
            View Details
          </Button>

          <Menu position="bottom-end" shadow="md">
            <Menu.Target>
              <Button variant="outline" leftSection={<FaUserEdit size={16} />}>
                Change Status
              </Button>
            </Menu.Target>
            <Menu.Dropdown>
              <Menu.Label>Update Application Status</Menu.Label>
              <Menu.Item
                leftSection={<FaUserClock size={14} />}
                onClick={() => updateStatus("pending")}
                disabled={candidate.status.toLowerCase() === "pending"}
              >
                Pending
              </Menu.Item>
              <Menu.Item
                leftSection={<FaUserEdit size={14} />}
                onClick={() => updateStatus("reviewing")}
                disabled={candidate.status.toLowerCase() === "reviewing"}
              >
                Reviewing
              </Menu.Item>
              <Menu.Item
                leftSection={<FaCheck size={14} />}
                onClick={() => updateStatus("shortlisted")}
                disabled={candidate.status.toLowerCase() === "shortlisted"}
              >
                Shortlisted
              </Menu.Item>
              <Menu.Item
                leftSection={<FaUserTie size={14} />}
                onClick={() => updateStatus("interview")}
                disabled={candidate.status.toLowerCase() === "interview"}
              >
                Interview
              </Menu.Item>
              <Menu.Item
                leftSection={<FaUserCheck size={14} />}
                onClick={() => updateStatus("offered")}
                disabled={candidate.status.toLowerCase() === "offered"}
              >
                Offered
              </Menu.Item>
              <Menu.Item
                leftSection={<FaUserCheck size={14} />}
                onClick={() => updateStatus("hired")}
                disabled={candidate.status.toLowerCase() === "hired"}
              >
                Hired
              </Menu.Item>
              <Menu.Item
                leftSection={<FaTimes size={14} />}
                onClick={() => updateStatus("rejected")}
                disabled={candidate.status.toLowerCase() === "rejected"}
              >
                Rejected
              </Menu.Item>
            </Menu.Dropdown>
          </Menu>
        </Group>
      </Stack>
    </Card>
  );
}
