import ContactUsForm from "@/components/website/contact-us/ContactUsForm";
import { Badge, useMantineColorScheme } from "@mantine/core";

export default function ContactUsPage() {
  const { colorScheme } = useMantineColorScheme();
  const isDark = colorScheme === "dark";

  return (
    <div className={`py-20 ${isDark ? "bg-gray-900" : "bg-gray-50"}`}>
      <div className="container">
        <div className="mx-auto mb-16 max-w-3xl text-center">
          <Badge color="primary" size="lg" radius="sm" className="mb-4">
            Get In Touch
          </Badge>
          <h1
            className={`mb-6 text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl ${isDark ? "text-white" : ""}`}
          >
            We&apos;d Love to Hear From You
          </h1>
          <p
            className={`text-lg ${isDark ? "text-gray-300" : "text-gray-600"}`}
          >
            Whether you have a question about our platform, need technical
            support, or want to explore partnership opportunities, our team is
            ready to help.
          </p>
        </div>

        <ContactUsForm />

        <div className="mt-20">
          <iframe
            src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3153.0952890486577!2d-122.4194!3d37.7749!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x80859a6d00690021%3A0x4a501367f076adff!2sSan%20Francisco%2C%20CA!5e0!3m2!1sen!2sus!4v1625687461947!5m2!1sen!2sus"
            width="100%"
            height="450"
            style={{ border: 0, borderRadius: "0.5rem" }}
            allowFullScreen
            loading="lazy"
            title="JobNest Office Location"
            className={`shadow-md ${isDark ? "opacity-80" : ""}`}
          ></iframe>
        </div>
      </div>
    </div>
  );
}
