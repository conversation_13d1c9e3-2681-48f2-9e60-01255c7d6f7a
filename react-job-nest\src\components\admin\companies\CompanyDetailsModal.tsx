"use client";

import {
  Avatar,
  Badge,
  Button,
  Card,
  Divider,
  Grid,
  Group,
  Image,
  Modal,
  Paper,
  SimpleGrid,
  Stack,
  Tabs,
  Text,
  ThemeIcon,
  Timeline,
  Tooltip,
} from "@mantine/core";
import {
  FaBan,
  FaBriefcase,
  FaBuilding,
  FaCalendarAlt,
  FaCheck,
  FaCheckCircle,
  FaEdit,
  FaEnvelope,
  FaExclamationCircle,
  FaEye,
  FaFacebook,
  FaFileAlt,
  FaGlobe,
  FaHistory,
  FaInfoCircle,
  FaLinkedin,
  FaMapMarkerAlt,
  FaPhone,
  FaTimesCircle,
  FaTrash,
  FaTwitter,
  FaUser,
  FaUsers,
} from "react-icons/fa";
import { Link } from "react-router";

interface CompanyDetailsModalProps {
  opened: boolean;
  onClose: () => void;
  companyId: number | null;
  onApprove?: () => void;
  onBlock?: () => void;
}

// Enhanced mock company data
const mockCompanyDetails = {
  id: 1,
  name: "Tech Corp",
  logo: "/images/company-logo-placeholder.png",
  coverImage: "/images/company-cover-placeholder.png",
  industry: "Technology",
  size: "201-500",
  type: "Corporation",
  location: "San Francisco, CA",
  status: "pending",
  website: "https://techcorp.example.com",
  email: "<EMAIL>",
  phone: "+****************",
  description:
    "Tech Corp is a leading technology company specializing in software development and IT services. We provide innovative solutions to businesses of all sizes, helping them leverage technology to achieve their goals. Our team of experts is dedicated to delivering high-quality products and services that meet the needs of our clients.",
  contactPerson: "John Smith (HR Manager)",
  registeredDate: "2023-10-10",
  verified: false,
  socialMedia: {
    linkedin: "https://linkedin.com/company/techcorp",
    twitter: "https://twitter.com/techcorp",
    facebook: "https://facebook.com/techcorp",
  },
  jobs: [
    {
      id: 1,
      title: "Senior Software Engineer",
      location: "San Francisco, CA",
      type: "Full-time",
      postedDate: "2023-10-15",
      status: "active",
      applicants: 12,
    },
    {
      id: 2,
      title: "UX Designer",
      location: "Remote",
      type: "Full-time",
      postedDate: "2023-10-12",
      status: "active",
      applicants: 8,
    },
    {
      id: 3,
      title: "Product Manager",
      location: "San Francisco, CA",
      type: "Full-time",
      postedDate: "2023-10-08",
      status: "pending",
      applicants: 5,
    },
  ],
  activityLog: [
    {
      id: 1,
      action: "Company Registered",
      date: "2023-10-10",
      user: "John Smith",
      details: "Initial registration completed",
    },
    {
      id: 2,
      action: "Profile Updated",
      date: "2023-10-11",
      user: "John Smith",
      details: "Company description and contact information updated",
    },
    {
      id: 3,
      action: "Job Posted",
      date: "2023-10-12",
      user: "John Smith",
      details: "Posted job: UX Designer",
    },
    {
      id: 4,
      action: "Job Posted",
      date: "2023-10-15",
      user: "John Smith",
      details: "Posted job: Senior Software Engineer",
    },
  ],
  documents: [
    {
      id: 1,
      name: "Business Registration Certificate",
      type: "PDF",
      uploadDate: "2023-10-10",
      status: "verified",
    },
    {
      id: 2,
      name: "Company Profile",
      type: "PDF",
      uploadDate: "2023-10-11",
      status: "verified",
    },
    {
      id: 3,
      name: "Tax Identification Document",
      type: "PDF",
      uploadDate: "2023-10-11",
      status: "pending",
    },
  ],
};

export default function CompanyDetailsModal({
  opened,
  onClose,
  // companyId,
  onApprove,
  onBlock,
}: CompanyDetailsModalProps) {
  // In a real application, you would fetch company details based on companyId
  const company = mockCompanyDetails;

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case "active":
        return "green";
      case "pending":
        return "yellow";
      case "blocked":
        return "red";
      default:
        return "blue";
    }
  };

  const getDocumentStatusIcon = (status: string) => {
    switch (status) {
      case "verified":
        return <FaCheckCircle size={14} className="text-green-500" />;
      case "pending":
        return <FaExclamationCircle size={14} className="text-yellow-500" />;
      case "rejected":
        return <FaTimesCircle size={14} className="text-red-500" />;
      default:
        return <FaFileAlt size={14} className="text-gray-500" />;
    }
  };

  return (
    <Modal
      opened={opened}
      onClose={onClose}
      title={
        <Group gap="md">
          <Avatar
            src={company.logo}
            size="lg"
            radius="xl"
            className="border border-gray-200"
          />
          <div>
            <Group gap="xs">
              <Text size="lg" fw={700}>
                {company.name}
              </Text>
              {company.verified && (
                <Tooltip label="Verified Company">
                  <ThemeIcon color="blue" variant="light" radius="xl" size="sm">
                    <FaCheck size={10} />
                  </ThemeIcon>
                </Tooltip>
              )}
            </Group>
            <Group gap="xs">
              <Badge
                color={getStatusBadgeColor(company.status)}
                variant="light"
              >
                {company.status}
              </Badge>
              <Badge variant="outline" color="gray">
                {company.industry}
              </Badge>
            </Group>
          </div>
        </Group>
      }
      size="xl"
      padding="lg"
      centered
    >
      <Tabs defaultValue="overview" mt="xl">
        <Tabs.List>
          <Tabs.Tab value="overview" leftSection={<FaInfoCircle size={14} />}>
            Overview
          </Tabs.Tab>
          <Tabs.Tab value="jobs" leftSection={<FaBriefcase size={14} />}>
            Jobs
          </Tabs.Tab>
          <Tabs.Tab value="documents" leftSection={<FaFileAlt size={14} />}>
            Documents
          </Tabs.Tab>
          <Tabs.Tab value="activity" leftSection={<FaHistory size={14} />}>
            Activity
          </Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="overview" pt="md">
          <div className="mb-4 overflow-hidden rounded-lg">
            <Image
              src={company.coverImage}
              alt={`${company.name} cover`}
              height={150}
              className="w-full object-cover"
            />
          </div>

          <Grid gutter="md">
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Paper withBorder p="md" radius="md">
                <Text fw={600} size="sm" c="dimmed" mb="md">
                  COMPANY INFORMATION
                </Text>
                <Stack gap="sm">
                  <Group>
                    <ThemeIcon variant="light" color="blue" size="md">
                      <FaBuilding size={14} />
                    </ThemeIcon>
                    <div>
                      <Text size="xs" c="dimmed">
                        Industry
                      </Text>
                      <Text size="sm">{company.industry}</Text>
                    </div>
                  </Group>
                  <Group>
                    <ThemeIcon variant="light" color="green" size="md">
                      <FaUsers size={14} />
                    </ThemeIcon>
                    <div>
                      <Text size="xs" c="dimmed">
                        Company Size
                      </Text>
                      <Text size="sm">{company.size} employees</Text>
                    </div>
                  </Group>
                  <Group>
                    <ThemeIcon variant="light" color="grape" size="md">
                      <FaBuilding size={14} />
                    </ThemeIcon>
                    <div>
                      <Text size="xs" c="dimmed">
                        Company Type
                      </Text>
                      <Text size="sm">{company.type}</Text>
                    </div>
                  </Group>
                  <Group align="flex-start">
                    <ThemeIcon
                      variant="light"
                      color="orange"
                      size="md"
                      style={{ marginTop: "4px" }}
                    >
                      <FaMapMarkerAlt size={14} />
                    </ThemeIcon>
                    <div>
                      <Text size="xs" c="dimmed">
                        Location
                      </Text>
                      <Text size="sm">{company.location}</Text>
                    </div>
                  </Group>
                </Stack>
              </Paper>
            </Grid.Col>

            <Grid.Col span={{ base: 12, md: 6 }}>
              <Paper withBorder p="md" radius="md">
                <Text fw={600} size="sm" c="dimmed" mb="md">
                  CONTACT INFORMATION
                </Text>
                <Stack gap="sm">
                  <Group>
                    <ThemeIcon variant="light" color="cyan" size="md">
                      <FaGlobe size={14} />
                    </ThemeIcon>
                    <div>
                      <Text size="xs" c="dimmed">
                        Website
                      </Text>
                      <Text size="sm" className="text-blue-500 hover:underline">
                        <a
                          href={company.website}
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          {company.website.replace("https://", "")}
                        </a>
                      </Text>
                    </div>
                  </Group>
                  <Group>
                    <ThemeIcon variant="light" color="blue" size="md">
                      <FaEnvelope size={14} />
                    </ThemeIcon>
                    <div>
                      <Text size="xs" c="dimmed">
                        Email
                      </Text>
                      <Text size="sm">{company.email}</Text>
                    </div>
                  </Group>
                  <Group>
                    <ThemeIcon variant="light" color="green" size="md">
                      <FaPhone size={14} />
                    </ThemeIcon>
                    <div>
                      <Text size="xs" c="dimmed">
                        Phone
                      </Text>
                      <Text size="sm">{company.phone}</Text>
                    </div>
                  </Group>
                  <Group>
                    <ThemeIcon variant="light" color="indigo" size="md">
                      <FaUser size={14} />
                    </ThemeIcon>
                    <div>
                      <Text size="xs" c="dimmed">
                        Contact Person
                      </Text>
                      <Text size="sm">{company.contactPerson}</Text>
                    </div>
                  </Group>
                </Stack>
              </Paper>
            </Grid.Col>

            <Grid.Col span={12}>
              <Paper withBorder p="md" radius="md">
                <Text fw={600} size="sm" c="dimmed" mb="md">
                  DESCRIPTION
                </Text>
                <Text size="sm" lh={1.6}>
                  {company.description}
                </Text>
              </Paper>
            </Grid.Col>

            <Grid.Col span={12}>
              <Paper withBorder p="md" radius="md">
                <Text fw={600} size="sm" c="dimmed" mb="md">
                  SOCIAL MEDIA
                </Text>
                <Group>
                  <Button
                    variant="light"
                    color="blue"
                    leftSection={<FaLinkedin size={16} />}
                    component={Link}
                    to={company.socialMedia.linkedin}
                    target="_blank"
                  >
                    LinkedIn
                  </Button>
                  <Button
                    variant="light"
                    color="cyan"
                    leftSection={<FaTwitter size={16} />}
                    component={Link}
                    to={company.socialMedia.twitter}
                    target="_blank"
                  >
                    Twitter
                  </Button>
                  <Button
                    variant="light"
                    color="indigo"
                    leftSection={<FaFacebook size={16} />}
                    component={Link}
                    to={company.socialMedia.facebook}
                    target="_blank"
                  >
                    Facebook
                  </Button>
                </Group>
              </Paper>
            </Grid.Col>

            <Grid.Col span={12}>
              <Group>
                <ThemeIcon variant="light" color="gray" size="md">
                  <FaCalendarAlt size={14} />
                </ThemeIcon>
                <Text size="sm">Registered on: {company.registeredDate}</Text>
              </Group>
            </Grid.Col>
          </Grid>
        </Tabs.Panel>

        <Tabs.Panel value="jobs" pt="md">
          <Text fw={600} size="md" mb="md">
            Jobs Posted by {company.name}
          </Text>

          {company.jobs.map((job) => (
            <Card
              key={job.id}
              withBorder
              shadow="sm"
              radius="md"
              mb="md"
              padding="md"
            >
              <Group justify="space-between">
                <div>
                  <Text fw={500}>{job.title}</Text>
                  <Group gap={6} mt={4}>
                    <Badge variant="light" size="sm">
                      {job.type}
                    </Badge>
                    <Text size="xs" c="dimmed">
                      <FaMapMarkerAlt size={10} className="mr-1 inline" />
                      {job.location}
                    </Text>
                  </Group>
                </div>
                <Badge color={getStatusBadgeColor(job.status)}>
                  {job.status}
                </Badge>
              </Group>

              <Group justify="space-between" mt="md">
                <Text size="xs" c="dimmed">
                  Posted: {job.postedDate} • {job.applicants} applicants
                </Text>
                <Group gap="xs">
                  <Button
                    variant="light"
                    size="xs"
                    leftSection={<FaEye size={12} />}
                  >
                    View
                  </Button>
                  {job.status === "pending" && (
                    <>
                      <Button
                        variant="light"
                        color="green"
                        size="xs"
                        leftSection={<FaCheck size={12} />}
                      >
                        Approve
                      </Button>
                      <Button
                        variant="light"
                        color="red"
                        size="xs"
                        leftSection={<FaBan size={12} />}
                      >
                        Reject
                      </Button>
                    </>
                  )}
                </Group>
              </Group>
            </Card>
          ))}
        </Tabs.Panel>

        <Tabs.Panel value="documents" pt="md">
          <Text fw={600} size="md" mb="md">
            Company Documents
          </Text>

          <SimpleGrid cols={{ base: 1, sm: 2 }} spacing="md">
            {company.documents.map((doc) => (
              <Paper key={doc.id} withBorder p="md" radius="md">
                <Group justify="space-between">
                  <Group>
                    <ThemeIcon
                      variant="light"
                      color="blue"
                      size="lg"
                      radius="xl"
                    >
                      <FaFileAlt size={18} />
                    </ThemeIcon>
                    <div>
                      <Text size="sm" fw={500}>
                        {doc.name}
                      </Text>
                      <Text size="xs" c="dimmed">
                        Uploaded: {doc.uploadDate}
                      </Text>
                    </div>
                  </Group>
                  <Tooltip label={`Status: ${doc.status}`}>
                    {getDocumentStatusIcon(doc.status)}
                  </Tooltip>
                </Group>
                <Group justify="flex-end" mt="md">
                  <Button
                    variant="light"
                    size="xs"
                    leftSection={<FaEye size={12} />}
                  >
                    View
                  </Button>
                  {doc.status === "pending" && (
                    <>
                      <Button
                        variant="light"
                        color="green"
                        size="xs"
                        leftSection={<FaCheck size={12} />}
                      >
                        Verify
                      </Button>
                      <Button
                        variant="light"
                        color="red"
                        size="xs"
                        leftSection={<FaBan size={12} />}
                      >
                        Reject
                      </Button>
                    </>
                  )}
                </Group>
              </Paper>
            ))}
          </SimpleGrid>
        </Tabs.Panel>

        <Tabs.Panel value="activity" pt="md">
          <Text fw={600} size="md" mb="md">
            Activity Log
          </Text>

          <Timeline
            active={company.activityLog.length - 1}
            bulletSize={24}
            lineWidth={2}
          >
            {company.activityLog.map((activity) => (
              <Timeline.Item
                key={activity.id}
                title={
                  <Text fw={500} size="sm">
                    {activity.action}
                  </Text>
                }
                bullet={<FaHistory size={12} />}
                color="blue"
              >
                <Text size="sm">{activity.details}</Text>
                <Text size="xs" mt={4} c="dimmed">
                  {activity.date} • {activity.user}
                </Text>
              </Timeline.Item>
            ))}
          </Timeline>
        </Tabs.Panel>
      </Tabs>

      <Divider my="lg" />

      <Group justify="space-between">
        <Group>
          <Button variant="subtle" leftSection={<FaEdit size={14} />}>
            Edit
          </Button>
          <Button
            variant="subtle"
            color="red"
            leftSection={<FaTrash size={14} />}
          >
            Delete
          </Button>
        </Group>

        {/* Action Buttons for Pending Companies */}
        {company.status === "pending" && onApprove && onBlock && (
          <Group>
            <Button
              variant="outline"
              color="red"
              leftSection={<FaBan size={14} />}
              onClick={() => {
                onBlock();
                onClose();
              }}
            >
              Block
            </Button>
            <Button
              color="green"
              leftSection={<FaCheck size={14} />}
              onClick={() => {
                onApprove();
                onClose();
              }}
            >
              Approve
            </Button>
          </Group>
        )}

        {/* Block Button for Active Companies */}
        {company.status === "active" && onBlock && (
          <Button
            variant="outline"
            color="red"
            leftSection={<FaBan size={14} />}
            onClick={() => {
              onBlock();
              onClose();
            }}
          >
            Block
          </Button>
        )}
      </Group>
    </Modal>
  );
}
