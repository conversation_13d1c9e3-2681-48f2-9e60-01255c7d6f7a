import multer from "multer";
import path from "path";
import fs from "fs";
import { v4 as uuidv4 } from "uuid";
import { Request } from "express";

// Define allowed file types and their corresponding MIME types
const allowedFileTypes = {
  image: ["image/jpeg", "image/png", "image/gif", "image/webp"],
  document: [
    "application/pdf",
    "application/msword",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
  ],
  csv: ["text/csv"],
};

// Define file size limits (in bytes)
const fileSizeLimits = {
  image: 5 * 1024 * 1024, // 5MB
  document: 10 * 1024 * 1024, // 10MB
  csv: 2 * 1024 * 1024, // 2MB
};

// Create upload directories if they don't exist
const createUploadDirectories = () => {
  const baseDir = path.join(process.cwd(), "uploads");
  const dirs = ["images", "documents", "resumes", "temp"];

  if (!fs.existsSync(baseDir)) {
    fs.mkdirSync(baseDir);
  }

  dirs.forEach((dir) => {
    const dirPath = path.join(baseDir, dir);
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath);
    }
  });
};

// Create upload directories on service initialization
createUploadDirectories();

// Configure storage
const storage = multer.diskStorage({
  destination: (
    req: Request,
    file: Express.Multer.File,
    cb: (error: Error | null, destination: string) => void
  ) => {
    let uploadPath = path.join(process.cwd(), "uploads/temp");

    // Determine destination based on file type
    if (allowedFileTypes.image.includes(file.mimetype)) {
      uploadPath = path.join(process.cwd(), "uploads/images");
    } else if (allowedFileTypes.document.includes(file.mimetype)) {
      // Check if it's a resume upload
      if (req.path.includes("/resume")) {
        uploadPath = path.join(process.cwd(), "uploads/resumes");
      } else {
        uploadPath = path.join(process.cwd(), "uploads/documents");
      }
    }

    cb(null, uploadPath);
  },
  filename: (
    req: Request,
    file: Express.Multer.File,
    cb: (error: Error | null, filename: string) => void
  ) => {
    // Generate a unique filename with original extension
    const fileExt = path.extname(file.originalname);
    const fileName = `${uuidv4()}${fileExt}`;
    cb(null, fileName);
  },
});

// File filter function
const fileFilter = (
  req: Request,
  file: Express.Multer.File,
  cb: multer.FileFilterCallback
) => {
  // Check if file type is allowed
  const isImage = allowedFileTypes.image.includes(file.mimetype);
  const isDocument = allowedFileTypes.document.includes(file.mimetype);
  const isCsv = allowedFileTypes.csv.includes(file.mimetype);

  if (isImage || isDocument || isCsv) {
    // Check file size based on type
    let sizeLimit = fileSizeLimits.document; // Default

    if (isImage) {
      sizeLimit = fileSizeLimits.image;
    } else if (isCsv) {
      sizeLimit = fileSizeLimits.csv;
    }

    // File size check will be handled by multer limits
    return cb(null, true);
  }

  // Reject file if type is not allowed
  cb(new Error("File type not allowed"));
};

// Create multer upload instance
const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 10 * 1024 * 1024, // Default max size (10MB)
  },
});

// Configure specific upload types
const uploadTypes = {
  // For profile images
  profileImage: multer({
    storage,
    fileFilter: (
      req: Request,
      file: Express.Multer.File,
      cb: multer.FileFilterCallback
    ) => {
      if (allowedFileTypes.image.includes(file.mimetype)) {
        cb(null, true);
      } else {
        cb(new Error("Only image files are allowed for profile pictures"));
      }
    },
    limits: {
      fileSize: fileSizeLimits.image,
    },
  }),

  // For resume uploads
  resume: multer({
    storage,
    fileFilter: (
      req: Request,
      file: Express.Multer.File,
      cb: multer.FileFilterCallback
    ) => {
      if (allowedFileTypes.document.includes(file.mimetype)) {
        cb(null, true);
      } else {
        cb(new Error("Only PDF and Word documents are allowed for resumes"));
      }
    },
    limits: {
      fileSize: fileSizeLimits.document,
    },
  }),

  // For company logos
  companyLogo: multer({
    storage,
    fileFilter: (
      req: Request,
      file: Express.Multer.File,
      cb: multer.FileFilterCallback
    ) => {
      if (allowedFileTypes.image.includes(file.mimetype)) {
        cb(null, true);
      } else {
        cb(new Error("Only image files are allowed for company logos"));
      }
    },
    limits: {
      fileSize: fileSizeLimits.image,
    },
  }),
};

// Delete a file
const deleteFile = (filePath: string): Promise<boolean> => {
  return new Promise((resolve) => {
    if (!filePath) {
      resolve(false);
      return;
    }

    const fullPath = path.join(process.cwd(), filePath);

    // Check if file exists
    if (fs.existsSync(fullPath)) {
      fs.unlink(fullPath, (err) => {
        if (err) {
          console.error("Error deleting file:", err);
          resolve(false);
        } else {
          resolve(true);
        }
      });
    } else {
      resolve(false);
    }
  });
};

// Get file URL
const getFileUrl = (filePath: string): string => {
  if (!filePath) return "";

  // Convert file path to URL format
  return filePath.replace(/\\/g, "/").replace(/^uploads\//, "/uploads/");
};

export { upload, uploadTypes, deleteFile, getFileUrl };
