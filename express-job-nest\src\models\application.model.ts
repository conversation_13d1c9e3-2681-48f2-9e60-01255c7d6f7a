import mongoose, { Document, Schema } from 'mongoose';

export type ApplicationStatus = 'pending' | 'reviewing' | 'shortlisted' | 'interview' | 'offered' | 'hired' | 'rejected';

export interface IApplication extends Document {
  job: mongoose.Types.ObjectId;
  candidate: mongoose.Types.ObjectId;
  resume: string;
  coverLetter?: string;
  status: ApplicationStatus;
  questionAnswers: {
    question: string;
    answer: string;
  }[];
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

const applicationSchema = new Schema<IApplication>(
  {
    job: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Job',
      required: [true, 'Job is required'],
    },
    candidate: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'Candidate is required'],
    },
    resume: {
      type: String,
      required: [true, 'Resume is required'],
    },
    coverLetter: {
      type: String,
    },
    status: {
      type: String,
      enum: {
        values: ['pending', 'reviewing', 'shortlisted', 'interview', 'offered', 'hired', 'rejected'],
        message: 'Status must be one of: pending, reviewing, shortlisted, interview, offered, hired, rejected',
      },
      default: 'pending',
    },
    questionAnswers: [
      {
        question: {
          type: String,
          required: true,
        },
        answer: {
          type: String,
          required: true,
        },
      },
    ],
    notes: {
      type: String,
    },
  },
  {
    timestamps: true, // Automatically add createdAt and updatedAt fields
  }
);

// Create indexes for better search performance
applicationSchema.index({ job: 1, candidate: 1 }, { unique: true }); // One application per job per candidate
applicationSchema.index({ job: 1 });
applicationSchema.index({ candidate: 1 });
applicationSchema.index({ status: 1 });

const Application = mongoose.model<IApplication>('Application', applicationSchema);

export default Application;
