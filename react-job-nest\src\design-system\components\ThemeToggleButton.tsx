import { ActionIcon, useMantineColorScheme } from "@mantine/core";
import { IoMoonOutline, IoSunnyOutline } from "react-icons/io5";

export function ThemeToggleButton() {
  const { colorScheme, toggleColorScheme } = useMantineColorScheme();
  const isDark = colorScheme === "dark";

  return (
    <ActionIcon
      variant="outline"
      color={isDark ? "accent" : "primary"}
      onClick={() => toggleColorScheme()}
      title="Toggle color scheme"
      className="transition-all duration-300"
    >
      {isDark ? <IoSunnyOutline size="1rem" /> : <IoMoonOutline size="1rem" />}
    </ActionIcon>
  );
}
