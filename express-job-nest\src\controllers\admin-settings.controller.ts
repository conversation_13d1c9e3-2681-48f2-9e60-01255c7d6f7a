import { Request, Response, NextFunction } from "express";
import { catchAsync } from "../utils/catch-async";
import { AppError } from "../utils/app-error";
import Settings from "../models/settings.model";
import emailService from "../services/email.service";
import { IUser } from "../models/user.model";

// Extend Express Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user: IUser;
    }
  }
}

// Get all settings
export const getSettings = catchAsync(async (_req: Request, res: Response) => {
  // Find settings or create default if not exists
  let settings = await Settings.findOne();

  if (!settings) {
    settings = await Settings.create({});
  }

  res.status(200).json({
    success: true,
    settings,
  });
});

// Update general settings
export const updateGeneralSettings = catchAsync(
  async (req: Request, res: Response) => {
    // Find settings or create default if not exists
    let settings = await Settings.findOne();

    if (!settings) {
      settings = await Settings.create({});
    }

    // Update general settings
    settings.general = {
      ...settings.general,
      ...req.body,
    };

    // Set updatedBy
    settings.updatedBy = req.user._id as any;

    // Save settings
    await settings.save();

    res.status(200).json({
      success: true,
      message: "General settings updated successfully",
      settings: settings.general,
    });
  }
);

// Update email settings
export const updateEmailSettings = catchAsync(
  async (req: Request, res: Response) => {
    // Find settings or create default if not exists
    let settings = await Settings.findOne();

    if (!settings) {
      settings = await Settings.create({});
    }

    // Update email settings
    settings.email = {
      ...settings.email,
      ...req.body,
    };

    // Set updatedBy
    settings.updatedBy = req.user._id as any;

    // Save settings
    await settings.save();

    // Reinitialize email service with new settings
    await emailService.loadSettings();

    res.status(200).json({
      success: true,
      message: "Email settings updated successfully",
      settings: settings.email,
    });
  }
);

// Update job settings
export const updateJobSettings = catchAsync(
  async (req: Request, res: Response) => {
    // Find settings or create default if not exists
    let settings = await Settings.findOne();

    if (!settings) {
      settings = await Settings.create({});
    }

    // Update job settings
    settings.job = {
      ...settings.job,
      ...req.body,
    };

    // Set updatedBy
    settings.updatedBy = req.user._id as any;

    // Save settings
    await settings.save();

    res.status(200).json({
      success: true,
      message: "Job settings updated successfully",
      settings: settings.job,
    });
  }
);

// Update notification settings
export const updateNotificationSettings = catchAsync(
  async (req: Request, res: Response) => {
    // Find settings or create default if not exists
    let settings = await Settings.findOne();

    if (!settings) {
      settings = await Settings.create({});
    }

    // Update notification settings
    settings.notification = {
      ...settings.notification,
      ...req.body,
    };

    // Set updatedBy
    settings.updatedBy = req.user._id as any;

    // Save settings
    await settings.save();

    res.status(200).json({
      success: true,
      message: "Notification settings updated successfully",
      settings: settings.notification,
    });
  }
);

// Test email settings
export const testEmailSettings = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    const { email } = req.body;

    if (!email) {
      return next(new AppError("Email address is required", 400));
    }

    // Send test email
    const emailSent = await emailService.sendEmail({
      to: email,
      subject: "JobNest Email Settings Test",
      html: `
        <h1>Email Settings Test</h1>
        <p>This is a test email to verify your email settings are working correctly.</p>
        <p>If you received this email, your settings are configured properly.</p>
        <p>Time sent: ${new Date().toLocaleString()}</p>
      `,
    });

    if (!emailSent) {
      return next(
        new AppError(
          "Failed to send test email. Please check your email settings.",
          500
        )
      );
    }

    res.status(200).json({
      success: true,
      message: "Test email sent successfully",
    });
  }
);

// Reset settings to default
export const resetSettings = catchAsync(async (req: Request, res: Response) => {
  // Delete existing settings
  await Settings.deleteOne({});

  // Create new settings with defaults
  const settings = await Settings.create({
    updatedBy: req.user._id as any,
  });

  res.status(200).json({
    success: true,
    message: "Settings reset to default values",
    settings,
  });
});
