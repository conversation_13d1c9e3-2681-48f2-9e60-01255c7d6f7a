import express from "express";
import {
  getAllUsers,
  getUserById,
  updateUser,
  deleteUser,
  updateMe,
  deleteMe,
} from "../controllers/user.controller";
import { protect, restrictTo } from "../middleware/auth.middleware";

const router = express.Router();

// Protect all routes after this middleware
router.use(protect);

// User routes (for logged in users)
router.patch("/me", updateMe);
router.delete("/me", deleteMe);

// Admin only routes
router.use(restrictTo("admin"));

router.route("/").get(getAllUsers);

router.route("/:id").get(getUserById).patch(updateUser).delete(deleteUser);

export default router;
