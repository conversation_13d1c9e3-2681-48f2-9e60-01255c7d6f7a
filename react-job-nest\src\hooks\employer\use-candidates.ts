import api from "@/services/api";
import employerApi from "@/services/employer-api";
import { cache } from "@/utils/cache";
import { notifications } from "@mantine/notifications";
import { useEffect, useRef, useState } from "react";

// Define types for the hook
interface Candidate {
  _id: string;
  name: string;
  email: string;
  status: string;
  job: {
    _id: string;
    title: string;
  };
  appliedDate: string;
  skills?: string[];
  resume?: string;
  coverLetter?: string;
}

interface UseCandidatesOptions {
  initialJobId?: string;
  initialStatus?: string;
  initialSearchQuery?: string;
  initialDateRange?: [Date | null, Date | null];
  initialSortBy?: string;
}

interface UseCandidatesReturn {
  candidates: Candidate[];
  isLoading: boolean;
  error: string | null;
  totalCandidates: number;
  page: number;
  setPage: (page: number) => void;
  jobId: string;
  setJobId: (jobId: string) => void;
  status: string;
  setStatus: (status: string) => void;
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  dateRange: [Date | null, Date | null];
  setDateRange: (range: [Date | null, Date | null]) => void;
  sortBy: string;
  setSortBy: (sortBy: string) => void;
  refreshCandidates: () => Promise<void>;
  updateCandidateStatus: (
    candidateId: string,
    newStatus: string,
  ) => Promise<boolean>;
  jobs: Array<{ _id: string; title: string }>;
}

export default function useCandidates({
  initialJobId = "",
  initialStatus = "",
  initialSearchQuery = "",
  initialDateRange = [null, null],
  initialSortBy = "newest",
}: UseCandidatesOptions = {}): UseCandidatesReturn {
  // State for candidates data
  const [candidates, setCandidates] = useState<Candidate[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [totalCandidates, setTotalCandidates] = useState(0);
  const [page, setPage] = useState(1);
  const [limit] = useState(10); // Number of candidates per page

  // State for filters
  const [jobId, setJobId] = useState(initialJobId);
  const [status, setStatus] = useState(initialStatus);
  const [searchQuery, setSearchQuery] = useState(initialSearchQuery);
  const [dateRange, setDateRange] =
    useState<[Date | null, Date | null]>(initialDateRange);
  const [sortBy, setSortBy] = useState(initialSortBy);

  // State for jobs (for filtering)
  const [jobs, setJobs] = useState<Array<{ _id: string; title: string }>>([]);

  // Fetch candidates from the backend
  const fetchCandidates = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Build query parameters
      const params: Record<string, any> = {
        page,
        limit,
        sortBy,
      };

      if (jobId) params.jobId = jobId;
      if (status) params.status = status;
      if (searchQuery) params.search = searchQuery;
      if (dateRange[0]) params.startDate = dateRange[0].toISOString();
      if (dateRange[1]) params.endDate = dateRange[1].toISOString();

      // Check if user is authenticated
      const token = cache.get("accessToken");
      if (!token) {
        throw new Error("You must be logged in to view candidates");
      }

      // Debug: Log the token and user info
      console.log("Using token:", token);

      try {
        // Check current user
        const userResponse = await api.auth.getCurrentUser();
        console.log("Current user:", userResponse.data);
      } catch (error) {
        console.error("Error getting current user:", error);
      }

      // If we have a specific job ID, use the job applications endpoint
      let response;
      // Make the API calls
      if (jobId) {
        response = await employerApi.candidates.getJobApplications(jobId);
      } else {
        // Otherwise use the general applicants endpoint
        response = await employerApi.candidates.getApplicants(params);
      }

      // Process the response
      if (response.data && response.data.success) {
        // Map the API response to our Candidate interface
        let fetchedCandidates: Candidate[] = [];

        // Process applications the same way for both endpoints
        fetchedCandidates = response.data.applications.map((app: any) => ({
          _id: app._id,
          name: app.candidate?.name || "Unknown Candidate",
          email: app.candidate?.email || "<EMAIL>",
          status: app.status || "pending",
          job: {
            _id: app.job?._id || jobId || "unknown",
            title: app.job?.title || "Unknown Job",
          },
          appliedDate: app.createdAt
            ? new Date(app.createdAt).toLocaleDateString()
            : "Unknown date",
          resume: app.resume,
          coverLetter: app.coverLetter,
          skills: app.candidate?.skills || [],
        }));

        setCandidates(fetchedCandidates);
        setTotalCandidates(response.data.count || fetchedCandidates.length);
      } else {
        throw new Error("Failed to fetch candidates");
      }
    } catch (error: any) {
      console.error("Error fetching candidates:", error);

      // Handle specific error types
      if (error.response?.status === 403) {
        setError(
          "You don't have permission to view candidates. Please make sure you're logged in as an employer.",
        );
        notifications.show({
          title: "Permission Denied",
          message:
            "You don't have permission to view candidates. Please make sure you're logged in as an employer.",
          color: "red",
        });
      } else if (error.response?.status === 401) {
        setError("Your session has expired. Please log in again.");
        notifications.show({
          title: "Authentication Error",
          message: "Your session has expired. Please log in again.",
          color: "red",
        });
      } else if (error.response?.status === 404) {
        setError(
          "The requested resource was not found. The API endpoint might be incorrect.",
        );
        notifications.show({
          title: "Not Found",
          message:
            "The requested resource was not found. The API endpoint might be incorrect.",
          color: "red",
        });
      } else {
        setError(error.message || "Failed to fetch candidates");
      }

      // Set empty candidates array
      setCandidates([]);
      setTotalCandidates(0);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch jobs for filtering
  const fetchJobs = async () => {
    try {
      const response = await employerApi.jobs.getMyJobs();
      if (response.data && response.data.success) {
        const fetchedJobs = response.data.jobs.map((job: any) => ({
          _id: job._id,
          title: job.title,
        }));
        setJobs(fetchedJobs);
      }
    } catch (error) {
      console.error("Error fetching jobs:", error);
      // No mock data fallback
      setJobs([]);
    }
  };

  // Update candidate application status
  const updateCandidateStatus = async (
    applicationId: string,
    newStatus: string,
  ): Promise<boolean> => {
    try {
      const response = await employerApi.candidates.updateApplicationStatus(
        applicationId,
        newStatus,
      );

      if (response.data && response.data.success) {
        // Update the local state
        setCandidates((prevCandidates) =>
          prevCandidates.map((candidate) =>
            candidate._id === applicationId
              ? { ...candidate, status: newStatus }
              : candidate,
          ),
        );

        return true;
      } else {
        throw new Error(response.data?.message || "Failed to update status");
      }
    } catch (error: any) {
      console.error("Error updating candidate status:", error);

      notifications.show({
        title: "Error",
        message: error.message || "Failed to update candidate status",
        color: "red",
      });

      return false;
    }
  };

  // We'll use a ref to track if this is the first render
  const isFirstRender = useRef(true);

  // Function to refresh candidates data - this will be called manually by the user
  const refreshCandidates = async () => {
    await fetchCandidates();
  };

  // This effect runs only once on mount to fetch initial data
  useEffect(() => {
    fetchCandidates();
    fetchJobs();

    // After first render, set isFirstRender to false
    return () => {
      isFirstRender.current = false;
    };
  }, []);

  // This effect handles pagination changes only
  useEffect(() => {
    // Skip on first render since the initial fetch already happened
    if (!isFirstRender.current) {
      fetchCandidates();
    }
  }, [page]);

  // We don't need this effect anymore as we fetch jobs in the initial effect

  return {
    candidates,
    isLoading,
    error,
    totalCandidates,
    page,
    setPage,
    jobId,
    setJobId,
    status,
    setStatus,
    searchQuery,
    setSearchQuery,
    dateRange,
    setDateRange,
    sortBy,
    setSortBy,
    refreshCandidates,
    updateCandidateStatus,
    jobs,
  };
}
