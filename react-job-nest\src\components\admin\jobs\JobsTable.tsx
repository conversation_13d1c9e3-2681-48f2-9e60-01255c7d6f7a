"use client";

import { useThemeClasses } from "@/design-system/utils/theme-utils";
import {
  ActionIcon,
  Avatar,
  Badge,
  Checkbox,
  Group,
  Menu,
  Table,
  Text,
  ThemeIcon,
  useMantineColorScheme,
} from "@mantine/core";
import { useState } from "react";
import {
  FaCheck,
  FaEdit,
  FaEllipsisV,
  FaEye,
  FaTag,
  FaTimes,
  FaTrash,
} from "react-icons/fa";

// Enhanced mock data for jobs
const mockJobs = [
  {
    id: 1,
    title: "Software Engineer",
    company: "Tech Corp",
    companyLogo: "/images/company-logo-placeholder.png",
    category: "Technology",
    status: "active",
    postedDate: "2023-10-10",
    applicants: 12,
    salary: "$100,000 - $130,000",
    jobType: "Full-time",
  },
  {
    id: 2,
    title: "Product Manager",
    company: "Innovate Inc",
    companyLogo: "/images/company-logo-placeholder.png",
    category: "Technology",
    location: "New York, NY",
    status: "pending",
    postedDate: "2023-10-09",
    applicants: 5,
    salary: "$110,000 - $140,000",
    jobType: "Full-time",
  },
  {
    id: 3,
    title: "UX Designer",
    company: "Design Studio",
    companyLogo: "/images/company-logo-placeholder.png",
    category: "Design",
    location: "Los Angeles, CA",
    status: "active",
    postedDate: "2023-10-08",
    applicants: 8,
    salary: "$90,000 - $120,000",
    jobType: "Full-time",
  },
  {
    id: 4,
    title: "Marketing Specialist",
    company: "Marketing Pro",
    companyLogo: "/images/company-logo-placeholder.png",
    category: "Marketing",
    status: "expired",
    postedDate: "2023-10-07",
    applicants: 3,
    salary: "$70,000 - $90,000",
    jobType: "Contract",
  },
  {
    id: 5,
    title: "Financial Analyst",
    company: "Finance Group",
    companyLogo: "/images/company-logo-placeholder.png",
    category: "Finance",
    status: "rejected",
    postedDate: "2023-10-06",
    applicants: 0,
    salary: "$80,000 - $100,000",
    jobType: "Part-time",
  },
];

interface JobsTableProps {
  onView: (jobId: number) => void;
  onApprove: (jobId: number) => void;
  onReject: (jobId: number) => void;
}

export default function JobsTable({
  onView,
  onApprove,
  onReject,
}: JobsTableProps) {
  const [jobs] = useState(mockJobs);
  const [selectedJobs, setSelectedJobs] = useState<number[]>([]);
  const [selectAll, setSelectAll] = useState(false);
  const { colorScheme } = useMantineColorScheme();
  const isDark = colorScheme === "dark";

  const toggleSelectAll = () => {
    if (selectAll) {
      setSelectedJobs([]);
    } else {
      setSelectedJobs(jobs.map((job) => job.id));
    }
    setSelectAll(!selectAll);
  };

  const toggleSelectJob = (jobId: number) => {
    if (selectedJobs.includes(jobId)) {
      setSelectedJobs(selectedJobs.filter((id) => id !== jobId));
      setSelectAll(false);
    } else {
      setSelectedJobs([...selectedJobs, jobId]);
      if (selectedJobs.length + 1 === jobs.length) {
        setSelectAll(true);
      }
    }
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case "active":
        return "green";
      case "pending":
        return "yellow";
      case "expired":
        return "gray";
      case "rejected":
        return "red";
      default:
        return "blue";
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case "Technology":
        return "blue";
      case "Design":
        return "violet";
      case "Marketing":
        return "orange";
      case "Finance":
        return "green";
      default:
        return "gray";
    }
  };

  return (
    <div className="overflow-x-auto">
      <Table
        striped
        highlightOnHover
        styles={{
          table: {
            backgroundColor: isDark ? "var(--mantine-color-dark-7)" : undefined,
          },
          thead: {
            backgroundColor: isDark ? "var(--mantine-color-dark-6)" : undefined,
          },
          tr: {
            "&:hover": {
              backgroundColor: isDark
                ? "var(--mantine-color-dark-5) !important"
                : undefined,
            },
          },
        }}
      >
        <Table.Thead>
          <Table.Tr>
            <Table.Th style={{ width: 40 }}>
              <Checkbox
                checked={selectAll}
                onChange={toggleSelectAll}
                aria-label="Select all jobs"
              />
            </Table.Th>
            <Table.Th>Job</Table.Th>
            <Table.Th className="hidden md:table-cell">Company</Table.Th>
            <Table.Th className="hidden lg:table-cell">Category</Table.Th>
            <Table.Th>Status</Table.Th>
            <Table.Th className="hidden lg:table-cell">Posted Date</Table.Th>
            <Table.Th>Actions</Table.Th>
          </Table.Tr>
        </Table.Thead>
        <Table.Tbody>
          {jobs.map((job) => (
            <Table.Tr key={job.id} className="group">
              <Table.Td>
                <Checkbox
                  checked={selectedJobs.includes(job.id)}
                  onChange={() => toggleSelectJob(job.id)}
                  aria-label={`Select ${job.title}`}
                />
              </Table.Td>
              <Table.Td>
                <div>
                  <Text size="sm" fw={500} className="line-clamp-1">
                    {job.title}
                  </Text>
                  <Group gap={6} className="md:hidden">
                    <Text size="xs" c="dimmed">
                      {job.company}
                    </Text>
                    <Text size="xs" c="dimmed">
                      •
                    </Text>
                    <Badge size="xs" variant="outline">
                      {job.jobType}
                    </Badge>
                  </Group>
                </div>
              </Table.Td>
              <Table.Td className="hidden md:table-cell">
                <Group gap="sm">
                  <Avatar
                    src={job.companyLogo}
                    radius="xl"
                    size="sm"
                    className={useThemeClasses(
                      "border border-gray-200",
                      "border border-dark-4",
                    )}
                  />
                  <Text size="sm">{job.company}</Text>
                </Group>
              </Table.Td>
              <Table.Td className="hidden lg:table-cell">
                <Group gap="xs">
                  <ThemeIcon
                    color={getCategoryColor(job.category)}
                    variant="light"
                    size="sm"
                    radius="xl"
                  >
                    <FaTag size={10} />
                  </ThemeIcon>
                  <Text size="sm">{job.category}</Text>
                </Group>
              </Table.Td>
              <Table.Td>
                <Badge
                  color={getStatusBadgeColor(job.status)}
                  variant="light"
                  size="sm"
                >
                  {job.status}
                </Badge>
              </Table.Td>
              <Table.Td className="hidden lg:table-cell">
                {job.postedDate}
              </Table.Td>
              <Table.Td>
                <Group gap="xs" className="hidden sm:flex">
                  <ActionIcon
                    variant="subtle"
                    color="blue"
                    onClick={() => onView(job.id)}
                    aria-label="View job details"
                  >
                    <FaEye size={16} />
                  </ActionIcon>
                  {job.status === "pending" && (
                    <>
                      <ActionIcon
                        variant="subtle"
                        color="green"
                        onClick={() => onApprove(job.id)}
                        aria-label="Approve job"
                      >
                        <FaCheck size={16} />
                      </ActionIcon>
                      <ActionIcon
                        variant="subtle"
                        color="red"
                        onClick={() => onReject(job.id)}
                        aria-label="Reject job"
                      >
                        <FaTimes size={16} />
                      </ActionIcon>
                    </>
                  )}
                </Group>

                {/* Mobile menu */}
                <div className="sm:hidden">
                  <Menu position="bottom-end" shadow="md" width={160}>
                    <Menu.Target>
                      <div className="cursor-pointer">
                        <ActionIcon variant="subtle">
                          <FaEllipsisV size={16} />
                        </ActionIcon>
                      </div>
                    </Menu.Target>
                    <Menu.Dropdown>
                      <Menu.Item
                        leftSection={<FaEye size={14} />}
                        onClick={() => onView(job.id)}
                      >
                        View Details
                      </Menu.Item>
                      <Menu.Item leftSection={<FaEdit size={14} />}>
                        Edit Job
                      </Menu.Item>
                      {job.status === "pending" && (
                        <>
                          <Menu.Item
                            leftSection={<FaCheck size={14} />}
                            color="green"
                            onClick={() => onApprove(job.id)}
                          >
                            Approve
                          </Menu.Item>
                          <Menu.Item
                            leftSection={<FaTimes size={14} />}
                            color="red"
                            onClick={() => onReject(job.id)}
                          >
                            Reject
                          </Menu.Item>
                        </>
                      )}
                      <Menu.Divider />
                      <Menu.Item
                        leftSection={<FaTrash size={14} />}
                        color="red"
                      >
                        Delete
                      </Menu.Item>
                    </Menu.Dropdown>
                  </Menu>
                </div>
              </Table.Td>
            </Table.Tr>
          ))}
        </Table.Tbody>
      </Table>
    </div>
  );
}
