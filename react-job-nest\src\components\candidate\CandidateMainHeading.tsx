import PageHeading, {
  type PageHeadingProps,
} from "@/design-system/components/typography/PageHeading";

/**
 * @deprecated Use PageHeading from design-system/components with variant="candidate" instead
 */
export default function CandidateMainHeading({
  title,
  subtitle,
  className,
}: PageHeadingProps) {
  return (
    <PageHeading
      title={title}
      subtitle={subtitle}
      className={className}
      variant="candidate"
    />
  );
}
