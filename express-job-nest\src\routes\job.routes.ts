import express from "express";
import {
  getAllJobs,
  getJob<PERSON><PERSON><PERSON>d,
  createJob,
  updateJob,
  deleteJob,
  getJobsByCompany,
  getMyJob<PERSON>,
} from "../controllers/job.controller";
import { protect, restrictTo } from "../middleware/auth.middleware";
import { checkSubscriptionForJobPosting } from "../middleware/subscription.middleware";
import {
  createJobValidator,
  updateJobValidator,
} from "../validators/job.validator";
import { validationMiddleware } from "../validators/validation.middleware";

const router = express.Router();

// Public routes
router.get("/", getAllJobs);
router.get("/:id", getJobById);
router.get("/company/:companyId", getJobsByCompany);

// Protected routes
router.use(protect);

// Employer and admin routes
router.get("/my/jobs", getMyJobs);

// Restrict to employer and admin
router.use(restrictTo("employer", "admin"));
router.post(
  "/",
  createJobValidator,
  validationMiddleware,
  // Subscription check removed to allow job posting without subscription
  createJob
);
router.patch("/:id", updateJobValidator, validationMiddleware, updateJob);
router.delete("/:id", deleteJob);

export default router;
