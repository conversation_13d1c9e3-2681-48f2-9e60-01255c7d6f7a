{"name": "react-job-nest", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "format": "prettier . --write", "prepare": "husky"}, "dependencies": {"@mantine/charts": "^8.0.0", "@mantine/core": "^8.0.0", "@mantine/dates": "^8.0.0", "@mantine/form": "^8.0.0", "@mantine/hooks": "^8.0.0", "@mantine/notifications": "^8.0.0", "@mongez/http": "^2.2.10", "@tailwindcss/vite": "^4.1.6", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dayjs": "^1.11.13", "lucide-react": "^0.509.0", "mantine-form-zod-resolver": "^1.1.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-router": "^7.6.0", "recharts": "2", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.1.6", "zustand": "^5.0.4"}, "devDependencies": {"@eslint/js": "^9.26.0", "@types/node": "^22.15.17", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react-swc": "^3.9.0", "eslint": "^9.26.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.1.0", "husky": "^9.1.7", "lint-staged": "^16.0.0", "postcss": "^8.5.3", "postcss-preset-mantine": "^1.17.0", "postcss-simple-vars": "^7.0.1", "prettier": "^3.5.3", "prettier-plugin-organize-imports": "^4.1.0", "prettier-plugin-tailwindcss": "^0.6.11", "tw-animate-css": "^1.2.9", "typescript": "~5.8.3", "typescript-eslint": "^8.32.0", "vite": "^6.3.5"}, "lint-staged": {"**/*": "prettier --write --ignore-unknown"}}