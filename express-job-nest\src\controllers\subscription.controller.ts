import { Request, Response, NextFunction } from "express";
import SubscriptionPlan from "../models/subscription-plan.model";
import UserSubscription from "../models/user-subscription.model";
import { catchAsync } from "../utils/catch-async";
import { AppError } from "../utils/app-error";
import Job from "../models/job.model";
import Company from "../models/company.model";
import Invoice from "../models/invoice.model";
import paymentService from "../services/payment.service";
import Notification from "../models/notification.model";

// Get all subscription plans
export const getAllSubscriptionPlans = catchAsync(
  async (_req: Request, res: Response, _next: NextFunction) => {
    // Only get active plans
    const plans = await SubscriptionPlan.find({ isActive: true }).sort(
      "sortOrder"
    );

    res.status(200).json({
      success: true,
      count: plans.length,
      plans,
    });
  }
);

// Get subscription plan by ID
export const getSubscriptionPlanById = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    const plan = await SubscriptionPlan.findById(req.params.id);

    if (!plan) {
      return next(new AppError("No subscription plan found with that ID", 404));
    }

    res.status(200).json({
      success: true,
      plan,
    });
  }
);

// Create new subscription plan (admin only)
export const createSubscriptionPlan = catchAsync(
  async (req: Request, res: Response, _next: NextFunction) => {
    const newPlan = await SubscriptionPlan.create(req.body);

    res.status(201).json({
      success: true,
      plan: newPlan,
    });
  }
);

// Update subscription plan (admin only)
export const updateSubscriptionPlan = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    const plan = await SubscriptionPlan.findByIdAndUpdate(
      req.params.id,
      req.body,
      {
        new: true,
        runValidators: true,
      }
    );

    if (!plan) {
      return next(new AppError("No subscription plan found with that ID", 404));
    }

    res.status(200).json({
      success: true,
      plan,
    });
  }
);

// Delete subscription plan (admin only)
export const deleteSubscriptionPlan = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    const plan = await SubscriptionPlan.findById(req.params.id);

    if (!plan) {
      return next(new AppError("No subscription plan found with that ID", 404));
    }

    // Instead of deleting, mark as inactive
    plan.isActive = false;
    await plan.save();

    res.status(204).json({
      success: true,
      data: null,
    });
  }
);

// Subscribe to a plan
export const subscribeToPlan = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    const { planId, billingPeriod, paymentMethod } = req.body;
    let { transactionId } = req.body;

    // Check if the plan exists
    const plan = await SubscriptionPlan.findById(planId);
    if (!plan || !plan.isActive) {
      return next(new AppError("Invalid or inactive subscription plan", 400));
    }

    // Calculate start and end dates
    const startDate = new Date();
    const endDate = new Date();
    if (billingPeriod === "month") {
      endDate.setMonth(endDate.getMonth() + 1);
    } else {
      endDate.setFullYear(endDate.getFullYear() + 1);
    }

    // Get the price based on billing period
    const price = billingPeriod === "month" ? plan.price : plan.annualPrice;

    // Check if user already has an active subscription
    const existingSubscription = await UserSubscription.findOne({
      user: (req.user as any)._id,
      status: "active",
    });

    // Process payment if not using an existing transaction ID
    let paymentResult;
    if (!transactionId) {
      // Prepare payment data
      const paymentData = {
        amount: price,
        currency: plan.currency,
        description: `Subscription to ${plan.name} plan (${billingPeriod})`,
        customer: {
          name: (req.user as any).name,
          email: (req.user as any).email,
        },
        metadata: {
          planId: (plan as any)._id?.toString(),
          billingPeriod,
          userId: (req.user as any)._id.toString(),
        },
      };

      // Process payment
      paymentResult = await paymentService.processPayment(
        paymentMethod as any,
        paymentData
      );

      if (!paymentResult.success) {
        return next(
          new AppError(
            `Payment failed: ${paymentResult.message || "Unknown error"}`,
            400
          )
        );
      }

      // Use the transaction ID from the payment result
      transactionId = paymentResult.transactionId;
    }

    // If payment is pending, return the payment URL
    if (
      paymentResult &&
      paymentResult.status === "pending" &&
      paymentResult.paymentUrl
    ) {
      return res.status(200).json({
        success: true,
        redirectUrl: paymentResult.paymentUrl,
        message: "Please complete the payment process",
        transactionId: paymentResult.transactionId,
      });
    }

    // If we have an existing subscription, cancel it
    if (existingSubscription) {
      // Cancel the existing subscription
      existingSubscription.status = "canceled";
      existingSubscription.canceledAt = new Date();
      await existingSubscription.save();
    }

    // Create new subscription
    const newSubscription = await UserSubscription.create({
      user: (req.user as any)._id,
      plan: planId,
      status:
        paymentResult && paymentResult.status === "pending"
          ? "pending"
          : "active",
      startDate,
      endDate,
      billingPeriod,
      autoRenew: true,
      paymentMethod,
      transactionId,
      price,
      currency: plan.currency,
      usageStats: {
        jobsPosted: 0,
        cvViews: 0,
        featuredJobsUsed: 0,
      },
    });

    // Create invoice
    const invoice = await Invoice.create({
      user: (req.user as any)._id,
      subscription: newSubscription._id,
      amount: price,
      currency: plan.currency,
      description: `Subscription to ${plan.name} plan (${billingPeriod})`,
      status: paymentResult ? paymentResult.status : "completed",
      paymentMethod,
      transactionId,
      paymentDate:
        paymentResult && paymentResult.status === "completed"
          ? new Date()
          : undefined,
      dueDate: new Date(startDate.getTime() + 7 * 24 * 60 * 60 * 1000), // 7 days from start date
      items: [
        {
          name: plan.name,
          description: `${
            billingPeriod === "month" ? "Monthly" : "Annual"
          } subscription`,
          quantity: 1,
          unitPrice: price,
          total: price,
        },
      ],
      subtotal: price,
      total: price,
      billingDetails: {
        name: (req.user as any).name,
        email: (req.user as any).email,
      },
    });

    // Send notification to user
    try {
      // Create notification data
      await Notification.create({
        recipient: (req.user as any)._id,
        type: "subscription_renewed",
        title: "Subscription Activated",
        message: `Your subscription to the ${plan.name} plan has been activated.`,
        relatedEntities: {
          subscription: newSubscription._id,
        },
        metadata: {
          planName: plan.name,
          billingPeriod,
          price: `${plan.currency} ${price}`,
          endDate: endDate.toISOString(),
        },
        isEmailSent: false, // Email sending would be handled separately
      });
    } catch (error) {
      console.error("Failed to send notification:", error);
      // Continue execution even if notification fails
    }

    res.status(201).json({
      success: true,
      subscription: newSubscription,
      invoice,
    });
  }
);

// Get current user's subscription
export const getCurrentSubscription = catchAsync(
  async (req: Request, res: Response, _next: NextFunction) => {
    const subscription = await UserSubscription.findOne({
      user: req.user._id,
      status: "active",
    }).populate("plan");

    res.status(200).json({
      success: true,
      subscription: subscription || null,
    });
  }
);

// Cancel subscription
export const cancelSubscription = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    const subscription = await UserSubscription.findOne({
      user: req.user._id,
      status: "active",
    });

    if (!subscription) {
      return next(new AppError("No active subscription found", 404));
    }

    subscription.status = "canceled";
    subscription.canceledAt = new Date();
    subscription.autoRenew = false;
    await subscription.save();

    res.status(200).json({
      success: true,
      subscription,
    });
  }
);

// Check if user can post a new job
export const checkJobPostingEligibility = catchAsync(
  async (req: Request, res: Response, _next: NextFunction) => {
    // SUBSCRIPTION CHECK DISABLED: Always allow job posting
    console.log(
      "Job posting eligibility check bypassed - always returning true"
    );
    return res.status(200).json({
      success: true,
      canPostJob: true,
      reason: null,
      subscription: null,
      activeJobsCount: 0,
      limit: "unlimited",
      subscriptionDisabled: true,
    });

    /* Original code commented out
    // In development mode, always allow job posting
    if (process.env.NODE_ENV === "development") {
      return res.status(200).json({
        success: true,
        canPostJob: true,
        reason: null,
        subscription: null,
        activeJobsCount: 0,
        limit: "unlimited",
        devMode: true,
      });
    }

    // Get user's active subscription
    const subscription = await UserSubscription.findOne({
      user: req.user._id,
      status: "active",
    }).populate("plan");

    if (!subscription) {
      return res.status(200).json({
        success: true,
        canPostJob: false,
        reason: "No active subscription",
        subscription: null,
      });
    }

    // Get companies owned by the user
    const companies = await Company.find({ owner: req.user._id });
    const companyIds = companies.map((company) => company._id);

    // Count user's active jobs
    const activeJobsCount = await Job.countDocuments({
      company: { $in: companyIds },
      isActive: true,
    });

    // Check if user has reached the limit
    const plan = subscription.plan as any; // Type assertion for TypeScript
    const canPostJob =
      plan.features.activeJobs === "unlimited" ||
      activeJobsCount < plan.features.activeJobs;

    res.status(200).json({
      success: true,
      canPostJob,
      reason: canPostJob ? null : "Job posting limit reached",
      subscription,
      activeJobsCount,
      limit: plan.features.activeJobs,
    });
    */
  }
);
