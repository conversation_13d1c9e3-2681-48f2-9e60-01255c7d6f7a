{"version": 3, "sources": ["../../@mantine/store/src/store.ts", "../../@mantine/notifications/src/notifications.store.ts", "../../@mantine/notifications/src/get-grouped-notifications/get-grouped-notifications.ts", "../../@mantine/notifications/src/get-notification-state-styles.ts", "../../@mantine/notifications/src/get-auto-close/get-auto-close.ts", "../../@mantine/notifications/src/NotificationContainer.tsx", "../../@mantine/notifications/esm/Notifications.module.css.mjs", "../../@mantine/notifications/src/Notifications.tsx"], "sourcesContent": ["import { useSyncExternalStore } from 'react';\n\nexport type MantineStoreSubscriber<Value> = (value: Value) => void;\ntype SetStateCallback<Value> = (value: Value) => Value;\n\nexport interface MantineStore<Value> {\n  getState: () => Value;\n  setState: (value: Value | SetStateCallback<Value>) => void;\n  updateState: (value: Value | SetStateCallback<Value>) => void;\n  initialize: (value: Value) => void;\n  subscribe: (callback: MantineStoreSubscriber<Value>) => () => void;\n}\n\nexport type MantineStoreValue<Store extends MantineStore<any>> = ReturnType<Store['getState']>;\n\nexport function createStore<Value extends Record<string, any>>(\n  initialState: Value\n): MantineStore<Value> {\n  let state = initialState;\n  let initialized = false;\n  const listeners = new Set<MantineStoreSubscriber<Value>>();\n\n  return {\n    getState() {\n      return state;\n    },\n\n    updateState(value) {\n      state = typeof value === 'function' ? value(state) : value;\n    },\n\n    setState(value) {\n      this.updateState(value);\n      listeners.forEach((listener) => listener(state));\n    },\n\n    initialize(value) {\n      if (!initialized) {\n        state = value;\n        initialized = true;\n      }\n    },\n\n    subscribe(callback) {\n      listeners.add(callback);\n      return () => listeners.delete(callback);\n    },\n  };\n}\n\nexport function useStore<Store extends MantineStore<any>>(store: Store) {\n  return useSyncExternalStore<MantineStoreValue<Store>>(\n    store.subscribe,\n    () => store.getState(),\n    () => store.getState()\n  );\n}\n", "import { NotificationProps } from '@mantine/core';\nimport { randomId } from '@mantine/hooks';\nimport { createStore, MantineStore, useStore } from '@mantine/store';\n\nexport type NotificationPosition =\n  | 'top-left'\n  | 'top-right'\n  | 'top-center'\n  | 'bottom-left'\n  | 'bottom-right'\n  | 'bottom-center';\n\nexport interface NotificationData\n  extends Omit<NotificationProps, 'onClose'>,\n    Record<`data-${string}`, any> {\n  /** Notification id, can be used to close or update notification */\n  id?: string;\n\n  /** Position of the notification, if not set, the position is determined based on `position` prop on Notifications component */\n  position?: NotificationPosition;\n\n  /** Notification message, required for all notifications */\n  message: React.ReactNode;\n\n  /** Determines whether notification should be closed automatically,\n   *  number is auto close timeout in ms, overrides `autoClose` from `Notifications`\n   * */\n  autoClose?: boolean | number;\n\n  /** Called when notification closes */\n  onClose?: (props: NotificationData) => void;\n\n  /** Called when notification opens */\n  onOpen?: (props: NotificationData) => void;\n}\n\nexport interface NotificationsState {\n  notifications: NotificationData[];\n  queue: NotificationData[];\n  defaultPosition: NotificationPosition;\n  limit: number;\n}\n\nexport type NotificationsStore = MantineStore<NotificationsState>;\n\nfunction getDistributedNotifications(\n  data: NotificationData[],\n  defaultPosition: NotificationPosition,\n  limit: number\n) {\n  const queue: NotificationData[] = [];\n  const notifications: NotificationData[] = [];\n  const count: Record<string, number> = {};\n\n  for (const item of data) {\n    const position = item.position || defaultPosition;\n    count[position] = count[position] || 0;\n    count[position] += 1;\n\n    if (count[position] <= limit) {\n      notifications.push(item);\n    } else {\n      queue.push(item);\n    }\n  }\n\n  return { notifications, queue };\n}\n\nexport const createNotificationsStore = () =>\n  createStore<NotificationsState>({\n    notifications: [],\n    queue: [],\n    defaultPosition: 'bottom-right',\n    limit: 5,\n  });\n\nexport const notificationsStore = createNotificationsStore();\nexport const useNotifications = (store: NotificationsStore = notificationsStore) => useStore(store);\n\nexport function updateNotificationsState(\n  store: NotificationsStore,\n  update: (notifications: NotificationData[]) => NotificationData[]\n) {\n  const state = store.getState();\n  const notifications = update([...state.notifications, ...state.queue]);\n  const updated = getDistributedNotifications(notifications, state.defaultPosition, state.limit);\n\n  store.setState({\n    notifications: updated.notifications,\n    queue: updated.queue,\n    limit: state.limit,\n    defaultPosition: state.defaultPosition,\n  });\n}\n\nexport function showNotification(\n  notification: NotificationData,\n  store: NotificationsStore = notificationsStore\n) {\n  const id = notification.id || randomId();\n\n  updateNotificationsState(store, (notifications) => {\n    if (notification.id && notifications.some((n) => n.id === notification.id)) {\n      return notifications;\n    }\n\n    return [...notifications, { ...notification, id }];\n  });\n\n  return id;\n}\n\nexport function hideNotification(id: string, store: NotificationsStore = notificationsStore) {\n  updateNotificationsState(store, (notifications) =>\n    notifications.filter((notification) => {\n      if (notification.id === id) {\n        notification.onClose?.(notification);\n        return false;\n      }\n\n      return true;\n    })\n  );\n\n  return id;\n}\n\nexport function updateNotification(\n  notification: NotificationData,\n  store: NotificationsStore = notificationsStore\n) {\n  updateNotificationsState(store, (notifications) =>\n    notifications.map((item) => {\n      if (item.id === notification.id) {\n        return { ...item, ...notification };\n      }\n\n      return item;\n    })\n  );\n\n  return notification.id;\n}\n\nexport function cleanNotifications(store: NotificationsStore = notificationsStore) {\n  updateNotificationsState(store, () => []);\n}\n\nexport function cleanNotificationsQueue(store: NotificationsStore = notificationsStore) {\n  updateNotificationsState(store, (notifications) =>\n    notifications.slice(0, store.getState().limit)\n  );\n}\n\nexport const notifications = {\n  show: showNotification,\n  hide: hideNotification,\n  update: updateNotification,\n  clean: cleanNotifications,\n  cleanQueue: cleanNotificationsQueue,\n  updateState: updateNotificationsState,\n} as const;\n", "import { NotificationData, NotificationPosition } from '../notifications.store';\n\nexport type GroupedNotifications = Record<NotificationPosition, NotificationData[]>;\n\nexport const positions: NotificationPosition[] = [\n  'bottom-center',\n  'bottom-left',\n  'bottom-right',\n  'top-center',\n  'top-left',\n  'top-right',\n];\n\nexport function getGroupedNotifications(\n  notifications: NotificationData[],\n  defaultPosition: NotificationPosition\n) {\n  return notifications.reduce<GroupedNotifications>(\n    (acc, notification) => {\n      acc[notification.position || defaultPosition].push(notification);\n      return acc;\n    },\n    positions.reduce<GroupedNotifications>((acc, item) => {\n      acc[item] = [];\n      return acc;\n    }, {} as GroupedNotifications)\n  );\n}\n", "import { TransitionStatus } from 'react-transition-group';\nimport type { NotificationsProps } from './Notifications';\n\ninterface NotificationStateStylesProps {\n  state: TransitionStatus;\n  maxHeight: number | string;\n  position: NotificationsProps['position'];\n  transitionDuration: number;\n}\n\nconst transforms = {\n  left: 'translateX(-100%)',\n  right: 'translateX(100%)',\n  'top-center': 'translateY(-100%)',\n  'bottom-center': 'translateY(100%)',\n};\n\nconst noTransform = {\n  left: 'translateX(0)',\n  right: 'translateX(0)',\n  'top-center': 'translateY(0)',\n  'bottom-center': 'translateY(0)',\n};\n\nexport function getNotificationStateStyles({\n  state,\n  maxHeight,\n  position,\n  transitionDuration,\n}: NotificationStateStylesProps): React.CSSProperties {\n  const [vertical, horizontal] = position!.split('-');\n  const property = (\n    horizontal === 'center' ? `${vertical}-center` : horizontal\n  ) as keyof typeof transforms;\n\n  const commonStyles: React.CSSProperties = {\n    opacity: 0,\n    maxHeight,\n    transform: transforms[property],\n    transitionDuration: `${transitionDuration}ms, ${transitionDuration}ms, ${transitionDuration}ms`,\n    transitionTimingFunction: 'cubic-bezier(.51,.3,0,1.21), cubic-bezier(.51,.3,0,1.21), linear',\n    transitionProperty: 'opacity, transform, max-height',\n  };\n\n  const inState: React.CSSProperties = {\n    opacity: 1,\n    transform: noTransform[property],\n  };\n\n  const outState: React.CSSProperties = {\n    opacity: 0,\n    maxHeight: 0,\n    transform: transforms[property],\n  };\n\n  const transitionStyles = {\n    entering: inState,\n    entered: inState,\n    exiting: outState,\n    exited: outState,\n  };\n\n  return { ...commonStyles, ...transitionStyles[state as keyof typeof transitionStyles] };\n}\n", "export function getAutoClose(\n  autoClose: boolean | number | undefined,\n  notificationAutoClose: boolean | number | undefined\n) {\n  if (typeof notificationAutoClose === 'number') {\n    return notificationAutoClose;\n  }\n\n  if (notificationAutoClose === false || autoClose === false) {\n    return false;\n  }\n\n  return autoClose;\n}\n", "import { forwardRef, useEffect, useRef } from 'react';\nimport { Notification, NotificationProps } from '@mantine/core';\nimport { getAutoClose } from './get-auto-close/get-auto-close';\nimport { NotificationData } from './notifications.store';\n\ninterface NotificationContainerProps extends NotificationProps {\n  data: NotificationData;\n  onHide: (id: string) => void;\n  autoClose: number | false;\n}\n\nexport const NotificationContainer = forwardRef<HTMLDivElement, NotificationContainerProps>(\n  ({ data, onHide, autoClose, ...others }, ref) => {\n    const { autoClose: _autoClose, message, ...notificationProps } = data;\n    const autoCloseDuration = getAutoClose(autoClose, data.autoClose);\n    const autoCloseTimeout = useRef<number>(-1);\n\n    const cancelAutoClose = () => window.clearTimeout(autoCloseTimeout.current);\n\n    const handleHide = () => {\n      onHide(data.id!);\n      cancelAutoClose();\n    };\n\n    const handleAutoClose = () => {\n      if (typeof autoCloseDuration === 'number') {\n        autoCloseTimeout.current = window.setTimeout(handleHide, autoCloseDuration);\n      }\n    };\n\n    useEffect(() => {\n      data.onOpen?.(data);\n    }, []);\n\n    useEffect(() => {\n      handleAutoClose();\n      return cancelAutoClose;\n    }, [autoCloseDuration]);\n\n    return (\n      <Notification\n        {...others}\n        {...notificationProps}\n        onClose={handleHide}\n        ref={ref}\n        onMouseEnter={cancelAutoClose}\n        onMouseLeave={handleAutoClose}\n      >\n        {message}\n      </Notification>\n    );\n  }\n);\n\nNotificationContainer.displayName = '@mantine/notifications/NotificationContainer';\n", "'use client';\nvar classes = {\"root\":\"m_b37d9ac7\",\"notification\":\"m_5ed0edd0\"};\n\nexport { classes as default };\n//# sourceMappingURL=Notifications.module.css.mjs.map\n", "import { useEffect, useRef } from 'react';\nimport {\n  Transition as _Transition,\n  TransitionGroup,\n  TransitionStatus,\n} from 'react-transition-group';\nimport {\n  Box,\n  BoxProps,\n  createVarsResolver,\n  ElementProps,\n  factory,\n  Factory,\n  getDefaultZIndex,\n  OptionalPortal,\n  PortalProps,\n  rem,\n  RemoveScroll,\n  StylesApiProps,\n  useMantineTheme,\n  useProps,\n  useStyles,\n} from '@mantine/core';\nimport { useDidUpdate, useForceUpdate, useReducedMotion } from '@mantine/hooks';\nimport {\n  getGroupedNotifications,\n  positions,\n} from './get-grouped-notifications/get-grouped-notifications';\nimport { getNotificationStateStyles } from './get-notification-state-styles';\nimport { NotificationContainer } from './NotificationContainer';\nimport {\n  hideNotification,\n  NotificationPosition,\n  notifications,\n  NotificationsStore,\n  notificationsStore,\n  useNotifications,\n} from './notifications.store';\nimport classes from './Notifications.module.css';\n\nconst Transition: any = _Transition;\n\nexport type NotificationsStylesNames = 'root' | 'notification';\nexport type NotificationsCssVariables = {\n  root: '--notifications-z-index' | '--notifications-container-width';\n};\n\nexport interface NotificationsProps\n  extends BoxProps,\n    StylesApiProps<NotificationsFactory>,\n    ElementProps<'div'> {\n  /** Notifications default position, `'bottom-right'` by default */\n  position?: NotificationPosition;\n\n  /** Auto close timeout for all notifications in ms, `false` to disable auto close, can be overwritten for individual notifications in `notifications.show` function, `4000` by default */\n  autoClose?: number | false;\n\n  /** Notification transition duration in ms, `250` by default */\n  transitionDuration?: number;\n\n  /** Notification width, cannot exceed 100%, `440` by default */\n  containerWidth?: number | string;\n\n  /** Notification `max-height`, used for transitions, `200` by default */\n  notificationMaxHeight?: number | string;\n\n  /** Maximum number of notifications displayed at a time, other new notifications will be added to queue, `5` by default */\n  limit?: number;\n\n  /** Notifications container z-index, `400` by default */\n  zIndex?: string | number;\n\n  /** Props passed down to the `Portal` component */\n  portalProps?: Omit<PortalProps, 'children'>;\n\n  /** Store for notifications state, can be used to create multiple instances of notifications system in your application */\n  store?: NotificationsStore;\n\n  /** Determines whether notifications container should be rendered inside `Portal`, `true` by default */\n  withinPortal?: boolean;\n}\n\nexport type NotificationsFactory = Factory<{\n  props: NotificationsProps;\n  ref: HTMLDivElement;\n  stylesNames: NotificationsStylesNames;\n  vars: NotificationsCssVariables;\n  staticComponents: {\n    show: typeof notifications.show;\n    hide: typeof notifications.hide;\n    update: typeof notifications.update;\n    clean: typeof notifications.clean;\n    cleanQueue: typeof notifications.cleanQueue;\n    updateState: typeof notifications.updateState;\n  };\n}>;\n\nconst defaultProps: Partial<NotificationsProps> = {\n  position: 'bottom-right',\n  autoClose: 4000,\n  transitionDuration: 250,\n  containerWidth: 440,\n  notificationMaxHeight: 200,\n  limit: 5,\n  zIndex: getDefaultZIndex('overlay'),\n  store: notificationsStore,\n  withinPortal: true,\n};\n\nconst varsResolver = createVarsResolver<NotificationsFactory>((_, { zIndex, containerWidth }) => ({\n  root: {\n    '--notifications-z-index': zIndex?.toString(),\n    '--notifications-container-width': rem(containerWidth),\n  },\n}));\n\nexport const Notifications = factory<NotificationsFactory>((_props, ref) => {\n  const props = useProps('Notifications', defaultProps, _props);\n  const {\n    classNames,\n    className,\n    style,\n    styles,\n    unstyled,\n    vars,\n    position,\n    autoClose,\n    transitionDuration,\n    containerWidth,\n    notificationMaxHeight,\n    limit,\n    zIndex,\n    store,\n    portalProps,\n    withinPortal,\n    ...others\n  } = props;\n\n  const theme = useMantineTheme();\n  const data = useNotifications(store);\n  const forceUpdate = useForceUpdate();\n  const shouldReduceMotion = useReducedMotion();\n  const refs = useRef<Record<string, HTMLDivElement>>({});\n  const previousLength = useRef<number>(0);\n\n  const reduceMotion = theme.respectReducedMotion ? shouldReduceMotion : false;\n  const duration = reduceMotion ? 1 : transitionDuration;\n\n  const getStyles = useStyles<NotificationsFactory>({\n    name: 'Notifications',\n    classes,\n    props,\n    className,\n    style,\n    classNames,\n    styles,\n    unstyled,\n    vars,\n    varsResolver,\n  });\n\n  useEffect(() => {\n    store?.updateState((current) => ({\n      ...current,\n      limit: limit || 5,\n      defaultPosition: position!,\n    }));\n  }, [limit, position]);\n\n  useDidUpdate(() => {\n    if (data.notifications.length > previousLength.current) {\n      setTimeout(() => forceUpdate(), 0);\n    }\n    previousLength.current = data.notifications.length;\n  }, [data.notifications]);\n\n  const grouped = getGroupedNotifications(data.notifications, position!);\n  const groupedComponents = positions.reduce(\n    (acc, pos) => {\n      acc[pos] = grouped[pos].map(({ style: notificationStyle, ...notification }) => (\n        <Transition\n          key={notification.id}\n          timeout={duration}\n          onEnter={() => refs.current[notification.id!].offsetHeight}\n          nodeRef={{ current: refs.current[notification.id!] }}\n        >\n          {(state: TransitionStatus) => (\n            <NotificationContainer\n              ref={(node) => {\n                refs.current[notification.id!] = node!;\n              }}\n              data={notification}\n              onHide={(id) => hideNotification(id, store)}\n              autoClose={autoClose!}\n              {...getStyles('notification', {\n                style: {\n                  ...getNotificationStateStyles({\n                    state,\n                    position: pos,\n                    transitionDuration: duration!,\n                    maxHeight: notificationMaxHeight!,\n                  }),\n                  ...notificationStyle,\n                },\n              })}\n            />\n          )}\n        </Transition>\n      ));\n\n      return acc;\n    },\n    {} as Record<NotificationPosition, React.ReactNode>\n  );\n\n  return (\n    <OptionalPortal withinPortal={withinPortal} {...portalProps}>\n      <Box {...getStyles('root')} data-position=\"top-center\" ref={ref} {...others}>\n        <TransitionGroup>{groupedComponents['top-center']}</TransitionGroup>\n      </Box>\n\n      <Box {...getStyles('root')} data-position=\"top-left\" {...others}>\n        <TransitionGroup>{groupedComponents['top-left']}</TransitionGroup>\n      </Box>\n\n      <Box\n        {...getStyles('root', { className: RemoveScroll.classNames.fullWidth })}\n        data-position=\"top-right\"\n        {...others}\n      >\n        <TransitionGroup>{groupedComponents['top-right']}</TransitionGroup>\n      </Box>\n\n      <Box\n        {...getStyles('root', { className: RemoveScroll.classNames.fullWidth })}\n        data-position=\"bottom-right\"\n        {...others}\n      >\n        <TransitionGroup>{groupedComponents['bottom-right']}</TransitionGroup>\n      </Box>\n\n      <Box {...getStyles('root')} data-position=\"bottom-left\" {...others}>\n        <TransitionGroup>{groupedComponents['bottom-left']}</TransitionGroup>\n      </Box>\n\n      <Box {...getStyles('root')} data-position=\"bottom-center\" {...others}>\n        <TransitionGroup>{groupedComponents['bottom-center']}</TransitionGroup>\n      </Box>\n    </OptionalPortal>\n  );\n});\n\nNotifications.classes = classes;\nNotifications.displayName = '@mantine/notifications/Notifications';\nNotifications.show = notifications.show;\nNotifications.hide = notifications.hide;\nNotifications.update = notifications.update;\nNotifications.clean = notifications.clean;\nNotifications.cleanQueue = notifications.cleanQueue;\nNotifications.updateState = notifications.updateState;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAeO,SAAS,YACd,cACqB;AACrB,MAAI,QAAQ;AACZ,MAAI,cAAc;AACZ,QAAA,YAAA,oBAAgB,IAAmC;AAElD,SAAA;IACL,WAAW;AACF,aAAA;IACT;IAEA,YAAY,OAAO;AACjB,cAAQ,OAAO,UAAU,aAAa,MAAM,KAAK,IAAI;IACvD;IAEA,SAAS,OAAO;AACd,WAAK,YAAY,KAAK;AACtB,gBAAU,QAAQ,CAAC,aAAa,SAAS,KAAK,CAAC;IACjD;IAEA,WAAW,OAAO;AAChB,UAAI,CAAC,aAAa;AACR,gBAAA;AACM,sBAAA;MAAA;IAElB;IAEA,UAAU,UAAU;AAClB,gBAAU,IAAI,QAAQ;AACf,aAAA,MAAM,UAAU,OAAO,QAAQ;IAAA;EAE1C;AACF;AAEO,SAAS,SAA0C,OAAc;AAC/D,aAAA;IACL,MAAM;IACN,MAAM,MAAM,SAAS;IACrB,MAAM,MAAM,SAAS;EACvB;AACF;;;ACXA,SAAS,4BACP,MACA,iBACA,OACA;AACA,QAAM,QAA4B,CAAC;AACnC,QAAMA,iBAAoC,CAAC;AAC3C,QAAM,QAAgC,CAAC;AAEvC,aAAW,QAAQ,MAAM;AACjB,UAAA,WAAW,KAAK,YAAY;AAClC,UAAM,QAAQ,IAAI,MAAM,QAAQ,KAAK;AACrC,UAAM,QAAQ,KAAK;AAEf,QAAA,MAAM,QAAQ,KAAK,OAAO;AAC5BA,qBAAc,KAAK,IAAI;IAAA,OAClB;AACL,YAAM,KAAK,IAAI;IAAA;EACjB;AAGK,SAAA,EAAE,eAAAA,gBAAe,MAAM;AAChC;AAEa,IAAA,2BAA2B,MACtC,YAAgC;EAC9B,eAAe,CAAC;EAChB,OAAO,CAAC;EACR,iBAAiB;EACjB,OAAO;AACT,CAAC;AAEI,IAAM,qBAAqB,yBAAyB;AACpD,IAAM,mBAAmB,CAAC,QAA4B,uBAAuB,SAAS,KAAK;AAElF,SAAA,yBACd,OACA,QACA;AACM,QAAA,QAAQ,MAAM,SAAS;AACvBA,QAAAA,iBAAgB,OAAO,CAAC,GAAG,MAAM,eAAe,GAAG,MAAM,KAAK,CAAC;AACrE,QAAM,UAAU,4BAA4BA,gBAAe,MAAM,iBAAiB,MAAM,KAAK;AAE7F,QAAM,SAAS;IACb,eAAe,QAAQ;IACvB,OAAO,QAAQ;IACf,OAAO,MAAM;IACb,iBAAiB,MAAM;EAAA,CACxB;AACH;AAEgB,SAAA,iBACd,cACA,QAA4B,oBAC5B;AACM,QAAA,KAAK,aAAa,MAAM,SAAS;AAEd,2BAAA,OAAO,CAACA,mBAAkB;AAC7C,QAAA,aAAa,MAAMA,eAAc,KAAK,CAAC,MAAM,EAAE,OAAO,aAAa,EAAE,GAAG;AACnEA,aAAAA;IAAA;AAGT,WAAO,CAAC,GAAGA,gBAAe,EAAE,GAAG,cAAc,GAAA,CAAI;EAAA,CAClD;AAEM,SAAA;AACT;AAEgB,SAAA,iBAAiB,IAAY,QAA4B,oBAAoB;AAC3F;IAAyB;IAAO,CAACA,mBAC/BA,eAAc,OAAO,CAAC,iBAAiB;;AACjC,UAAA,aAAa,OAAO,IAAI;AAC1B,2BAAa,YAAb,sCAAuB;AAChB,eAAA;MAAA;AAGF,aAAA;IACR,CAAA;EACH;AAEO,SAAA;AACT;AAEgB,SAAA,mBACd,cACA,QAA4B,oBAC5B;AACA;IAAyB;IAAO,CAACA,mBAC/BA,eAAc,IAAI,CAAC,SAAS;AACtB,UAAA,KAAK,OAAO,aAAa,IAAI;AAC/B,eAAO,EAAE,GAAG,MAAM,GAAG,aAAa;MAAA;AAG7B,aAAA;IACR,CAAA;EACH;AAEA,SAAO,aAAa;AACtB;AAEgB,SAAA,mBAAmB,QAA4B,oBAAoB;AACxD,2BAAA,OAAO,MAAM,CAAA,CAAE;AAC1C;AAEgB,SAAA,wBAAwB,QAA4B,oBAAoB;AACtF;IAAyB;IAAO,CAACA,mBAC/BA,eAAc,MAAM,GAAG,MAAM,SAAA,EAAW,KAAK;EAC/C;AACF;AAEO,IAAM,gBAAgB;EAC3B,MAAM;EACN,MAAM;EACN,QAAQ;EACR,OAAO;EACP,YAAY;EACZ,aAAa;AACf;A;;;;;;AC9JO,IAAM,YAAoC;EAC/C;EACA;EACA;EACA;EACA;EACA;AACF;AAEgB,SAAA,wBACdC,gBACA,iBACA;AACA,SAAOA,eAAc;IACnB,CAAC,KAAK,iBAAiB;AACrB,UAAI,aAAa,YAAY,eAAe,EAAE,KAAK,YAAY;AACxD,aAAA;IACT;IACA,UAAU,OAA6B,CAAC,KAAK,SAAS;AAChD,UAAA,IAAI,IAAI,CAAC;AACN,aAAA;IAAA,GACN,CAA0B,CAAA;EAC/B;AACF;;;ACjBA,IAAM,aAAa;EACjB,MAAM;EACN,OAAO;EACP,cAAc;EACd,iBAAiB;AACnB;AAEA,IAAM,cAAc;EAClB,MAAM;EACN,OAAO;EACP,cAAc;EACd,iBAAiB;AACnB;AAEO,SAAS,2BAA2B;EACzC;EACA;EACA;EACA;AACF,GAAsD;AACpD,QAAM,CAAC,UAAU,UAAU,IAAI,SAAU,MAAM,GAAG;AAClD,QAAM,WACJ,eAAe,WAAW,GAAG,QAAQ,YAAY;AAGnD,QAAM,eAAoC;IACxC,SAAS;IACT;IACA,WAAW,WAAW,QAAQ;IAC9B,oBAAoB,GAAG,kBAAkB,OAAO,kBAAkB,OAAO,kBAAkB;IAC3F,0BAA0B;IAC1B,oBAAoB;EACtB;AAEA,QAAM,UAA+B;IACnC,SAAS;IACT,WAAW,YAAY,QAAQ;EACjC;AAEA,QAAM,WAAgC;IACpC,SAAS;IACT,WAAW;IACX,WAAW,WAAW,QAAQ;EAChC;AAEA,QAAM,mBAAmB;IACvB,UAAU;IACV,SAAS;IACT,SAAS;IACT,QAAQ;EACV;AAEA,SAAO,EAAE,GAAG,cAAc,GAAG,iBAAiB,KAAsC,EAAE;AACxF;A;;;;;;AC/DgB,SAAA,aACd,WACA,uBACA;AACI,MAAA,OAAO,0BAA0B,UAAU;AACtC,WAAA;EAAA;AAGL,MAAA,0BAA0B,SAAS,cAAc,OAAO;AACnD,WAAA;EAAA;AAGF,SAAA;AACT;;;ACFO,IAAM,4BAAwB;EACnC,CAAC,EAAE,MAAM,QAAQ,WAAW,GAAG,OAAA,GAAU,QAAQ;AAC/C,UAAM,EAAE,WAAW,YAAY,SAAS,GAAG,kBAAsB,IAAA;AACjE,UAAM,oBAAoB,aAAa,WAAW,KAAK,SAAS;AAC1D,UAAA,uBAAmB,sBAAe,EAAE;AAE1C,UAAM,kBAAkB,MAAM,OAAO,aAAa,iBAAiB,OAAO;AAE1E,UAAM,aAAa,MAAM;AACvB,aAAO,KAAK,EAAG;AACC,sBAAA;IAClB;AAEA,UAAM,kBAAkB,MAAM;AACxB,UAAA,OAAO,sBAAsB,UAAU;AACzC,yBAAiB,UAAU,OAAO,WAAW,YAAY,iBAAiB;MAAA;IAE9E;AAEA,iCAAU,MAAM;;AACd,iBAAK,WAAL,8BAAc;IAChB,GAAG,CAAA,CAAE;AAEL,iCAAU,MAAM;AACE,sBAAA;AACT,aAAA;IAAA,GACN,CAAC,iBAAiB,CAAC;AAGpB,eAAA;MAAC;MAAA;QACE,GAAG;QACH,GAAG;QACJ,SAAS;QACT;QACA,cAAc;QACd,cAAc;QAEb,UAAA;MAAA;IACH;EAAA;AAGN;AAEA,sBAAsB,cAAc;;;ACrDpC,IAAI,UAAU,EAAC,QAAO,cAAa,gBAAe,aAAY;;;ACuC9D,IAAM,aAAkBC;AAyDxB,IAAM,eAA4C;EAChD,UAAU;EACV,WAAW;EACX,oBAAoB;EACpB,gBAAgB;EAChB,uBAAuB;EACvB,OAAO;EACP,QAAQ,iBAAiB,SAAS;EAClC,OAAO;EACP,cAAc;AAChB;AAEA,IAAM,eAAe,mBAAyC,CAAC,GAAG,EAAE,QAAQ,eAAA,OAAsB;EAChG,MAAM;IACJ,2BAA2B,iCAAQ;IACnC,mCAAmC,IAAI,cAAc;EAAA;AAEzD,EAAE;AAEK,IAAM,gBAAgB,QAA8B,CAAC,QAAQ,QAAQ;AAC1E,QAAM,QAAQ,SAAS,iBAAiB,cAAc,MAAM;AACtD,QAAA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,GAAG;EAAA,IACD;AAEJ,QAAM,QAAQ,gBAAgB;AACxB,QAAA,OAAO,iBAAiB,KAAK;AACnC,QAAM,cAAc,eAAe;AACnC,QAAM,qBAAqB,iBAAiB;AACtC,QAAA,WAAO,sBAAuC,CAAA,CAAE;AAChD,QAAA,qBAAiB,sBAAe,CAAC;AAEjC,QAAA,eAAe,MAAM,uBAAuB,qBAAqB;AACjE,QAAA,WAAW,eAAe,IAAI;AAEpC,QAAM,YAAY,UAAgC;IAChD,MAAM;IACN;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EAAA,CACD;AAED,+BAAU,MAAM;AACP,mCAAA,YAAY,CAAC,aAAa;MAC/B,GAAG;MACH,OAAO,SAAS;MAChB,iBAAiB;IAAA;EACjB,GACD,CAAC,OAAO,QAAQ,CAAC;AAEpB,eAAa,MAAM;AACjB,QAAI,KAAK,cAAc,SAAS,eAAe,SAAS;AAC3C,iBAAA,MAAM,YAAY,GAAG,CAAC;IAAA;AAEpB,mBAAA,UAAU,KAAK,cAAc;EAAA,GAC3C,CAAC,KAAK,aAAa,CAAC;AAEvB,QAAM,UAAU,wBAAwB,KAAK,eAAe,QAAS;AACrE,QAAM,oBAAoB,UAAU;IAClC,CAAC,KAAK,QAAQ;AACZ,UAAI,GAAG,IAAI,QAAQ,GAAG,EAAE,IAAI,CAAC,EAAE,OAAO,mBAAmB,GAAG,aAC1D,UAAA;QAAC;QAAA;UAEC,SAAS;UACT,SAAS,MAAM,KAAK,QAAQ,aAAa,EAAG,EAAE;UAC9C,SAAS,EAAE,SAAS,KAAK,QAAQ,aAAa,EAAG,EAAE;UAElD,UAAA,CAAC,cACA;YAAC;YAAA;cACC,KAAK,CAAC,SAAS;AACR,qBAAA,QAAQ,aAAa,EAAG,IAAI;cACnC;cACA,MAAM;cACN,QAAQ,CAAC,OAAO,iBAAiB,IAAI,KAAK;cAC1C;cACC,GAAG,UAAU,gBAAgB;gBAC5B,OAAO;kBACL,GAAG,2BAA2B;oBAC5B;oBACA,UAAU;oBACV,oBAAoB;oBACpB,WAAW;kBAAA,CACZ;kBACD,GAAG;gBAAA;cAEN,CAAA;YAAA;UAAA;QACH;QAxBG,aAAa;MAAA,CA2BrB;AAEM,aAAA;IACT;IACA,CAAA;EACF;AAEA,aACG,0BAAA,gBAAA,EAAe,cAA6B,GAAG,aAC9C,UAAA;QAAA,yBAAC,KAAK,EAAA,GAAG,UAAU,MAAM,GAAG,iBAAc,cAAa,KAAW,GAAG,QACnE,cAAC,yBAAA,yBAAA,EAAiB,UAAkB,kBAAA,YAAY,EAAE,CAAA,EACpD,CAAA;QAEC,yBAAA,KAAA,EAAK,GAAG,UAAU,MAAM,GAAG,iBAAc,YAAY,GAAG,QACvD,cAAC,yBAAA,yBAAA,EAAiB,UAAkB,kBAAA,UAAU,EAAE,CAAA,EAClD,CAAA;QAEA;MAAC;MAAA;QACE,GAAG,UAAU,QAAQ,EAAE,WAAW,oBAAa,WAAW,UAAA,CAAW;QACtE,iBAAc;QACb,GAAG;QAEJ,cAAC,yBAAA,yBAAA,EAAiB,UAAkB,kBAAA,WAAW,EAAE,CAAA;MAAA;IACnD;QAEA;MAAC;MAAA;QACE,GAAG,UAAU,QAAQ,EAAE,WAAW,oBAAa,WAAW,UAAA,CAAW;QACtE,iBAAc;QACb,GAAG;QAEJ,cAAC,yBAAA,yBAAA,EAAiB,UAAkB,kBAAA,cAAc,EAAE,CAAA;MAAA;IACtD;QAEC,yBAAA,KAAA,EAAK,GAAG,UAAU,MAAM,GAAG,iBAAc,eAAe,GAAG,QAC1D,cAAC,yBAAA,yBAAA,EAAiB,UAAkB,kBAAA,aAAa,EAAE,CAAA,EACrD,CAAA;QAEC,yBAAA,KAAA,EAAK,GAAG,UAAU,MAAM,GAAG,iBAAc,iBAAiB,GAAG,QAC5D,cAAC,yBAAA,yBAAA,EAAiB,UAAkB,kBAAA,eAAe,EAAA,CAAE,EACvD,CAAA;EAAA,EACF,CAAA;AAEJ,CAAC;AAED,cAAc,UAAU;AACxB,cAAc,cAAc;AAC5B,cAAc,OAAO,cAAc;AACnC,cAAc,OAAO,cAAc;AACnC,cAAc,SAAS,cAAc;AACrC,cAAc,QAAQ,cAAc;AACpC,cAAc,aAAa,cAAc;AACzC,cAAc,cAAc,cAAc;", "names": ["notifications", "notifications", "_Transition"]}