import PageContainer, {
  type PageContainerProps,
} from "@/design-system/components/layout/PageContainer";
import { cn } from "@/design-system/utils";

/**
 * @deprecated Use PageContainer from design-system/components with variant="employer" instead
 */
export default function EmployerPageContainer({
  children,
  breadcrumbItems,
  showBreadcrumbs = true,
  className,
}: PageContainerProps) {
  return (
    <PageContainer
      breadcrumbItems={breadcrumbItems}
      showBreadcrumbs={showBreadcrumbs}
      className={cn(className, "rounded-lg border")}
      variant="employer"
    >
      {children}
    </PageContainer>
  );
}
