import { Request, Response, NextFunction } from "express";
import Company from "../models/company.model";
import Job from "../models/job.model";
import Application from "../models/application.model";
import { AppError } from "../utils/app-error";
import { catchAsync } from "../utils/catch-async";

// Get all companies with filtering, sorting, and pagination
export const getAllCompanies = catchAsync(
  async (req: Request, res: Response, _next: NextFunction) => {
    // Build query
    const queryObj = { ...req.query };
    const excludedFields = ["page", "sort", "limit", "fields", "search"];
    excludedFields.forEach((field) => delete queryObj[field]);

    // Advanced filtering
    let queryStr = JSON.stringify(queryObj);
    queryStr = queryStr.replace(/\b(gte|gt|lte|lt)\b/g, (match) => `$${match}`);

    let query = Company.find(JSON.parse(queryStr));

    // Search functionality
    if (req.query.search) {
      query = query.find({
        $text: { $search: req.query.search as string },
      });
    }

    // Sorting
    if (req.query.sort) {
      const sortBy = (req.query.sort as string).split(",").join(" ");
      query = query.sort(sortBy);
    } else {
      query = query.sort("-createdAt");
    }

    // Field limiting
    if (req.query.fields) {
      const fields = (req.query.fields as string).split(",").join(" ");
      query = query.select(fields) as any;
    } else {
      query = query.select("-__v") as any;
    }

    // Pagination
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const skip = (page - 1) * limit;

    query = query.skip(skip).limit(limit);

    // Execute query
    const companies = await query;
    const total = await Company.countDocuments(JSON.parse(queryStr));

    res.status(200).json({
      success: true,
      count: companies.length,
      total,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      companies,
    });
  }
);

// Get company by ID
export const getCompanyById = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    const company = await Company.findById(req.params.id);

    if (!company) {
      return next(new AppError("No company found with that ID", 404));
    }

    res.status(200).json({
      success: true,
      company,
    });
  }
);

// Create new company
export const createCompany = catchAsync(
  async (req: Request, res: Response, _next: NextFunction) => {
    // Set the owner to the current user
    const newCompany = await Company.create({
      ...req.body,
      owner: req.user._id as unknown as string,
    });

    res.status(201).json({
      success: true,
      company: newCompany,
    });
  }
);

// Update company
export const updateCompany = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    // Find the company
    const company = await Company.findById(req.params.id);

    if (!company) {
      return next(new AppError("No company found with that ID", 404));
    }

    // Check if the company belongs to the user
    if (company.owner.toString() !== req.user._id?.toString()) {
      return next(new AppError("You can only update your own company", 403));
    }

    // Update the company
    const updatedCompany = await Company.findByIdAndUpdate(
      req.params.id,
      req.body,
      {
        new: true,
        runValidators: true,
      }
    );

    res.status(200).json({
      success: true,
      company: updatedCompany,
    });
  }
);

// Delete company
export const deleteCompany = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    // Find the company
    const company = await Company.findById(req.params.id);

    if (!company) {
      return next(new AppError("No company found with that ID", 404));
    }

    // Check if the company belongs to the user
    if (company.owner.toString() !== req.user._id?.toString()) {
      return next(new AppError("You can only delete your own company", 403));
    }

    // Delete the company
    await Company.findByIdAndDelete(req.params.id);

    res.status(204).json({
      success: true,
      data: null,
    });
  }
);

// Get companies owned by the current user
export const getMyCompanies = catchAsync(
  async (req: Request, res: Response, _next: NextFunction) => {
    const companies = await Company.find({
      owner: req.user._id as unknown as string,
    });

    res.status(200).json({
      success: true,
      count: companies.length,
      companies,
    });
  }
);

// Verify company (admin only)
export const verifyCompany = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    const company = await Company.findById(req.params.id);

    if (!company) {
      return next(new AppError("No company found with that ID", 404));
    }

    company.isVerified = true;
    await company.save();

    res.status(200).json({
      success: true,
      company,
    });
  }
);

// Upload company logo
export const uploadCompanyLogo = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    // Find the company
    const company = await Company.findById(req.params.id);

    if (!company) {
      return next(new AppError("No company found with that ID", 404));
    }

    // Check if the company belongs to the user
    if (company.owner.toString() !== req.user._id?.toString()) {
      return next(new AppError("You can only update your own company", 403));
    }

    // Check if a file was uploaded
    if (!req.file) {
      return next(new AppError("No file uploaded", 400));
    }

    // Get the file path
    const filePath = req.file.path.replace(/\\/g, "/");

    // Update the company with the new logo
    company.logo = `/uploads/images/${req.file.filename}`;
    await company.save();

    res.status(200).json({
      success: true,
      company,
    });
  }
);

// Remove company logo
export const removeCompanyLogo = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    // Find the company
    const company = await Company.findById(req.params.id);

    if (!company) {
      return next(new AppError("No company found with that ID", 404));
    }

    // Check if the company belongs to the user
    if (company.owner.toString() !== req.user._id?.toString()) {
      return next(new AppError("You can only update your own company", 403));
    }

    // Check if the company has a logo
    if (!company.logo) {
      return next(new AppError("Company does not have a logo to remove", 400));
    }

    // Remove the logo from the company
    company.logo = undefined;
    await company.save();

    res.status(200).json({
      success: true,
      company,
    });
  }
);

// Get company statistics
export const getCompanyStats = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    // Find the company
    const company = await Company.findById(req.params.id);

    if (!company) {
      return next(new AppError("No company found with that ID", 404));
    }

    // Check if the company belongs to the user or user is admin
    if (
      company.owner.toString() !== req.user._id?.toString() &&
      req.user.role !== "admin"
    ) {
      return next(
        new AppError("You can only view stats for your own company", 403)
      );
    }

    // Get job count for this company
    const totalJobs = await Job.countDocuments({ company: req.params.id });

    // Get active job count for this company
    const activeJobs = await Job.countDocuments({
      company: req.params.id,
      isActive: true,
    });

    // Get total applications for this company's jobs
    const jobs = await Job.find({ company: req.params.id }).select("_id");
    const jobIds = jobs.map((job) => job._id);

    const totalApplications = await Application.countDocuments({
      job: { $in: jobIds },
    });

    // Get new applications (last 7 days)
    const oneWeekAgo = new Date();
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

    const newApplications = await Application.countDocuments({
      job: { $in: jobIds },
      createdAt: { $gte: oneWeekAgo },
    });

    // Calculate average response time (mock data for now)
    const averageResponseTime = 2.5; // days

    // Get view count (mock data for now)
    const viewCount = totalJobs * 30 + Math.floor(Math.random() * 100);

    res.status(200).json({
      success: true,
      stats: {
        totalJobs,
        activeJobs,
        totalApplications,
        newApplications,
        viewCount,
        averageResponseTime,
      },
    });
  }
);

// Get company verification status
export const getCompanyVerificationStatus = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    // Find the company
    const company = await Company.findById(req.params.id);

    if (!company) {
      return next(new AppError("No company found with that ID", 404));
    }

    // Check if the company belongs to the user or user is admin
    if (
      company.owner.toString() !== req.user._id?.toString() &&
      req.user.role !== "admin"
    ) {
      return next(
        new AppError(
          "You can only view verification status for your own company",
          403
        )
      );
    }

    // For now, we'll return a simple verification status
    // In a real application, you might have a more complex verification process
    const verificationStatus = {
      isVerified: company.isVerified,
      status: company.isVerified ? "verified" : "not_requested",
      requestedAt: company.isVerified
        ? new Date(company.updatedAt).toISOString()
        : null,
      verifiedAt: company.isVerified
        ? new Date(company.updatedAt).toISOString()
        : null,
    };

    res.status(200).json({
      success: true,
      verificationStatus,
    });
  }
);

// Request company verification
export const requestCompanyVerification = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    // Find the company
    const company = await Company.findById(req.params.id);

    if (!company) {
      return next(new AppError("No company found with that ID", 404));
    }

    // Check if the company belongs to the user
    if (company.owner.toString() !== req.user._id?.toString()) {
      return next(
        new AppError(
          "You can only request verification for your own company",
          403
        )
      );
    }

    // Check if the company is already verified
    if (company.isVerified) {
      return next(new AppError("Company is already verified", 400));
    }

    // In a real application, you would set a verification status field
    // For now, we'll just return a mock verification status
    const verificationStatus = {
      isVerified: false,
      status: "pending",
      requestedAt: new Date().toISOString(),
      verifiedAt: null,
    };

    res.status(200).json({
      success: true,
      message: "Verification request submitted successfully",
      verificationStatus,
    });
  }
);
