import { Request, Response, NextFunction } from "express";
import UserSubscription from "../models/user-subscription.model";
import SubscriptionPlan from "../models/subscription-plan.model";
import Job from "../models/job.model";
import Company from "../models/company.model";
import { AppError } from "../utils/app-error";

/**
 * Middleware to check if a user has an active subscription
 * and if they have reached their job posting limit
 */
export const checkSubscriptionForJobPosting = async (
  req: Request,
  _res: Response,
  next: NextFunction
) => {
  try {
    // SUBSCRIPTION CHECK DISABLED: Always proceed to next middleware
    // This allows job posting without subscription requirements
    console.log("Subscription check for job posting bypassed");
    return next();

    /* Original code commented out
    // Skip for admin users or in development mode
    if (req.user.role === "admin" || process.env.NODE_ENV === "development") {
      return next();
    }

    // Check if the user has an active subscription
    const subscription = await UserSubscription.findOne({
      user: req.user._id,
      status: "active",
    }).populate("plan");

    if (!subscription) {
      return next(
        new AppError(
          "You need an active subscription to post a job. Please subscribe to a plan.",
          403
        )
      );
    }

    // Check if the subscription has expired
    if (new Date(subscription.endDate) < new Date()) {
      subscription.status = "expired";
      await subscription.save();
      return next(
        new AppError(
          "Your subscription has expired. Please renew your subscription to post a job.",
          403
        )
      );
    }

    // Get the plan details
    const plan = subscription.plan as any; // Type assertion for TypeScript

    // Get the company ID from the request body
    const companyId = req.body.company;

    // Check if the company belongs to the user
    const company = await Company.findOne({
      _id: companyId,
      owner: req.user._id,
    });

    if (!company) {
      return next(
        new AppError("You can only post jobs for your own company", 403)
      );
    }

    // Count user's active jobs for this company
    const activeJobsCount = await Job.countDocuments({
      company: companyId,
      isActive: true,
    });

    // Check if user has reached the limit
    if (
      plan.features.activeJobs !== "unlimited" &&
      activeJobsCount >= plan.features.activeJobs
    ) {
      return next(
        new AppError(
          `You have reached your limit of ${plan.features.activeJobs} active job postings. Please upgrade your plan or deactivate some jobs.`,
          403
        )
      );
    }

    // If all checks pass, proceed to the next middleware
    next();
    */
  } catch (error) {
    next(error);
  }
};

/**
 * Middleware to check if a user has an active subscription
 * with CV viewing capabilities
 */
export const checkSubscriptionForCVViewing = async (
  req: Request,
  _res: Response,
  next: NextFunction
) => {
  try {
    // SUBSCRIPTION CHECK DISABLED: Always proceed to next middleware
    // This allows CV viewing without subscription requirements
    console.log("Subscription check for CV viewing bypassed");
    return next();

    /* Original code commented out
    // Skip for admin users or in development mode
    if (req.user.role === "admin" || process.env.NODE_ENV === "development") {
      return next();
    }

    // Check if the user has an active subscription
    const subscription = await UserSubscription.findOne({
      user: req.user._id,
      status: "active",
    }).populate("plan");

    if (!subscription) {
      return next(
        new AppError(
          "You need an active subscription to view candidate CVs. Please subscribe to a plan.",
          403
        )
      );
    }

    // Check if the subscription has expired
    if (new Date(subscription.endDate) < new Date()) {
      subscription.status = "expired";
      await subscription.save();
      return next(
        new AppError(
          "Your subscription has expired. Please renew your subscription to view candidate CVs.",
          403
        )
      );
    }

    // Get the plan details
    const plan = subscription.plan as any; // Type assertion for TypeScript

    // Check if the user has CV views available
    if (
      plan.features.cvViews !== "unlimited" &&
      subscription.usageStats.cvViews >= plan.features.cvViews
    ) {
      return next(
        new AppError(
          `You have reached your limit of ${plan.features.cvViews} CV views. Please upgrade your plan.`,
          403
        )
      );
    }

    // If all checks pass, proceed to the next middleware
    next();
    */
  } catch (error) {
    next(error);
  }
};

/**
 * Middleware to increment CV view count
 */
export const incrementCVViewCount = async (
  req: Request,
  _res: Response,
  next: NextFunction
) => {
  try {
    // SUBSCRIPTION TRACKING DISABLED: Always proceed to next middleware
    // This prevents CV view count from being incremented
    console.log("CV view count increment bypassed");
    return next();

    /* Original code commented out
    // Skip for admin users or in development mode
    if (req.user.role === "admin" || process.env.NODE_ENV === "development") {
      return next();
    }

    // Get the user's active subscription
    const subscription = await UserSubscription.findOne({
      user: req.user._id,
      status: "active",
    }).populate("plan");

    if (subscription) {
      const plan = subscription.plan as any; // Type assertion for TypeScript

      // Only increment if there's a limit (unlimited plans don't need tracking)
      if (plan.features.cvViews !== "unlimited") {
        // Increment CV view count
        subscription.usageStats.cvViews += 1;
        await subscription.save();
      }
    }

    // Continue to the next middleware regardless
    next();
    */
  } catch (error) {
    // Don't block the request if there's an error with incrementing
    console.error("Error incrementing CV view count:", error);
    next();
  }
};
