import { URLS } from "@/utils/urls";
import { Card, Grid, Text, Title } from "@mantine/core";
import { type IconType } from "react-icons";
import {
  FaBriefcase,
  FaCalendarAlt,
  FaChartBar,
  FaFileAlt,
  FaPlus,
  FaSearch,
  FaUserEdit,
  FaUsers,
} from "react-icons/fa";
import { Link } from "react-router";

interface QuickActionItem {
  id: number;
  label: string;
  description: string;
  href: string;
  icon: IconType;
  color: string;
  bgColor: string;
}

const quickActions: QuickActionItem[] = [
  {
    id: 1,
    label: "Post a Job",
    description: "Create a new job posting",
    href: URLS.employer.createJob,
    icon: FaPlus,
    color: "text-white",
    bgColor: "bg-blue-600 hover:bg-blue-700",
  },
  {
    id: 2,
    label: "Manage Jobs",
    description: "View and edit your job postings",
    href: URLS.employer.manageJobs,
    icon: FaBriefcase,
    color: "text-blue-600",
    bgColor: "bg-blue-50 hover:bg-blue-100",
  },
  {
    id: 3,
    label: "View Candidates",
    description: "Browse all applicants",
    href: URLS.employer.candidates,
    icon: FaUsers,
    color: "text-green-600",
    bgColor: "bg-green-50 hover:bg-green-100",
  },
  {
    id: 4,
    label: "Schedule Interviews",
    description: "Manage interview calendar",
    href: "#",
    icon: FaCalendarAlt,
    color: "text-purple-600",
    bgColor: "bg-purple-50 hover:bg-purple-100",
  },
  {
    id: 5,
    label: "Search Resumes",
    description: "Find candidates in our database",
    href: "#",
    icon: FaSearch,
    color: "text-yellow-600",
    bgColor: "bg-yellow-50 hover:bg-yellow-100",
  },
  {
    id: 6,
    label: "Edit Profile",
    description: "Update company information",
    href: URLS.employer.profile,
    icon: FaUserEdit,
    color: "text-indigo-600",
    bgColor: "bg-indigo-50 hover:bg-indigo-100",
  },
  {
    id: 7,
    label: "Job Analytics",
    description: "View performance metrics",
    href: "#",
    icon: FaChartBar,
    color: "text-cyan-600",
    bgColor: "bg-cyan-50 hover:bg-cyan-100",
  },
  {
    id: 8,
    label: "Job Templates",
    description: "Create reusable job templates",
    href: "#",
    icon: FaFileAlt,
    color: "text-gray-600",
    bgColor: "bg-gray-50 hover:bg-gray-100",
  },
];

export default function QuickActions() {
  return (
    <div className="mb-8">
      <Title order={3} className="mb-4 text-xl font-semibold text-blue-800">
        Quick Actions
      </Title>

      <Grid gutter="md">
        {quickActions.map(
          ({ href, icon: Icon, id, label, description, color, bgColor }) => (
            <Grid.Col key={id} span={{ base: 12, sm: 6, md: 4, lg: 3 }}>
              <Card
                component={Link}
                to={href}
                withBorder
                radius="md"
                className={`block h-full transition-all duration-200 ${bgColor}`}
                padding="lg"
              >
                <div className="flex items-start gap-3">
                  <div
                    className={`rounded-full p-3 ${
                      id === 1 ? "bg-blue-600" : "bg-white"
                    }`}
                  >
                    <Icon
                      size={20}
                      className={id === 1 ? "text-white" : color}
                    />
                  </div>
                  <div>
                    <Text fw={600} className={id === 1 ? "text-white" : ""}>
                      {label}
                    </Text>
                    <Text
                      size="sm"
                      className={id === 1 ? "text-blue-100" : "text-gray-600"}
                    >
                      {description}
                    </Text>
                  </div>
                </div>
              </Card>
            </Grid.Col>
          ),
        )}
      </Grid>
    </div>
  );
}
