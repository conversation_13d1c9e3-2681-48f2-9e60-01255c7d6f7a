import { body } from "express-validator";

export const createCompanyValidator = [
  body("name")
    .notEmpty()
    .withMessage("Company name is required")
    .isLength({ min: 2, max: 100 })
    .withMessage("Company name must be between 2 and 100 characters"),

  body("description")
    .notEmpty()
    .withMessage("Company description is required")
    .isLength({ min: 10 })
    .withMessage("Company description must be at least 10 characters long"),

  body("logo").optional().isURL().withMessage("Logo must be a valid URL"),

  body("website").optional().isURL().withMessage("Website must be a valid URL"),

  body("industry")
    .notEmpty()
    .withMessage("Industry is required")
    .isString()
    .withMessage("Industry must be a string"),

  body("size")
    .notEmpty()
    .withMessage("Company size is required")
    .isIn(["1-10", "11-50", "51-200", "201-500", "501-1000", "1000+"])
    .withMessage(
      "Company size must be one of: 1-10, 11-50, 51-200, 201-500, 501-1000, 1000+"
    ),

  body("location")
    .notEmpty()
    .withMessage("Company location is required")
    .isString()
    .withMessage("Location must be a string"),

  body("foundedYear")
    .optional()
    .isInt({ min: 1800, max: new Date().getFullYear() })
    .withMessage(
      `Founded year must be between 1800 and ${new Date().getFullYear()}`
    ),

  body("socialLinks")
    .optional()
    .isObject()
    .withMessage("Social links must be an object"),

  body("socialLinks.linkedin")
    .optional()
    .isURL()
    .withMessage("LinkedIn URL must be valid"),

  body("socialLinks.twitter")
    .optional()
    .isURL()
    .withMessage("Twitter URL must be valid"),

  body("socialLinks.facebook")
    .optional()
    .isURL()
    .withMessage("Facebook URL must be valid"),

  body("socialLinks.instagram")
    .optional()
    .isURL()
    .withMessage("Instagram URL must be valid"),

  body("contactEmail")
    .notEmpty()
    .withMessage("Contact email is required")
    .isEmail()
    .withMessage("Contact email must be a valid email address"),

  body("contactPhone")
    .optional()
    .isMobilePhone("any")
    .withMessage("Contact phone must be a valid phone number"),
];

export const updateCompanyValidator = [
  body("name")
    .optional()
    .isLength({ min: 2, max: 100 })
    .withMessage("Company name must be between 2 and 100 characters"),

  body("description")
    .optional()
    .isLength({ min: 10 })
    .withMessage("Company description must be at least 10 characters long"),

  body("logo").optional().isURL().withMessage("Logo must be a valid URL"),

  body("website").optional().isURL().withMessage("Website must be a valid URL"),

  body("industry")
    .optional()
    .isString()
    .withMessage("Industry must be a string"),

  body("size")
    .optional()
    .isIn(["1-10", "11-50", "51-200", "201-500", "501-1000", "1000+"])
    .withMessage(
      "Company size must be one of: 1-10, 11-50, 51-200, 201-500, 501-1000, 1000+"
    ),

  body("location")
    .optional()
    .isString()
    .withMessage("Location must be a string"),

  body("foundedYear")
    .optional({ nullable: true })
    .if((value) => value !== null && value !== undefined && value !== "")
    .isInt({ min: 1800, max: new Date().getFullYear() })
    .withMessage(
      `Founded year must be between 1800 and ${new Date().getFullYear()}`
    ),

  body("socialLinks")
    .optional()
    .isObject()
    .withMessage("Social links must be an object"),

  body("socialLinks.linkedin")
    .optional()
    .if((value) => value !== null && value !== undefined && value !== "")
    .isURL()
    .withMessage("LinkedIn URL must be valid"),

  body("socialLinks.twitter")
    .optional()
    .if((value) => value !== null && value !== undefined && value !== "")
    .isURL()
    .withMessage("Twitter URL must be valid"),

  body("socialLinks.facebook")
    .optional()
    .if((value) => value !== null && value !== undefined && value !== "")
    .isURL()
    .withMessage("Facebook URL must be valid"),

  body("socialLinks.instagram")
    .optional()
    .if((value) => value !== null && value !== undefined && value !== "")
    .isURL()
    .withMessage("Instagram URL must be valid"),

  body("contactEmail")
    .optional()
    .isEmail()
    .withMessage("Contact email must be a valid email address"),

  body("contactPhone")
    .optional()
    .isMobilePhone("any")
    .withMessage("Contact phone must be a valid phone number"),

  body("companyType")
    .optional()
    .isIn([
      "Startup",
      "Small Business",
      "Medium Enterprise",
      "Large Enterprise",
      "Corporation",
      "Non-Profit",
      "Government",
      "Educational Institution",
      "Freelancer/Self-Employed",
    ])
    .withMessage("Company type must be one of the valid types"),
];
