import {
  Avatar,
  Badge,
  Button,
  Group,
  LoadingOverlay,
  Text,
} from "@mantine/core";
import { notifications } from "@mantine/notifications";
import { useState } from "react";
import { FaCalendarAlt, FaClock, FaVideo } from "react-icons/fa";
import { useNavigate } from "react-router";

// Activity interface
interface Activity {
  id: string;
  type: string;
  message: string;
  date: string;
  status?: string;
  data?: {
    applicationId?: string;
    userId?: string;
    jobId?: string;
    jobTitle?: string;
    candidateName?: string;
    candidateEmail?: string;
    interviewDate?: string;
    interviewTime?: string;
    interviewType?: string;
  };
}

interface UpcomingInterviewsProps {
  interviews?: Activity[];
}

export default function UpcomingInterviews({
  interviews = [],
}: UpcomingInterviewsProps) {
  const navigate = useNavigate();
  const [loadingStates, setLoadingStates] = useState<Record<string, boolean>>(
    {},
  );

  // Map activities to interview format
  const upcomingInterviews = interviews.map((activity) => {
    // Extract candidate name and job title from message or use data
    let candidateName = activity.data?.candidateName || "";
    let jobTitle = activity.data?.jobTitle || "";

    if (!candidateName && activity.message) {
      const messageParts = activity.message.split(" for ");
      candidateName = messageParts[0].replace("Interview scheduled with ", "");
      jobTitle = messageParts[1] || "Unknown Position";
    }

    // Use the activity date as the interview date
    const interviewDate = new Date(activity.date);

    return {
      id: activity.id,
      applicationId: activity.data?.applicationId,
      userId: activity.data?.userId,
      jobId: activity.data?.jobId,
      candidateName,
      jobTitle,
      date: interviewDate.toLocaleDateString(),
      time: activity.data?.interviewTime || "Time not specified",
      type: activity.data?.interviewType || "Video Call",
      status: activity.status === "scheduled" ? "Confirmed" : "Pending",
      email: activity.data?.candidateEmail,
    };
  });

  // Handle view candidate profile action
  const handleViewProfile = (userId: string) => {
    if (userId) {
      navigate(`/employer/candidates/${userId}`);
    } else {
      notifications.show({
        title: "Error",
        message: "Cannot view profile. User ID not available.",
        color: "red",
      });
    }
  };

  // Handle view interview details action
  const handleViewDetails = (applicationId: string) => {
    if (applicationId) {
      navigate(`/employer/applications/${applicationId}`);
    } else {
      notifications.show({
        title: "Error",
        message: "Cannot view details. Application ID not available.",
        color: "red",
      });
    }
  };

  // Handle join call action
  const handleJoinCall = (interview: any) => {
    // This would typically launch a video call application or redirect to a meeting URL
    // For now, we'll just show a notification
    notifications.show({
      title: "Join Call",
      message: `Joining video call with ${interview.candidateName}`,
      color: "blue",
    });
  };

  // Handle reschedule action
  const handleReschedule = (applicationId: string, interviewId: string) => {
    if (applicationId) {
      setLoadingStates((prev) => ({ ...prev, [interviewId]: true }));

      // Simulate a delay for the loading state
      setTimeout(() => {
        setLoadingStates((prev) => ({ ...prev, [interviewId]: false }));
        navigate(`/employer/interviews/reschedule/${applicationId}`);
      }, 500);
    } else {
      notifications.show({
        title: "Error",
        message: "Cannot reschedule. Application ID not available.",
        color: "red",
      });
    }
  };
  return (
    <div className="relative">
      {upcomingInterviews.length > 0 ? (
        <>
          {upcomingInterviews.map((interview) => (
            <div key={interview.id} className="mb-4 border-b pb-4 relative">
              <LoadingOverlay visible={loadingStates[interview.id] || false} />
              <Group justify="space-between" wrap="nowrap" className="mb-2">
                <Group gap="sm" wrap="nowrap">
                  <Avatar
                    color="blue"
                    radius="xl"
                    onClick={() => handleViewProfile(interview.userId || "")}
                    style={{ cursor: "pointer" }}
                  >
                    {interview.candidateName
                      .split(" ")
                      .map((n) => n[0])
                      .join("")}
                  </Avatar>
                  <div>
                    <Text fw={600}>{interview.candidateName}</Text>
                    <Text size="sm" c="dimmed">
                      {interview.jobTitle}
                    </Text>
                  </div>
                </Group>
                <Badge
                  color={interview.status === "Confirmed" ? "green" : "yellow"}
                >
                  {interview.status}
                </Badge>
              </Group>

              <Group className="mt-2 ml-12">
                <Group gap="xs">
                  <FaCalendarAlt size={14} className="text-blue-500" />
                  <Text size="sm">{interview.date}</Text>
                </Group>
                <Group gap="xs">
                  <FaClock size={14} className="text-blue-500" />
                  <Text size="sm">{interview.time}</Text>
                </Group>
                <Group gap="xs">
                  <FaVideo size={14} className="text-blue-500" />
                  <Text size="sm">{interview.type}</Text>
                </Group>
              </Group>

              <Group className="mt-3 ml-12">
                <Button
                  variant="light"
                  size="xs"
                  loading={loadingStates[interview.id] || false}
                  onClick={() =>
                    handleReschedule(
                      interview.applicationId || "",
                      interview.id,
                    )
                  }
                >
                  Reschedule
                </Button>
                <Button
                  variant="outline"
                  size="xs"
                  onClick={() =>
                    handleViewDetails(interview.applicationId || "")
                  }
                >
                  View Details
                </Button>
                {interview.type === "Video Call" && (
                  <Button
                    variant="filled"
                    size="xs"
                    onClick={() => handleJoinCall(interview)}
                  >
                    Join Call
                  </Button>
                )}
              </Group>
            </div>
          ))}
        </>
      ) : (
        <div className="py-6 text-center border rounded-md">
          <Text size="lg" fw={500} className="mb-2">
            No upcoming interviews
          </Text>
          <Text size="sm" c="dimmed" className="mb-4">
            You have no scheduled interviews at the moment. Schedule interviews
            with candidates to see them here.
          </Text>
          <Button
            variant="filled"
            onClick={() => navigate("/employer/candidates")}
            leftSection={<FaCalendarAlt size={14} />}
          >
            View Candidates
          </Button>
        </div>
      )}
    </div>
  );
}
