import { candidateData } from "@/data/candidate-data";
import { Badge, Card, Text, Title } from "@mantine/core";

export default function CandidatePersonalInformation() {
  return (
    <Card withBorder radius="md" className="p-6 shadow-sm">
      <Title order={2} className="mb-4 text-lg font-semibold">
        Personal Information
      </Title>
      <Text size="md" className="mb-2" component="div">
        <strong>Name:</strong> {candidateData.name}
      </Text>
      <Text size="md" className="mb-2" component="div">
        <strong>Email:</strong> {candidateData.email}
      </Text>
      <Text size="md" className="mb-2" component="div">
        <strong>Phone:</strong> {candidateData.phone}
      </Text>
      <Text size="md" className="mb-2" component="div">
        <strong>Job Title:</strong> {candidateData.jobTitle}
      </Text>
      <Text size="md" className="mb-2" component="div">
        <strong>Applied Date:</strong> {candidateData.appliedDate}
      </Text>
      <Text size="md" className="mb-2" component="div">
        <strong>Status:</strong>{" "}
        <Badge color="blue" size="md">
          {candidateData.status}
        </Badge>
      </Text>
    </Card>
  );
}
