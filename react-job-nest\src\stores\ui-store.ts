"use client";

import { create } from "zustand";

// Define the UI store type
interface UIStore {
  // Whether the component is rendered inside a drawer
  isInDrawer: boolean;

  // Actions
  setIsInDrawer: (isInDrawer: boolean) => void;
}

// Create the UI store
export const useUIStore = create<UIStore>((set) => ({
  isInDrawer: false,
  setIsInDrawer: (isInDrawer) => set({ isInDrawer }),
}));

// Helper hook for drawer context
export const useDrawerContext = () => {
  const isInDrawer = useUIStore((state) => state.isInDrawer);
  const setIsInDrawer = useUIStore((state) => state.setIsInDrawer);

  return {
    isInDrawer,
    setIsInDrawer,
  };
};
