import express from 'express';
import {
  getRecommendedJobs,
  getSimilarJobs,
  getJobApplicationStats,
} from '../controllers/job-recommendation.controller';
import { protect, restrictTo } from '../middleware/auth.middleware';

const router = express.Router();

// All routes are protected and restricted to candidates
router.use(protect);
router.use(restrictTo('candidate'));

// Get recommended jobs for the current candidate
router.get('/recommended', getRecommendedJobs);

// Get similar jobs to a specific job
router.get('/similar/:jobId', getSimilarJobs);

// Get job application statistics for the candidate dashboard
router.get('/application-stats', getJobApplicationStats);

export default router;
