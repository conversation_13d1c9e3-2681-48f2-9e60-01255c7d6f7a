import mongoose, { Document, Schema } from "mongoose";

export interface ICompany extends Document {
  name: string;
  description: string;
  logo?: string;
  website?: string;
  industry: string;
  size: string;
  location: string;
  foundedYear?: number;
  owner: mongoose.Types.ObjectId;
  isVerified: boolean;
  isActive: boolean;
  socialLinks: {
    linkedin?: string;
    twitter?: string;
    facebook?: string;
    instagram?: string;
  };
  contactEmail: string;
  contactPhone?: string;
  companyType?: string;
  createdAt: Date;
  updatedAt: Date;
}

const companySchema = new Schema<ICompany>(
  {
    name: {
      type: String,
      required: [true, "Company name is required"],
      trim: true,
      minlength: [2, "Company name must be at least 2 characters long"],
      maxlength: [100, "Company name must be at most 100 characters long"],
    },
    description: {
      type: String,
      required: [true, "Company description is required"],
      minlength: [
        10,
        "Company description must be at least 10 characters long",
      ],
    },
    logo: {
      type: String,
    },
    website: {
      type: String,
    },
    industry: {
      type: String,
      required: [true, "Industry is required"],
    },
    size: {
      type: String,
      required: [true, "Company size is required"],
      enum: {
        values: ["1-10", "11-50", "51-200", "201-500", "501-1000", "1000+"],
        message:
          "Company size must be one of: 1-10, 11-50, 51-200, 201-500, 501-1000, 1000+",
      },
    },
    location: {
      type: String,
      required: [true, "Company location is required"],
    },
    foundedYear: {
      type: Number,
      min: [1800, "Founded year must be after 1800"],
      max: [new Date().getFullYear(), "Founded year cannot be in the future"],
    },
    owner: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: [true, "Company owner is required"],
    },
    isVerified: {
      type: Boolean,
      default: false,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    socialLinks: {
      linkedin: String,
      twitter: String,
      facebook: String,
      instagram: String,
    },
    contactEmail: {
      type: String,
      required: [true, "Contact email is required"],
    },
    contactPhone: {
      type: String,
    },
    companyType: {
      type: String,
      enum: {
        values: [
          "Startup",
          "Small Business",
          "Medium Enterprise",
          "Large Enterprise",
          "Corporation",
          "Non-Profit",
          "Government",
          "Educational Institution",
          "Freelancer/Self-Employed",
        ],
      },
    },
  },
  {
    timestamps: true, // Automatically add createdAt and updatedAt fields
  }
);

// Create indexes for better search performance
companySchema.index({ name: "text", description: "text" });
companySchema.index({ industry: 1 });
companySchema.index({ location: 1 });
companySchema.index({ isActive: 1 });
companySchema.index({ owner: 1 });

const Company = mongoose.model<ICompany>("Company", companySchema);

export default Company;
