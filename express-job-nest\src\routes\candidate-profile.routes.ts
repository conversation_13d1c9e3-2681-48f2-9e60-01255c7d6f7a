import express from "express";
import {
  getMyProfile,
  updateMyProfile,
  addEducation,
  updateEducation,
  deleteEducation,
  addExperience,
  updateExperience,
  deleteExperience,
  getCandidateProfileByUserId,
  addSkill,
  removeSkill,
  endorseSkill,
  uploadResume,
  uploadAndParseResume,
  uploadProfileImage,
  uploadAndSaveProfileImage,
} from "../controllers/candidate-profile.controller";
import { protect, restrictTo } from "../middleware/auth.middleware";
import { body } from "express-validator";
import { validationMiddleware } from "../validators/validation.middleware";

// Define validators
const updateProfileValidator = [
  body("headline")
    .optional()
    .isString()
    .withMessage("Headline must be a string")
    .isLength({ max: 100 })
    .withMessage("Headline cannot exceed 100 characters"),
  body("summary").optional().isString().withMessage("Summary must be a string"),
  body("phone").optional().isString().withMessage("Phone must be a string"),
  body("location")
    .optional()
    .isString()
    .withMessage("Location must be a string"),
  body("skills").optional().isArray().withMessage("Skills must be an array"),
];

const skillValidator = [
  body("skill")
    .notEmpty()
    .withMessage("Skill is required")
    .isString()
    .withMessage("Skill must be a string"),
];

const endorseSkillValidator = [
  body("candidateId")
    .notEmpty()
    .withMessage("Candidate ID is required")
    .isMongoId()
    .withMessage("Invalid candidate ID format"),
  body("skill")
    .notEmpty()
    .withMessage("Skill is required")
    .isString()
    .withMessage("Skill must be a string"),
];

const educationValidator = [
  body("institution")
    .notEmpty()
    .withMessage("Institution name is required")
    .isString()
    .withMessage("Institution name must be a string"),
  body("degree")
    .notEmpty()
    .withMessage("Degree is required")
    .isString()
    .withMessage("Degree must be a string"),
  body("startDate")
    .notEmpty()
    .withMessage("Start date is required")
    .isISO8601()
    .withMessage("Start date must be a valid date"),
];

const experienceValidator = [
  body("company")
    .notEmpty()
    .withMessage("Company name is required")
    .isString()
    .withMessage("Company name must be a string"),
  body("position")
    .notEmpty()
    .withMessage("Position is required")
    .isString()
    .withMessage("Position must be a string"),
  body("startDate")
    .notEmpty()
    .withMessage("Start date is required")
    .isISO8601()
    .withMessage("Start date must be a valid date"),
];

const router = express.Router();

// All routes are protected
router.use(protect);

// Candidate routes
router.get("/me", getMyProfile);
router.patch(
  "/me",
  updateProfileValidator,
  validationMiddleware,
  updateMyProfile
);

// Education routes
router.post(
  "/education",
  educationValidator,
  validationMiddleware,
  addEducation
);
router.patch(
  "/education/:educationId",
  educationValidator,
  validationMiddleware,
  updateEducation
);
router.delete("/education/:educationId", deleteEducation);

// Experience routes
router.post(
  "/experience",
  experienceValidator,
  validationMiddleware,
  addExperience
);
router.patch(
  "/experience/:experienceId",
  experienceValidator,
  validationMiddleware,
  updateExperience
);
router.delete("/experience/:experienceId", deleteExperience);

// Skills routes
router.post("/skills", skillValidator, validationMiddleware, addSkill);
router.delete("/skills/:skill", removeSkill);

// Resume routes
router.post("/resume", uploadResume, uploadAndParseResume);

// Profile image routes
router.post("/profile-image", uploadProfileImage, uploadAndSaveProfileImage);

// Employer and admin routes
router.use(restrictTo("employer", "admin"));
router.get("/user/:userId", getCandidateProfileByUserId);
router.post(
  "/endorse",
  endorseSkillValidator,
  validationMiddleware,
  endorseSkill
);

export default router;
