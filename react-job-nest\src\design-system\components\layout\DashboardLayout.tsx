"use client";

import { useDrawerContext } from "@/stores/ui-store";
import { <PERSON>, Drawer } from "@mantine/core";
import { useDisclosure } from "@mantine/hooks";
import { type PropsWithChildren, useEffect } from "react";
import Sidebar, { type SidebarLink } from "../sidebar/Sidebar";

interface DashboardLayoutProps extends PropsWithChildren {
  title: string;
  links: SidebarLink[];
}

/**
 * Reusable dashboard layout component for admin, employer, and candidate sections
 */
export default function DashboardLayout({
  children,
  title,
  links,
}: DashboardLayoutProps) {
  const [mobileOpen, { open: openMobile, close: closeMobile }] =
    useDisclosure(false);
  const { setIsInDrawer } = useDrawerContext();

  // Set drawer context when drawer opens/closes
  useEffect(() => {
    setIsInDrawer(mobileOpen);

    return () => {
      // Reset when component unmounts
      setIsInDrawer(false);
    };
  }, [mobileOpen, setIsInDrawer]);

  return (
    <div className="relative flex flex-col gap-3 md:flex-row">
      {/* Mobile burger menu - only visible on small screens */}
      <div className="sticky top-0 z-10 flex items-center border-b bg-white p-4 md:hidden">
        <Burger
          opened={mobileOpen}
          onClick={openMobile}
          aria-label={`Toggle ${title.toLowerCase()} menu`}
        />
        <h2 className="text-primary-color ml-4 text-xl font-bold">{title}</h2>
      </div>

      {/* Mobile drawer for sidebar */}
      <Drawer
        opened={mobileOpen}
        onClose={closeMobile}
        size="xs"
        className="md:hidden"
        title={title}
      >
        <Sidebar title={title} links={links} onLinkClick={closeMobile} />
      </Drawer>

      {/* Desktop sidebar - hidden on mobile */}
      <div className="hidden md:block">
        <Sidebar title={title} links={links} />
      </div>

      {/* Main content */}
      <div className="flex-1">{children}</div>
    </div>
  );
}
