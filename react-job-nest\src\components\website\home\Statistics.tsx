import { useMantineColorScheme } from "@mantine/core";
import { FaBriefcase, FaBuilding, FaUsers } from "react-icons/fa";

const stats = [
  {
    id: 1,
    icon: FaUsers,
    value: "10,000+",
    label: "Active Users",
    description: "Professionals using JobNest daily",
  },
  {
    id: 2,
    icon: FaBriefcase,
    value: "5,000+",
    label: "Job Postings",
    description: "New opportunities added monthly",
  },
  {
    id: 3,
    icon: FaBuilding,
    value: "2,500+",
    label: "Companies",
    description: "Trusted employers on our platform",
  },
];

export default function Statistics() {
  const { colorScheme } = useMantineColorScheme();
  const isDark = colorScheme === "dark";

  return (
    <section className={`py-20 ${isDark ? "bg-gray-900" : ""}`}>
      <div className="container">
        <div
          className={`mx-auto max-w-4xl rounded-2xl ${
            isDark
              ? "bg-gradient-to-r from-primary-color/90 to-blue-700/90"
              : "bg-gradient-to-r from-primary-color to-blue-700"
          } p-8 shadow-lg sm:p-12`}
        >
          <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
            {stats.map(({ id, icon: Icon, value, label, description }) => (
              <div key={id} className="text-center">
                <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-white/20">
                  <Icon className="text-white" size={28} />
                </div>
                <h3 className="mb-1 text-4xl font-bold text-white">{value}</h3>
                <p className="mb-1 text-lg font-medium text-white">{label}</p>
                <p className="text-sm text-white/80">{description}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
