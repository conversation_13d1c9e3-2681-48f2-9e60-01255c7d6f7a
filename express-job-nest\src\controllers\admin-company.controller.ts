import { Request, Response, NextFunction } from "express";
import { catchAsync } from "../utils/catch-async";
import { AppError } from "../utils/app-error";
import Company from "../models/company.model";
import Job from "../models/job.model";

// Get all companies with filtering and pagination
export const getAllCompanies = catchAsync(
  async (req: Request, res: Response, _next: NextFunction) => {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const skip = (page - 1) * limit;

    // Build query
    const queryObj: any = {};

    // Filter by verification status
    if (req.query.verified === "true") {
      queryObj.isVerified = true;
    } else if (req.query.verified === "false") {
      queryObj.isVerified = false;
    }

    // Filter by active status
    if (req.query.active === "true") {
      queryObj.isActive = true;
    } else if (req.query.active === "false") {
      queryObj.isActive = false;
    }

    // Filter by industry
    if (req.query.industry && req.query.industry !== "all") {
      queryObj.industry = req.query.industry;
    }

    // Search by name or description
    if (req.query.search) {
      queryObj.$or = [
        { name: { $regex: req.query.search, $options: "i" } },
        { description: { $regex: req.query.search, $options: "i" } },
      ];
    }

    // Execute query with pagination
    const companies = await Company.find(queryObj)
      .populate({
        path: "owner",
        select: "name email",
      })
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    // Get total count
    const totalCompanies = await Company.countDocuments(queryObj);

    res.status(200).json({
      success: true,
      count: companies.length,
      totalPages: Math.ceil(totalCompanies / limit),
      currentPage: page,
      companies,
    });
  }
);

// Get company by ID
export const getCompanyById = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    const company = await Company.findById(req.params.id).populate({
      path: "owner",
      select: "name email",
    });

    if (!company) {
      return next(new AppError("Company not found", 404));
    }

    // Get job count for this company
    const jobCount = await Job.countDocuments({ company: req.params.id });

    // Get active job count for this company
    const activeJobCount = await Job.countDocuments({
      company: req.params.id,
      isActive: true,
    });

    res.status(200).json({
      success: true,
      company,
      stats: {
        jobCount,
        activeJobCount,
      },
    });
  }
);

// Update company
export const updateCompany = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    // Find the company
    const company = await Company.findById(req.params.id);

    if (!company) {
      return next(new AppError("Company not found", 404));
    }

    // Update the company
    const updatedCompany = await Company.findByIdAndUpdate(
      req.params.id,
      req.body,
      {
        new: true,
        runValidators: true,
      }
    ).populate({
      path: "owner",
      select: "name email",
    });

    res.status(200).json({
      success: true,
      company: updatedCompany,
    });
  }
);

// Delete company
export const deleteCompany = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    // Find the company
    const company = await Company.findById(req.params.id);

    if (!company) {
      return next(new AppError("Company not found", 404));
    }

    // Delete the company
    await Company.findByIdAndDelete(req.params.id);

    // Delete all jobs for this company
    await Job.deleteMany({ company: req.params.id });

    res.status(200).json({
      success: true,
      message: "Company deleted successfully",
    });
  }
);

// Verify company
export const verifyCompany = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    // Find the company
    const company = await Company.findById(req.params.id);

    if (!company) {
      return next(new AppError("Company not found", 404));
    }

    // Update the company verification status
    company.isVerified = true;
    await company.save();

    res.status(200).json({
      success: true,
      message: "Company verified successfully",
      company,
    });
  }
);

// Block/unblock company
export const toggleCompanyStatus = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    const { reason } = req.body;

    // Find the company
    const company = await Company.findById(req.params.id);

    if (!company) {
      return next(new AppError("Company not found", 404));
    }

    // Toggle the company active status
    company.isActive = !company.isActive;
    await company.save();

    // If blocking the company, also deactivate all its jobs
    if (!company.isActive) {
      await Job.updateMany({ company: company._id }, { isActive: false });
    }

    // TODO: Send notification to company owner with reason
    console.log(
      `Company ${company._id} ${
        company.isActive ? "unblocked" : "blocked"
      } with reason: ${reason}`
    );

    res.status(200).json({
      success: true,
      message: company.isActive
        ? "Company unblocked successfully"
        : "Company blocked successfully",
      company,
    });
  }
);

// Get company industries
export const getCompanyIndustries = catchAsync(
  async (_req: Request, res: Response, _next: NextFunction) => {
    const industries = await Company.distinct("industry");

    res.status(200).json({
      success: true,
      industries,
    });
  }
);
