import { Request, Response, NextFunction } from "express";
import { AppError } from "../utils/app-error";
import { catchAsync } from "../utils/catch-async";
import User, { IUser } from "../models/user.model";
import Company from "../models/company.model";
import {
  uploadTypes,
  deleteFile,
  getFileUrl,
} from "../services/file-upload.service";
import Notification from "../models/notification.model";

// Extend Express Request to include user property with proper typing
declare global {
  namespace Express {
    interface Request {
      user: IUser;
    }
  }
}

// Get current employer's profile
export const getMyEmployerProfile = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    // Find the user
    const user = await User.findById(req.user._id);
    
    if (!user) {
      return next(new AppError("User not found", 404));
    }

    // Check if the user is an employer
    if (user.role !== "employer") {
      return next(new AppError("User is not an employer", 400));
    }

    // Find the company owned by this employer
    const company = await Company.findOne({ owner: req.user._id });

    // Create employer profile object
    const employerProfile = {
      user: {
        _id: user._id,
        name: user.name,
        email: user.email,
        profileImageUrl: user.profileImageUrl,
      },
      company: company || null,
    };

    res.status(200).json({
      success: true,
      profile: employerProfile,
    });
  }
);

// Update current employer's profile
export const updateMyEmployerProfile = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    // Find the user
    const user = await User.findById(req.user._id);
    
    if (!user) {
      return next(new AppError("User not found", 404));
    }

    // Check if the user is an employer
    if (user.role !== "employer") {
      return next(new AppError("User is not an employer", 400));
    }

    // Update user fields if provided
    const userUpdateFields: any = {};
    if (req.body.name) userUpdateFields.name = req.body.name;
    if (req.body.email) userUpdateFields.email = req.body.email;

    if (Object.keys(userUpdateFields).length > 0) {
      await User.findByIdAndUpdate(req.user._id, userUpdateFields, {
        new: true,
        runValidators: true,
      });
    }

    // Find the company owned by this employer
    const company = await Company.findOne({ owner: req.user._id });

    // Get updated user
    const updatedUser = await User.findById(req.user._id);

    // Create updated employer profile object
    const employerProfile = {
      user: {
        _id: updatedUser!._id,
        name: updatedUser!.name,
        email: updatedUser!.email,
        profileImageUrl: updatedUser!.profileImageUrl,
      },
      company: company || null,
    };

    res.status(200).json({
      success: true,
      profile: employerProfile,
    });
  }
);

// Upload profile image middleware
export const uploadEmployerProfileImage =
  uploadTypes.profileImage.single("profileImage");

// Upload and save employer profile image
export const uploadAndSaveEmployerProfileImage = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    // Check if file was uploaded
    if (!req.file) {
      return next(new AppError("No profile image file uploaded", 400));
    }

    try {
      // Get the file path
      const filePath = `uploads/images/${req.file.filename}`;
      const fileUrl = getFileUrl(filePath);

      // Update the user's profile image URL
      const updatedUser = await User.findByIdAndUpdate(
        req.user._id,
        { profileImageUrl: fileUrl },
        {
          new: true,
          runValidators: true,
        }
      );

      if (!updatedUser) {
        return next(new AppError("Failed to update user profile image", 500));
      }

      // Send notification
      try {
        const notificationData = {
          recipient: (req.user as any)._id,
          type: "account_update" as const,
          title: "Profile Image Updated",
          message: "Your profile image has been updated successfully.",
          metadata: {},
          isEmailSent: false,
        };

        await Notification.create(notificationData);
      } catch (error) {
        console.error("Failed to send notification:", error);
      }

      res.status(200).json({
        success: true,
        message: "Profile image uploaded successfully",
        profileImageUrl: fileUrl,
        user: {
          _id: updatedUser._id,
          name: updatedUser.name,
          email: updatedUser.email,
          profileImageUrl: updatedUser.profileImageUrl,
        },
      });
    } catch (error) {
      console.error("Error uploading profile image:", error);
      return next(new AppError("Failed to upload profile image", 500));
    }
  }
);
