import { Request, Response, NextFunction } from "express";
import catchAsync from "../utils/catch-async";
import AppError from "../utils/app-error";
import User from "../models/user.model";
import Company from "../models/company.model";
import { uploadTypes, getFileUrl } from "../services/file-upload.service";

// Get current employer's profile
export const getMyEmployerProfile = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    // Find the user
    const user = await User.findById(req.user._id);
    
    if (!user) {
      return next(new AppError("User not found", 404));
    }

    // Check if the user is an employer
    if (user.role !== "employer") {
      return next(new AppError("User is not an employer", 400));
    }

    // Find the company owned by this employer
    const company = await Company.findOne({ owner: req.user._id });

    // Create employer profile object
    const employerProfile = {
      user: {
        _id: user._id,
        name: user.name,
        email: user.email,
        profileImageUrl: user.profileImageUrl,
      },
      company: company || null,
    };

    res.status(200).json({
      success: true,
      profile: employerProfile,
    });
  }
);

// Update current employer's profile
export const updateMyEmployerProfile = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    // Find the user
    const user = await User.findById(req.user._id);
    
    if (!user) {
      return next(new AppError("User not found", 404));
    }

    // Check if the user is an employer
    if (user.role !== "employer") {
      return next(new AppError("User is not an employer", 400));
    }

    // Extract user-related fields from request body
    const { name, email, ...companyData } = req.body;

    // Update user information if provided
    if (name || email) {
      const userUpdateData: any = {};
      if (name) userUpdateData.name = name;
      if (email) userUpdateData.email = email;

      await User.findByIdAndUpdate(req.user._id, userUpdateData, {
        new: true,
        runValidators: true,
      });
    }

    // Handle company data
    let company = await Company.findOne({ owner: req.user._id });

    if (!company && Object.keys(companyData).length > 0) {
      // Create a new company if none exists and company data is provided
      company = await Company.create({
        ...companyData,
        owner: req.user._id,
      });
    } else if (company && Object.keys(companyData).length > 0) {
      // Update existing company
      company = await Company.findByIdAndUpdate(company._id, companyData, {
        new: true,
        runValidators: true,
      });
    }

    // Get updated user
    const updatedUser = await User.findById(req.user._id);

    // Create updated employer profile object
    const employerProfile = {
      user: {
        _id: updatedUser!._id,
        name: updatedUser!.name,
        email: updatedUser!.email,
        profileImageUrl: updatedUser!.profileImageUrl,
      },
      company: company || null,
    };

    res.status(200).json({
      success: true,
      profile: employerProfile,
    });
  }
);

// Upload profile image middleware
export const uploadEmployerProfileImage =
  uploadTypes.profileImage.single("profileImage");

// Upload and save employer profile image
export const uploadAndSaveEmployerProfileImage = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    // Check if file was uploaded
    if (!req.file) {
      return next(new AppError("No profile image file uploaded", 400));
    }

    // Find the user
    const user = await User.findById(req.user._id);
    
    if (!user) {
      return next(new AppError("User not found", 404));
    }

    // Check if the user is an employer
    if (user.role !== "employer") {
      return next(new AppError("User is not an employer", 400));
    }

    try {
      // Get the file path
      const filePath = `uploads/images/${req.file.filename}`;
      const fileUrl = getFileUrl(filePath);

      // Update the user's profile image URL
      const updatedUser = await User.findByIdAndUpdate(
        req.user._id,
        { profileImageUrl: fileUrl },
        {
          new: true,
          runValidators: true,
        }
      );

      if (!updatedUser) {
        return next(new AppError("Failed to update user profile image", 500));
      }

      res.status(200).json({
        success: true,
        message: "Profile image uploaded successfully",
        profileImageUrl: fileUrl,
        user: {
          _id: updatedUser._id,
          name: updatedUser.name,
          email: updatedUser.email,
          profileImageUrl: updatedUser.profileImageUrl,
        },
      });
    } catch (error) {
      console.error("Error uploading profile image:", error);
      return next(new AppError("Failed to upload profile image", 500));
    }
  }
);
