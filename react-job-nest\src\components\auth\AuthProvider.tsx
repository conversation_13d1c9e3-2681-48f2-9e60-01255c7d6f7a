"use client";

import { useAuthStore } from "@/stores/auth-store";
import { useEffect, useState } from "react";

interface AuthProviderProps {
  children: React.ReactNode;
}

/**
 * AuthProvider component that initializes the auth state when the application loads
 * This component should be placed near the root of the application
 */
export default function AuthProvider({ children }: AuthProviderProps) {
  const { init } = useAuthStore();
  const [initialized, setInitialized] = useState(false);

  // Initialize auth state when the component mounts, but only once
  useEffect(() => {
    if (!initialized) {
      init().finally(() => {
        setInitialized(true);
      });
    }
  }, [init, initialized]);

  return <>{children}</>;
}
