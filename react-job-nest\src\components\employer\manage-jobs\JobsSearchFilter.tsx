"use client";

import { Select, TextInput } from "@mantine/core";
import { useState } from "react";
import { Fa<PERSON><PERSON><PERSON>, FaSearch } from "react-icons/fa";

export default function JobsSearchFilter() {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedStatus, setSelectedStatus] = useState<string | null>(null);

  // Filter jobs based on selected status and search query
  // const filteredJobs = jobs.filter((job) => {
  //   const matchesStatus = selectedStatus ? job.status === selectedStatus : true;
  //   const matchesSearch = job.title
  //     .toLowerCase()
  //     .includes(searchQuery.toLowerCase());
  //   return matchesStatus && matchesSearch;
  // });

  return (
    <div className="flex flex-col gap-4 sm:flex-row sm:items-center">
      <TextInput
        placeholder="Search by job title"
        leftSection={<FaSearch size={16} className="text-gray-500" />}
        className="flex-1"
        value={searchQuery}
        onChange={(e) => setSearchQuery(e.target.value)}
      />
      <Select
        placeholder="Filter by status"
        data={[
          { value: "Active", label: "Active" },
          { value: "Pending", label: "Pending" },
          { value: "Closed", label: "Closed" },
        ]}
        leftSection={<FaFilter size={16} className="text-gray-500" />}
        className="w-full sm:w-48"
        value={selectedStatus}
        onChange={setSelectedStatus}
      />
    </div>
  );
}
