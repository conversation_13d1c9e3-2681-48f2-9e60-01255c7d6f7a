/**
 * <PERSON><PERSON><PERSON> to directly modify the database to disable subscription requirements
 * Run this script with: node src/scripts/disable-subscription-check.js
 */

require('dotenv').config();
const mongoose = require('mongoose');

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/job-nest')
  .then(() => {
    console.log('MongoDB Connected');
    disableSubscriptionCheck();
  })
  .catch(err => {
    console.error('Error connecting to MongoDB:', err);
    process.exit(1);
  });

async function disableSubscriptionCheck() {
  try {
    // Get direct access to the collections
    const db = mongoose.connection.db;
    const usersCollection = db.collection('users');
    const subscriptionPlansCollection = db.collection('subscriptionplans');
    const userSubscriptionsCollection = db.collection('usersubscriptions');
    
    console.log('Connected to collections successfully');
    
    // 1. Create an unlimited subscription plan
    const unlimitedPlan = {
      name: 'Unlimited Developer Plan',
      description: 'Unlimited plan for development purposes',
      price: 0,
      annualPrice: 0,
      currency: 'USD',
      billingPeriod: 'month',
      features: {
        activeJobs: 'unlimited',
        jobVisibilityDays: 90,
        cvViews: 'unlimited',
        featuredJobs: 'unlimited',
        priorityPlacement: true,
        featuredCompanyProfile: true,
        candidateMatching: true,
        advancedAnalytics: true,
        dedicatedSupport: true,
        customFeatures: ['All features enabled for development']
      },
      isActive: true,
      isPopular: false,
      sortOrder: 999,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    // Check if plan already exists
    const existingPlan = await subscriptionPlansCollection.findOne({ name: 'Unlimited Developer Plan' });
    let planId;
    
    if (existingPlan) {
      console.log('Using existing unlimited plan:', existingPlan._id);
      planId = existingPlan._id;
    } else {
      const result = await subscriptionPlansCollection.insertOne(unlimitedPlan);
      planId = result.insertedId;
      console.log('Created new unlimited plan with ID:', planId);
    }
    
    // 2. Find all employer users
    const employers = await usersCollection.find({ role: 'employer' }).toArray();
    console.log(`Found ${employers.length} employer users`);
    
    // 3. Add subscription for each employer
    const startDate = new Date();
    const endDate = new Date();
    endDate.setFullYear(endDate.getFullYear() + 10); // 10 years subscription
    
    for (const employer of employers) {
      // Check if user already has an active subscription
      const existingSubscription = await userSubscriptionsCollection.findOne({
        user: employer._id,
        status: 'active'
      });
      
      if (!existingSubscription) {
        // Create new subscription
        const subscription = {
          user: employer._id,
          plan: planId,
          status: 'active',
          startDate,
          endDate,
          billingPeriod: 'month',
          autoRenew: true,
          paymentMethod: 'development',
          price: 0,
          currency: 'USD',
          usageStats: {
            jobsPosted: 0,
            cvViews: 0,
            featuredJobsUsed: 0
          },
          createdAt: new Date(),
          updatedAt: new Date()
        };
        
        await userSubscriptionsCollection.insertOne(subscription);
        console.log(`Added subscription for user ${employer.email}`);
      } else {
        console.log(`User ${employer.email} already has an active subscription`);
      }
    }
    
    console.log('Subscription check disabled successfully!');
    console.log('All employer users now have an active unlimited subscription');
    
    // Close connection
    mongoose.connection.close();
    console.log('Database connection closed');
    process.exit(0);
  } catch (error) {
    console.error('Error disabling subscription check:', error);
    mongoose.connection.close();
    process.exit(1);
  }
}
