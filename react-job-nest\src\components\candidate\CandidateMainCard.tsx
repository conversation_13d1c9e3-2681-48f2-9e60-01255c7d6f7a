import Card, { type CardProps } from "@/design-system/components/card/Card";
import { type PropsWithChildren } from "react";

/**
 * @deprecated Use Card from design-system/components with variant="candidate" instead
 */
export default function CandidateMainCard({
  className = "",
  children,
  withBorder = true,
  withShadow = false,
  padding = "lg",
  radius = "md",
  ...rest
}: PropsWithChildren<CardProps>) {
  return (
    <Card
      className={className}
      withBorder={withBorder}
      withShadow={withShadow}
      padding={padding}
      radius={radius}
      variant="candidate"
      {...rest}
    >
      {children}
    </Card>
  );
}
