"use client";

import { <PERSON><PERSON><PERSON> } from "@mantine/charts";
import EmployerMainCard from "../EmployerMainCard";
import EmployerSectionHeading from "../EmployerSectionHeading";

const jobStatusData = [
  { name: "Active", value: 8, color: "blue" },
  { name: "Pending", value: 4, color: "yellow" },
  { name: "Closed", value: 3, color: "red" },
];

export default function JobStatusDistribution() {
  return (
    <EmployerMainCard>
      <EmployerSectionHeading>Job Status Distribution</EmployerSectionHeading>
      <PieChart data={jobStatusData} withTooltip withLabels size={300} />
    </EmployerMainCard>
  );
}
