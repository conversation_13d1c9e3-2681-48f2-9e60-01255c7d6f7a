import express from 'express';
import {
  searchJobs,
  getJobCategories,
  getJobTypes,
  getExperienceLevels,
  getJobLocations,
} from '../controllers/job-search.controller';

const router = express.Router();

// Public routes - anyone can search for jobs
router.get('/search', searchJobs);
router.get('/categories', getJobCategories);
router.get('/types', getJobTypes);
router.get('/experience-levels', getExperienceLevels);
router.get('/locations', getJobLocations);

export default router;
