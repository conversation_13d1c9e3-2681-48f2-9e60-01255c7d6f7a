import mongoose, { Document, Schema } from 'mongoose';

export type JobType = 'full-time' | 'part-time' | 'contract' | 'internship' | 'temporary';
export type JobLocation = 'remote' | 'onsite' | 'hybrid';

export interface IJob extends Document {
  title: string;
  company: mongoose.Types.ObjectId;
  description: string;
  requirements: string;
  jobType: JobType;
  location: JobLocation;
  category: string;
  tags: string[];
  minSalary?: number;
  maxSalary?: number;
  currency?: string;
  showSalary: boolean;
  benefits: string[];
  applicationDeadline?: Date;
  isActive: boolean;
  questions: string[];
  createdAt: Date;
  updatedAt: Date;
  requiredExperience: string;
}

const jobSchema = new Schema<IJob>(
  {
    title: {
      type: String,
      required: [true, 'Job title is required'],
      trim: true,
      minlength: [3, 'Job title must be at least 3 characters long'],
      maxlength: [100, 'Job title must be at most 100 characters long'],
    },
    company: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Company',
      required: [true, 'Company is required'],
    },
    description: {
      type: String,
      required: [true, 'Job description is required'],
      minlength: [10, 'Job description must be at least 10 characters long'],
    },
    requirements: {
      type: String,
      required: [true, 'Job requirements are required'],
      minlength: [10, 'Job requirements must be at least 10 characters long'],
    },
    jobType: {
      type: String,
      required: [true, 'Job type is required'],
      enum: {
        values: ['full-time', 'part-time', 'contract', 'internship', 'temporary'],
        message: 'Job type must be one of: full-time, part-time, contract, internship, temporary',
      },
    },
    location: {
      type: String,
      required: [true, 'Job location is required'],
      enum: {
        values: ['remote', 'onsite', 'hybrid'],
        message: 'Job location must be one of: remote, onsite, hybrid',
      },
    },
    category: {
      type: String,
      required: [true, 'Job category is required'],
    },
    tags: {
      type: [String],
      default: [],
    },
    minSalary: {
      type: Number,
      min: [0, 'Minimum salary cannot be negative'],
    },
    maxSalary: {
      type: Number,
      min: [0, 'Maximum salary cannot be negative'],
    },
    currency: {
      type: String,
    },
    showSalary: {
      type: Boolean,
      default: false,
    },
    benefits: {
      type: [String],
      default: [],
    },
    applicationDeadline: {
      type: Date,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    questions: {
      type: [String],
      default: [],
    },
    requiredExperience: {
      type: String,
      required: [true, 'Required experience is required'],
    },
  },
  {
    timestamps: true, // Automatically add createdAt and updatedAt fields
  }
);

// Create indexes for better search performance
jobSchema.index({ title: 'text', description: 'text', tags: 'text' });
jobSchema.index({ category: 1 });
jobSchema.index({ jobType: 1 });
jobSchema.index({ location: 1 });
jobSchema.index({ isActive: 1 });
jobSchema.index({ company: 1 });

const Job = mongoose.model<IJob>('Job', jobSchema);

export default Job;
