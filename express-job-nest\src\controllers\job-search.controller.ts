import { Request, Response, NextFunction } from 'express';
import { catchAsync } from '../utils/catch-async';
import Job from '../models/job.model';
import Company from '../models/company.model';

// Advanced job search with filters
export const searchJobs = catchAsync(
  async (req: Request, res: Response, _next: NextFunction) => {
    const {
      keyword,
      location,
      category,
      jobType,
      experienceLevel,
      salary,
      company,
      postedWithin,
      page = 1,
      limit = 10,
      sortBy = 'createdAt',
      sortOrder = 'desc',
    } = req.query;

    // Build the query
    const query: any = { isActive: true };

    // Keyword search (title, description, skills, tags)
    if (keyword) {
      query.$or = [
        { title: { $regex: keyword, $options: 'i' } },
        { description: { $regex: keyword, $options: 'i' } },
        { skills: { $regex: keyword, $options: 'i' } },
        { tags: { $regex: keyword, $options: 'i' } },
      ];
    }

    // Location filter
    if (location) {
      query.location = { $regex: location, $options: 'i' };
    }

    // Category filter
    if (category) {
      query.category = category;
    }

    // Job type filter (full-time, part-time, contract, etc.)
    if (jobType) {
      query.jobType = jobType;
    }

    // Experience level filter
    if (experienceLevel) {
      query.requiredExperience = experienceLevel;
    }

    // Salary range filter
    if (salary) {
      const [min, max] = (salary as string).split('-').map(Number);
      if (min && max) {
        query.$or = [
          { 'salary.min': { $gte: min, $lte: max } },
          { 'salary.max': { $gte: min, $lte: max } },
          {
            $and: [
              { 'salary.min': { $lte: min } },
              { 'salary.max': { $gte: max } },
            ],
          },
        ];
      } else if (min) {
        query.$or = [
          { 'salary.min': { $gte: min } },
          { 'salary.max': { $gte: min } },
        ];
      }
    }

    // Company filter
    if (company) {
      // Find company by name
      const companies = await Company.find({
        name: { $regex: company, $options: 'i' },
      }).select('_id');
      
      if (companies.length > 0) {
        const companyIds = companies.map((c) => c._id);
        query.company = { $in: companyIds };
      } else {
        // If no companies match, return empty result
        return res.status(200).json({
          success: true,
          count: 0,
          totalPages: 0,
          currentPage: parseInt(page as string),
          jobs: [],
        });
      }
    }

    // Posted within filter (in days)
    if (postedWithin) {
      const daysAgo = new Date();
      daysAgo.setDate(daysAgo.getDate() - parseInt(postedWithin as string));
      query.createdAt = { $gte: daysAgo };
    }

    // Calculate pagination
    const pageNum = parseInt(page as string);
    const limitNum = parseInt(limit as string);
    const skip = (pageNum - 1) * limitNum;

    // Determine sort order
    const sort: any = {};
    sort[sortBy as string] = sortOrder === 'asc' ? 1 : -1;

    // Execute query with pagination
    const jobs = await Job.find(query)
      .populate({
        path: 'company',
        select: 'name logo',
      })
      .sort(sort)
      .skip(skip)
      .limit(limitNum);

    // Get total count for pagination
    const totalJobs = await Job.countDocuments(query);
    const totalPages = Math.ceil(totalJobs / limitNum);

    res.status(200).json({
      success: true,
      count: jobs.length,
      totalJobs,
      totalPages,
      currentPage: pageNum,
      jobs,
    });
  }
);

// Get job categories for filtering
export const getJobCategories = catchAsync(
  async (_req: Request, res: Response, _next: NextFunction) => {
    const categories = await Job.distinct('category');

    res.status(200).json({
      success: true,
      categories,
    });
  }
);

// Get job types for filtering
export const getJobTypes = catchAsync(
  async (_req: Request, res: Response, _next: NextFunction) => {
    const jobTypes = await Job.distinct('jobType');

    res.status(200).json({
      success: true,
      jobTypes,
    });
  }
);

// Get experience levels for filtering
export const getExperienceLevels = catchAsync(
  async (_req: Request, res: Response, _next: NextFunction) => {
    const experienceLevels = await Job.distinct('requiredExperience');

    res.status(200).json({
      success: true,
      experienceLevels,
    });
  }
);

// Get locations for filtering
export const getJobLocations = catchAsync(
  async (_req: Request, res: Response, _next: NextFunction) => {
    const locations = await Job.distinct('location');

    res.status(200).json({
      success: true,
      locations,
    });
  }
);
