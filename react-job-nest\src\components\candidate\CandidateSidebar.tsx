"use client";

import { Sidebar, type SidebarLink } from "@/design-system/components";
import { URLS } from "@/utils/urls";
import {
  FaBriefcase,
  FaClipboardList,
  FaTachometerAlt,
  FaUser,
} from "react-icons/fa";

const candidateLinks: SidebarLink[] = [
  {
    id: 1,
    name: "Dashboard",
    href: URLS.candidate.dashboard,
    icon: FaTachometerAlt,
  },
  {
    id: 2,
    name: "Browse Jobs",
    href: URLS.candidate.jobs,
    icon: FaBriefcase,
  },
  {
    id: 3,
    name: "My Applications",
    href: URLS.candidate.applications,
    icon: FaClipboardList,
  },
  {
    id: 4,
    name: "Profile",
    href: URLS.candidate.profile,
    icon: FaUser,
  },
];

interface CandidateSidebarProps {
  onLinkClick?: () => void;
}

export default function CandidateSidebar({
  onLinkClick,
}: CandidateSidebarProps) {
  return (
    <Sidebar
      title="Candidate Portal"
      links={candidateLinks}
      onLinkClick={onLinkClick}
    />
  );
}
