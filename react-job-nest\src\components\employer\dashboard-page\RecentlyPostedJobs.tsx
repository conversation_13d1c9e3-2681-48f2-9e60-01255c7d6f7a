import employerApi from "@/services/employer-api";
import { Badge, Button, Group, LoadingOverlay, Text } from "@mantine/core";
import { notifications } from "@mantine/notifications";
import { useState } from "react";
import {
  FaCalendarAlt,
  FaEye,
  FaPencilAlt,
  FaUserFriends,
} from "react-icons/fa";
import { useNavigate } from "react-router";

// Activity interface
interface Activity {
  id: string;
  type: string;
  message: string;
  date: string;
  status?: string;
  data?: {
    jobId?: string;
    jobTitle?: string;
    companyName?: string;
    isActive?: boolean;
    location?: string;
  };
}

interface RecentlyPostedJobsProps {
  jobs?: Activity[];
}

export default function RecentlyPostedJobs({
  jobs = [],
}: RecentlyPostedJobsProps) {
  const navigate = useNavigate();
  const [loadingStates, setLoadingStates] = useState<Record<string, boolean>>(
    {},
  );

  // Map activities to job format
  const recentJobs = jobs.map((activity) => {
    // Extract job title from message or use data
    let title = activity.data?.jobTitle || "";
    if (!title && activity.message) {
      title = activity.message.replace("Job posted: ", "");
    }

    return {
      id: activity.id,
      jobId: activity.data?.jobId || activity.id,
      title,
      status:
        activity.data?.isActive === false
          ? "Closed"
          : activity.status === "closed"
            ? "Closed"
            : activity.status === "active"
              ? "Active"
              : "Active",
      postedDate: new Date(activity.date).toLocaleDateString(),
      applicants: 0, // We don't have this data yet from the backend
      location: activity.data?.location || "Not specified",
      companyName: activity.data?.companyName || "",
    };
  });

  // Handle view job action
  const handleViewJob = (jobId: string) => {
    if (jobId) {
      setLoadingStates((prev) => ({ ...prev, [jobId]: true }));

      // First try to get the job details to verify it exists
      employerApi.jobs
        .getJobById(jobId)
        .then((response: any) => {
          if (response.data.success) {
            // Navigate to the job details page
            navigate(`/employer/manage-jobs/${jobId}/applications`);
          } else {
            throw new Error("Job not found");
          }
        })
        .catch((error: any) => {
          console.error("Error viewing job:", error);
          notifications.show({
            title: "Error",
            message:
              "Cannot view job. The job may have been deleted or you don't have permission to view it.",
            color: "red",
          });
        })
        .finally(() => {
          setLoadingStates((prev) => ({ ...prev, [jobId]: false }));
        });
    } else {
      notifications.show({
        title: "Error",
        message: "Cannot view job. Job ID not available.",
        color: "red",
      });
    }
  };

  // Handle edit job action
  const handleEditJob = (jobId: string) => {
    if (jobId) {
      setLoadingStates((prev) => ({ ...prev, [jobId]: true }));

      // First try to get the job details to verify it exists
      employerApi.jobs
        .getJobById(jobId)
        .then((response: any) => {
          if (response.data.success) {
            // Navigate to the job edit page
            navigate(`/employer/manage-jobs/${jobId}/edit`);
          } else {
            throw new Error("Job not found");
          }
        })
        .catch((error: any) => {
          console.error("Error editing job:", error);
          notifications.show({
            title: "Error",
            message:
              "Cannot edit job. The job may have been deleted or you don't have permission to edit it.",
            color: "red",
          });
        })
        .finally(() => {
          setLoadingStates((prev) => ({ ...prev, [jobId]: false }));
        });
    } else {
      notifications.show({
        title: "Error",
        message: "Cannot edit job. Job ID not available.",
        color: "red",
      });
    }
  };

  // Handle view all jobs action
  const handleViewAllJobs = () => {
    navigate("/employer/manage-jobs");
  };
  return (
    <div className="relative">
      {recentJobs.length > 0 ? (
        <>
          {recentJobs.map((job) => (
            <div key={job.id} className="mb-4 border-b pb-4 relative">
              <LoadingOverlay visible={loadingStates[job.id] || false} />
              <Group justify="space-between" wrap="nowrap" className="mb-2">
                <div>
                  <Text fw={600}>{job.title}</Text>
                  <Text size="sm" c="dimmed">
                    {job.location}
                  </Text>
                </div>
                <Badge
                  size="lg"
                  color={
                    job.status === "Active"
                      ? "blue"
                      : job.status === "Pending"
                        ? "yellow"
                        : "red"
                  }
                >
                  {job.status}
                </Badge>
              </Group>

              <Group className="mt-2">
                <Group gap="xs">
                  <FaCalendarAlt size={14} className="text-blue-500" />
                  <Text size="sm">Posted: {job.postedDate}</Text>
                </Group>
                <Group gap="xs">
                  <FaUserFriends size={14} className="text-blue-500" />
                  <Text size="sm">{job.applicants} Applicants</Text>
                </Group>
              </Group>

              <Group className="mt-3">
                <Button
                  variant="light"
                  size="xs"
                  leftSection={<FaEye size={12} />}
                  onClick={() => handleViewJob(job.jobId)}
                >
                  View
                </Button>
                <Button
                  variant="outline"
                  size="xs"
                  leftSection={<FaPencilAlt size={12} />}
                  onClick={() => handleEditJob(job.jobId)}
                >
                  Edit
                </Button>
              </Group>
            </div>
          ))}

          <Group justify="center" className="mt-4">
            <Button variant="subtle" onClick={handleViewAllJobs}>
              View All Jobs
            </Button>
          </Group>
        </>
      ) : (
        <div className="py-6 text-center border rounded-md">
          <Text size="lg" fw={500} className="mb-2">
            No jobs posted yet
          </Text>
          <Text size="sm" c="dimmed" className="mb-4">
            You haven&apos;t posted any jobs recently. Create a new job to
            attract candidates.
          </Text>
          <Button
            variant="filled"
            onClick={() => navigate("/employer/create-job")}
            leftSection={<FaPencilAlt size={14} />}
          >
            Post a New Job
          </Button>
        </div>
      )}
    </div>
  );
}
