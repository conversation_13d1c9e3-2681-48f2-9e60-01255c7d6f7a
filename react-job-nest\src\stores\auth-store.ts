"use client";

import api from "@/services/api";
import {
  type AuthState,
  type LoginCredentials,
  type RegisterData,
} from "@/types/auth";
import { cache } from "@/utils/cache";
import { notifications } from "@mantine/notifications";
import { create } from "zustand";

// Define the auth store type
interface AuthStore extends AuthState {
  // Actions
  setUser: (user: AuthState["user"]) => void;
  setLoading: (isLoading: boolean) => void;
  setError: (error: string | null) => void;
  setLoggedIn: (isLoggedIn: boolean) => void;
  setAccessToken: (token: string | null) => void;

  // Auth methods
  login: (credentials: LoginCredentials) => Promise<boolean>;
  register: (data: RegisterData) => Promise<boolean>;
  logout: () => Promise<boolean>;
  init: () => Promise<void>;
}

// Create the auth store
export const useAuthStore = create<AuthStore>((set) => ({
  // Initial state
  user: null,
  accessToken: null,
  isLoggedIn: false,
  isLoading: true,
  error: null,

  // Actions
  setUser: (user) => set({ user }),
  setLoading: (isLoading) => set({ isLoading }),
  setError: (error) => set({ error }),
  setLoggedIn: (isLoggedIn) => set({ isLoggedIn }),
  setAccessToken: (accessToken) => set({ accessToken }),

  // Auth methods
  init: async () => {
    return new Promise<void>(async (resolve) => {
      try {
        // Set loading state
        set({
          isLoading: true,
        });

        // Check if we already have a token in the cache
        const existingToken = cache.get("accessToken");

        // If no token exists, we're not logged in
        if (!existingToken) {
          set({
            isLoading: false,
            isLoggedIn: false,
            user: null,
            accessToken: null,
          });
          resolve();
          return;
        }

        // Check if backend is available
        try {
          const response = await api.auth.getCurrentUser();
          const data = response.data;

          if (data.success && data.user) {
            // Store the token in cache if it exists
            if (data.token) {
              cache.set("accessToken", data.token);
            }

            set({
              user: data.user,
              accessToken: data.token || existingToken,
              isLoggedIn: true,
              isLoading: false,
              error: null,
            });
          } else {
            // If the backend responds but user data is not available,
            // clear the token and set not logged in
            cache.clear();
            set({
              isLoading: false,
              isLoggedIn: false,
              user: null,
              accessToken: null,
            });
          }
        } catch (error: any) {
          console.warn("Backend connection issue:", error.message);

          // For development purposes, we'll set isLoading to false
          // but won't show an error to the user
          set({
            isLoading: false,
          });

          // In a production environment, you might want to show a notification
          // that the backend is not available
          if (process.env.NODE_ENV === "development") {
            console.info(
              "Development mode: Backend connection failed. This is expected if the backend server is not running.",
            );
          }
        }
      } catch (error) {
        console.error("Failed to initialize auth:", error);
        set({
          isLoading: false,
          error: "Failed to initialize authentication",
        });
      } finally {
        // Always resolve the promise to indicate initialization is complete
        resolve();
      }
    });
  },

  login: async (credentials) => {
    try {
      // Set loading state
      set({
        isLoading: true,
        error: null,
      });

      // Check if we're in development mode and backend might not be available
      if (process.env.NODE_ENV === "development") {
        try {
          // Make API request using our API client
          const response = await api.auth.login(credentials);
          const data = response.data;

          if (!data.success) {
            // Handle error
            set({
              isLoading: false,
              error: data.message || "Login failed",
            });

            // Show error notification
            notifications.show({
              title: "Login Failed",
              message: data.message || "Invalid email or password",
              color: "red",
            });

            return false;
          }

          if (!data.user) {
            // Handle missing user data
            set({
              isLoading: false,
              error: "Invalid response from server",
            });

            // Show error notification
            notifications.show({
              title: "Login Failed",
              message: "Invalid response from server",
              color: "red",
            });

            return false;
          }

          // Store the token in cache if it exists
          if (data.token) {
            cache.set("accessToken", data.token);
          }

          // Update state with user data
          set({
            user: data.user,
            accessToken: data.token || null,
            isLoggedIn: true,
            isLoading: false,
            error: null,
          });

          // Show success notification
          notifications.show({
            title: "Login Successful",
            message: "Welcome back!",
            color: "green",
          });

          return true;
        } catch (error: any) {
          console.warn("Backend connection issue:", error.message);

          // For development purposes, we'll simulate a successful login
          // This is just for testing the UI while the backend is not available
          console.info(
            "Development mode: Simulating successful login for testing purposes",
          );

          // Create a mock user for development
          const mockUser = {
            id: 1,
            name: credentials.email.split("@")[0],
            email: credentials.email,
            role: "candidate" as const,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          };

          // Update state with mock user data
          set({
            user: mockUser,
            accessToken: "mock-token-for-development",
            isLoggedIn: true,
            isLoading: false,
            error: null,
          });

          // Show success notification
          notifications.show({
            title: "Development Mode",
            message:
              "Simulated login successful. Backend connection not available.",
            color: "blue",
          });

          return true;
        }
      } else {
        // Production mode - normal flow
        const response = await api.auth.login(credentials);
        const data = response.data;

        if (!data.success) {
          // Handle error
          set({
            isLoading: false,
            error: data.message || "Login failed",
          });

          // Show error notification
          notifications.show({
            title: "Login Failed",
            message: data.message || "Invalid email or password",
            color: "red",
          });

          return false;
        }

        if (!data.user) {
          // Handle missing user data
          set({
            isLoading: false,
            error: "Invalid response from server",
          });

          // Show error notification
          notifications.show({
            title: "Login Failed",
            message: "Invalid response from server",
            color: "red",
          });

          return false;
        }

        // Store the token in cache if it exists
        if (data.token) {
          cache.set("accessToken", data.token);
        }

        // Update state with user data
        set({
          user: data.user,
          accessToken: data.token || null,
          isLoggedIn: true,
          isLoading: false,
          error: null,
        });

        // Show success notification
        notifications.show({
          title: "Login Successful",
          message: "Welcome back!",
          color: "green",
        });

        return true;
      }
    } catch (error: any) {
      console.error("Login error:", error);

      // Get error message from response if available
      const errorMessage =
        error.response?.data?.message || "An unexpected error occurred";

      // Update state with error
      set({
        isLoading: false,
        error: errorMessage,
      });

      // Show error notification
      notifications.show({
        title: "Login Failed",
        message: errorMessage,
        color: "red",
      });

      return false;
    }
  },

  register: async (data) => {
    try {
      // Set loading state
      set({
        isLoading: true,
        error: null,
      });

      // Check if we're in development mode and backend might not be available
      if (process.env.NODE_ENV === "development") {
        try {
          // Make API request using our API client
          const response = await api.auth.register(data);
          const responseData = response.data;

          if (!responseData.success) {
            // Handle error
            set({
              isLoading: false,
              error: responseData.message || "Registration failed",
            });

            // Show error notification
            notifications.show({
              title: "Registration Failed",
              message: responseData.message || "Could not create account",
              color: "red",
            });

            return false;
          }

          // Store the token in cache if it exists
          if (responseData.token) {
            cache.set("accessToken", responseData.token);
          }

          // Update state with user data
          set({
            user: responseData.user,
            accessToken: responseData.token || null,
            isLoggedIn: true,
            isLoading: false,
            error: null,
          });

          // Show success notification
          notifications.show({
            title: "Registration Successful",
            message: "Your account has been created successfully!",
            color: "green",
          });

          return true;
        } catch (error: any) {
          console.warn("Backend connection issue:", error.message);

          // For development purposes, we'll simulate a successful registration
          // This is just for testing the UI while the backend is not available
          console.info(
            "Development mode: Simulating successful registration for testing purposes",
          );

          // Create a mock user for development
          const mockUser = {
            id: 1,
            name: data.name,
            email: data.email,
            role: data.role,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          };

          // Update state with mock user data
          set({
            user: mockUser,
            accessToken: "mock-token-for-development",
            isLoggedIn: true,
            isLoading: false,
            error: null,
          });

          // Show success notification
          notifications.show({
            title: "Development Mode",
            message:
              "Simulated registration successful. Backend connection not available.",
            color: "blue",
          });

          return true;
        }
      } else {
        // Production mode - normal flow
        const response = await api.auth.register(data);
        const responseData = response.data;

        if (!responseData.success) {
          // Handle error
          set({
            isLoading: false,
            error: responseData.message || "Registration failed",
          });

          // Show error notification
          notifications.show({
            title: "Registration Failed",
            message: responseData.message || "Could not create account",
            color: "red",
          });

          return false;
        }

        // Store the token in cache if it exists
        if (responseData.token) {
          cache.set("accessToken", responseData.token);
        }

        // Update state with user data
        set({
          user: responseData.user,
          accessToken: responseData.token || null,
          isLoggedIn: true,
          isLoading: false,
          error: null,
        });

        // Show success notification
        notifications.show({
          title: "Registration Successful",
          message: "Your account has been created successfully!",
          color: "green",
        });

        return true;
      }
    } catch (error: any) {
      console.error("Registration error:", error);

      // Get error message from response if available
      const errorMessage =
        error.response?.data?.message || "An unexpected error occurred";

      // Update state with error
      set({
        isLoading: false,
        error: errorMessage,
      });

      // Show error notification
      notifications.show({
        title: "Registration Failed",
        message: errorMessage,
        color: "red",
      });

      return false;
    }
  },

  logout: async () => {
    try {
      // Set loading state
      set({
        isLoading: true,
      });

      // Check if we're in development mode and backend might not be available
      if (process.env.NODE_ENV === "development") {
        try {
          // Make API request using our API client
          await api.auth.logout();

          // Clear the token from cache
          cache.clear();

          // Update state
          set({
            user: null,
            accessToken: null,
            isLoggedIn: false,
            isLoading: false,
            error: null,
          });

          // Show success notification
          notifications.show({
            title: "Logged Out",
            message: "You have been logged out successfully",
            color: "blue",
          });

          return true;
        } catch (error: any) {
          console.warn("Backend connection issue:", error.message);

          // For development purposes, we'll simulate a successful logout
          // This is just for testing the UI while the backend is not available
          console.info(
            "Development mode: Simulating successful logout for testing purposes",
          );

          // Clear the token from cache
          cache.clear();

          // Update state
          set({
            user: null,
            accessToken: null,
            isLoggedIn: false,
            isLoading: false,
            error: null,
          });

          // Show success notification
          notifications.show({
            title: "Development Mode",
            message:
              "Simulated logout successful. Backend connection not available.",
            color: "blue",
          });

          return true;
        }
      } else {
        // Production mode - normal flow
        await api.auth.logout();

        // Clear the token from cache
        cache.clear();

        // Update state
        set({
          user: null,
          accessToken: null,
          isLoggedIn: false,
          isLoading: false,
          error: null,
        });

        // Show success notification
        notifications.show({
          title: "Logged Out",
          message: "You have been logged out successfully",
          color: "blue",
        });

        return true;
      }
    } catch (error: any) {
      console.error("Logout error:", error);

      // Get error message from response if available
      const errorMessage =
        error.response?.data?.message || "An error occurred during logout";

      // Update state
      set({
        isLoading: false,
        error: errorMessage,
      });

      // Show error notification
      notifications.show({
        title: "Logout Failed",
        message: errorMessage,
        color: "red",
      });

      return false;
    }
  },
}));
