"use client";

import { userEditSchema } from "@/schemas/admin/user-management-schema";
import { useUserEditModal } from "@/stores/modal-store";
import {
  Button,
  Group,
  LoadingOverlay,
  Modal,
  Select,
  TextInput,
  Textarea,
} from "@mantine/core";
import { useForm } from "@mantine/form";
import { zodResolver } from "mantine-form-zod-resolver";
import { useEffect, useRef, useState } from "react";
import { FaEnvelope, FaPhone, FaUser } from "react-icons/fa";

interface UserEditModalProps {
  userId?: number | null;
}

// Mock user data
const mockUserDetails = {
  id: 1,
  name: "<PERSON>",
  email: "<EMAIL>",
  role: "candidate",
  status: "active",
  phone: "+****************",
  address: "123 Main St, Anytown, USA",
  bio: "Experienced software developer with 5 years of experience in web development.",
};

export default function UserEditModal({ userId }: UserEditModalProps) {
  const [isLoading, setIsLoading] = useState(false);

  console.log(userId);

  // In a real application, you would fetch user details based on userId
  const user = mockUserDetails;

  const form = useForm({
    initialValues: {
      name: "",
      email: "",
      role: "candidate" as const,
      status: "active" as const,
      phone: "",
      address: "",
      bio: "",
    },
    validate: zodResolver(userEditSchema),
  });

  const { close, isOpen } = useUserEditModal();

  const closeModal = () => {
    // Reset form when closing the modal
    form.reset();
    close();
  };

  // Use a ref to track if we've already set the form values
  const formInitialized = useRef(false);

  useEffect(() => {
    // Only set form values once when the modal is opened and not already initialized
    if (user && isOpen && !formInitialized.current) {
      form.setValues({
        name: user.name,
        email: user.email,
        role: user.role as any,
        status: user.status as any,
        phone: user.phone,
        address: user.address,
        bio: user.bio,
      });
      formInitialized.current = true;
    }

    // Reset the initialization flag when the modal closes
    if (!isOpen) {
      formInitialized.current = false;
    }
  }, [isOpen, user, form]);

  const handleSubmit = (values: typeof form.values) => {
    setIsLoading(true);

    // Simulate API call
    setTimeout(() => {
      console.log("Updated user:", values);
      setIsLoading(false);
      closeModal();
    }, 1000);
  };

  // Already declared above

  return (
    <Modal
      opened={isOpen}
      onClose={closeModal}
      title="Edit User"
      size="lg"
      padding="xl"
    >
      <div className="relative">
        <LoadingOverlay visible={isLoading} overlayProps={{ blur: 2 }} />

        <form onSubmit={form.onSubmit(handleSubmit)}>
          <div className="flex flex-col gap-4">
            <TextInput
              label="Full Name"
              placeholder="John Doe"
              required
              leftSection={<FaUser size={14} />}
              {...form.getInputProps("name")}
            />

            <TextInput
              label="Email"
              placeholder="<EMAIL>"
              required
              leftSection={<FaEnvelope size={14} />}
              {...form.getInputProps("email")}
            />

            <Select
              label="Role"
              placeholder="Select role"
              required
              data={[
                { value: "candidate", label: "Candidate" },
                { value: "employer", label: "Employer" },
                { value: "admin", label: "Admin" },
              ]}
              {...form.getInputProps("role")}
            />

            <Select
              label="Status"
              placeholder="Select status"
              required
              data={[
                { value: "active", label: "Active" },
                { value: "inactive", label: "Inactive" },
                { value: "blocked", label: "Blocked" },
              ]}
              {...form.getInputProps("status")}
            />

            <TextInput
              label="Phone"
              placeholder="+****************"
              leftSection={<FaPhone size={14} />}
              {...form.getInputProps("phone")}
            />

            <TextInput
              label="Address"
              placeholder="123 Main St, Anytown, USA"
              {...form.getInputProps("address")}
            />

            <Textarea
              label="Bio"
              placeholder="User biography"
              {...form.getInputProps("bio")}
            />

            <Group justify="flex-end" mt="xl">
              <Button variant="outline" onClick={closeModal}>
                Cancel
              </Button>
              <Button type="submit">Save Changes</Button>
            </Group>
          </div>
        </form>
      </div>
    </Modal>
  );
}
