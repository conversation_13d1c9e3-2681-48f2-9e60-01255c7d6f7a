import { filterSchema, searchSchema } from "@/schemas/candidate";
import candidateApi from "@/services/candidate-api";
import { useJobsStore } from "@/stores/candidate-store";
import { useForm } from "@mantine/form";
import { notifications } from "@mantine/notifications";
import { zodResolver } from "mantine-form-zod-resolver";
import { useEffect, useState } from "react";

export default function useJobsPage() {
  // Get the jobs store
  const { setJobs, setLoading, setError } = useJobsStore();

  // State for pagination
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalJobs, setTotalJobs] = useState(0);

  // Search form
  const searchForm = useForm({
    initialValues: { search: "" },
    validate: zodResolver(searchSchema),
  });

  // Filter form
  const filterForm = useForm({
    initialValues: {
      jobType: "",
      workType: "",
      category: "",
      datePosted: "",
      experience: "",
      careerLevel: "",
    },
    validate: zod<PERSON><PERSON>olver(filterSchema),
  });

  // Fetch jobs from the backend
  const fetchJobs = async () => {
    try {
      setLoading(true);

      // Build query parameters
      const params: Record<string, any> = {
        page,
        limit: 10,
        sortBy: "createdAt",
        sortOrder: "desc",
      };

      // Add search term if present
      if (searchForm.values.search) {
        params.keyword = searchForm.values.search;
      }

      // Add filters if present
      if (filterForm.values.jobType) {
        params.jobType = filterForm.values.jobType;
      }

      if (filterForm.values.workType) {
        params.location = filterForm.values.workType;
      }

      if (filterForm.values.category) {
        params.category = filterForm.values.category;
      }

      if (filterForm.values.experience) {
        params.experienceLevel = filterForm.values.experience;
      }

      if (filterForm.values.datePosted) {
        params.postedWithin = filterForm.values.datePosted;
      }

      // Make the API call
      const response = await candidateApi.jobs.searchJobs(params);

      if (response.data && response.data.success) {
        // Map backend job format to frontend format
        const mappedJobs = response.data.jobs.map((job) => ({
          ...job,
          id: job._id,
          type: job.jobType,
          workType: job.location, // Backend uses location for what frontend calls workType
          experience: job.requiredExperience,
          datePosted: job.createdAt,
          skills: job.tags || [],
        }));

        setJobs(mappedJobs);
        setTotalPages(response.data.totalPages);
        setTotalJobs(response.data.totalJobs);
        setError(null);
      } else {
        throw new Error("Failed to fetch jobs");
      }
    } catch (error: any) {
      console.error("Error fetching jobs:", error);

      // Show error notification
      notifications.show({
        title: "Error",
        message: error.message || "Failed to fetch jobs",
        color: "red",
      });

      setError(error.message || "Failed to fetch jobs");

      // In development mode, use mock data if backend is not available
      if (process.env.NODE_ENV === "development") {
        console.log("Using mock job data");
        // Import dummy data from the store (already set in the store)
        setError(null);
      }
    } finally {
      setLoading(false);
    }
  };

  // Fetch jobs when search, filters, or page changes
  useEffect(() => {
    fetchJobs();
  }, [page, searchForm.values.search, filterForm.values]);

  // Handle filter removal
  const handleFilterRemove = (filterType: string) => {
    if (filterType === "search") {
      searchForm.reset();
    } else {
      filterForm.setFieldValue(filterType, "");
    }
  };

  // Handle search form submission
  const handleSearch = () => {
    setPage(1); // Reset to first page
    fetchJobs();
  };

  return {
    searchForm,
    filterForm,
    handleFilterRemove,
    handleSearch,
    page,
    setPage,
    totalPages,
    totalJobs,
    refreshJobs: fetchJobs,
  };
}
