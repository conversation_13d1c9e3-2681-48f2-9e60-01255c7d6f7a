import employerApi from "@/services/employer-api";
import { getFullUrl } from "@/utils/url-utils";
import { useCallback, useEffect, useState } from "react";

// Company type from backend
interface Company {
  _id: string;
  name: string;
  description: string;
  logo?: string;
  website?: string;
  industry: string;
  size: string;
  location: string;
  foundedYear?: number;
  owner: string;
  isVerified: boolean;
  isActive: boolean;
  socialLinks: {
    linkedin?: string;
    twitter?: string;
    facebook?: string;
    instagram?: string;
  };
  contactEmail: string;
  contactPhone?: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * Hook to fetch and manage company data
 * @returns Company data and loading state
 */
export function useCompanyData() {
  const [company, setCompany] = useState<Company | null>(null);
  const [logoUrl, setLogoUrl] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch company data
  const fetchCompanyData = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await employerApi.company.getMyCompanies();
      const data = response.data;

      if (data.success && data.companies && data.companies.length > 0) {
        const companyData = data.companies[0];
        setCompany(companyData);

        // Set logo URL if available
        if (companyData.logo) {
          // Handle both relative and absolute URLs
          const fullLogoUrl = getFullUrl(companyData.logo);
          console.log("Setting company logo URL:", fullLogoUrl);
          setLogoUrl(fullLogoUrl);
        } else {
          setLogoUrl(null);
        }
      } else {
        setCompany(null);
        setLogoUrl(null);
      }
    } catch (error: any) {
      console.warn("Error fetching company data:", error);

      // Don't show error messages for 403 errors in development mode
      // This handles the case where the endpoint doesn't exist or user doesn't have permission
      if (
        process.env.NODE_ENV === "development" &&
        (error?.response?.status === 403 ||
          error?.response?.status === 404 ||
          error.message.includes("Network Error"))
      ) {
        console.log("Using mock company data (development mode)");
        setError(null);

        // Create mock company data for development
        const mockCompany = {
          _id: "mock-company-id",
          name: "Mock Company",
          description: "This is a mock company for development",
          industry: "Technology",
          size: "10-50",
          location: "Remote",
          owner: "mock-user-id",
          isVerified: true,
          isActive: true,
          socialLinks: {},
          contactEmail: "<EMAIL>",
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };

        setCompany(mockCompany as any);
        setLogoUrl(null);
        return;
      } else {
        setError("Failed to fetch company data");
      }

      setCompany(null);
      setLogoUrl(null);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Fetch company data on mount
  useEffect(() => {
    fetchCompanyData();
  }, [fetchCompanyData]);

  return {
    company,
    logoUrl,
    isLoading,
    error,
    refetch: fetchCompanyData,
  };
}
