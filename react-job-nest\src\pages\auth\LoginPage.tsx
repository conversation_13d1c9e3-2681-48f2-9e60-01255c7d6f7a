import LoginForm from "@/components/auth/LoginForm";
import { Card, Text, Title } from "@mantine/core";
import { useEffect, useState } from "react";
import { useLocation } from "react-router";

export default function LoginPage() {
  const location = useLocation();
  const [role, setRole] = useState<string | null>(null);

  // Extract role from URL query parameters
  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const roleParam = searchParams.get("role");
    if (roleParam) {
      setRole(roleParam);
    }
  }, [location.search]);

  return (
    <div className="py-16">
      <Card
        shadow="md"
        padding="xl"
        radius="md"
        withBorder
        className="mx-auto w-[500px]"
      >
        <Title order={2} className="mb-2 text-center">
          Login to JobNest
        </Title>

        {role === "employer" && (
          <Text size="sm" color="dimmed" className="mb-6 text-center">
            Access your employer dashboard to post jobs and manage applications
          </Text>
        )}

        {!role && (
          <Text size="sm" color="dimmed" className="mb-6 text-center">
            Sign in to access your account
          </Text>
        )}

        <LoginForm
          initialRole={role as "employer" | "candidate" | "admin" | null}
        />
      </Card>
    </div>
  );
}
