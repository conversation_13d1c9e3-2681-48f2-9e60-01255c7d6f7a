"use client";

import { <PERSON><PERSON><PERSON>r, PageHeading } from "@/design-system/components";
import useCandidateDashboard from "@/hooks/candidate/use-candidate-dashboard";
import {
  Badge,
  Button,
  Card,
  Divider,
  Group,
  LoadingOverlay,
  Progress,
  Text,
  useMantineColorScheme,
} from "@mantine/core";
import {
  FaBriefcase,
  FaBuilding,
  FaCalendarAlt,
  FaCheckCircle,
  FaEye,
  FaMapMarkerAlt,
  FaStar,
} from "react-icons/fa";
import { Link } from "react-router";

export default function CandidateDashboardPage() {
  const { colorScheme } = useMantineColorScheme();
  const isDark = colorScheme === "dark";
  const { isLoading, stats, recommendedJobs } = useCandidateDashboard();

  return (
    <PageContainer
      breadcrumbItems={[{ title: "Home", href: "/" }, { title: "Dashboard" }]}
      variant="candidate"
    >
      <LoadingOverlay visible={isLoading} />
      <PageHeading
        title="Candidate Dashboard"
        subtitle="Welcome back! Here's an overview of your job search"
      />

      {/* Key Metrics */}
      <div className="mb-8">
        <Text
          size="lg"
          fw={700}
          className={`mb-4 ${isDark ? "text-gray-200" : "text-gray-800"}`}
        >
          Your Job Search Overview
        </Text>

        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
          {/* Jobs Applied Metric */}
          <Card
            withBorder
            p="lg"
            radius="md"
            className={`transition-all duration-200 hover:shadow-md ${
              isDark
                ? "border-dark-4 bg-dark-7 hover:border-primary-500/30"
                : "border-gray-200 bg-white hover:border-primary-500/30"
            }`}
            style={{
              borderColor: isDark ? "var(--mantine-color-dark-4)" : undefined,
              backgroundColor: isDark
                ? "var(--mantine-color-dark-7)"
                : undefined,
            }}
          >
            <div className="flex flex-col h-full">
              <div className="flex justify-between items-start mb-4">
                <div
                  className="rounded-full p-3"
                  style={{
                    backgroundColor: isDark
                      ? "rgba(24, 144, 255, 0.2)"
                      : "rgba(24, 144, 255, 0.1)",
                  }}
                >
                  <FaBriefcase className="text-primary-color" size={22} />
                </div>
                <Badge
                  color="teal"
                  variant="light"
                  size="sm"
                  className="animate-pulse"
                >
                  +3 new
                </Badge>
              </div>

              <Text size="sm" c="dimmed" className="mb-1">
                Jobs Applied
              </Text>

              <Text size="xl" fw={700} className="mb-4">
                {stats.total}
              </Text>

              <div className="mt-auto">
                <Group justify="space-between" className="mb-1">
                  <Text size="xs" c="dimmed">
                    Success Rate
                  </Text>
                  <Text
                    size="xs"
                    fw={500}
                    className={isDark ? "text-primary-300" : "text-primary-600"}
                  >
                    {stats.successRate.toFixed(1)}%
                  </Text>
                </Group>
                <Progress
                  value={stats.successRate}
                  size="sm"
                  radius="xl"
                  color="primary"
                  className="transition-all duration-300"
                />
              </div>
            </div>
          </Card>

          {/* Interviews Metric */}
          <Card
            withBorder
            p="lg"
            radius="md"
            className={`transition-all duration-200 hover:shadow-md ${
              isDark
                ? "border-dark-4 bg-dark-7 hover:border-secondary-500/30"
                : "border-gray-200 bg-white hover:border-secondary-500/30"
            }`}
            style={{
              borderColor: isDark ? "var(--mantine-color-dark-4)" : undefined,
              backgroundColor: isDark
                ? "var(--mantine-color-dark-7)"
                : undefined,
            }}
          >
            <div className="flex flex-col h-full">
              <div className="flex justify-between items-start mb-4">
                <div
                  className="rounded-full p-3"
                  style={{
                    backgroundColor: isDark
                      ? "rgba(19, 194, 194, 0.2)"
                      : "rgba(19, 194, 194, 0.1)",
                  }}
                >
                  <FaCheckCircle className="text-secondary-color" size={22} />
                </div>
                <Badge color="teal" variant="light" size="sm">
                  +1 new
                </Badge>
              </div>

              <Text size="sm" c="dimmed" className="mb-1">
                Interviews
              </Text>

              <Text size="xl" fw={700} className="mb-4">
                {stats.interview}
              </Text>

              <div className="mt-auto">
                <Group justify="space-between" className="mb-1">
                  <Text size="xs" c="dimmed">
                    Shortlisted
                  </Text>
                  <Text
                    size="xs"
                    fw={500}
                    className={
                      isDark ? "text-secondary-300" : "text-secondary-600"
                    }
                  >
                    {stats.shortlisted}
                  </Text>
                </Group>
                <Progress
                  value={
                    stats.total > 0
                      ? (stats.shortlisted / stats.total) * 100
                      : 0
                  }
                  size="sm"
                  radius="xl"
                  color="secondary"
                  className="transition-all duration-300"
                />
              </div>
            </div>
          </Card>

          {/* Saved Jobs Metric */}
          <Card
            withBorder
            p="lg"
            radius="md"
            className={`transition-all duration-200 hover:shadow-md ${
              isDark
                ? "border-dark-4 bg-dark-7 hover:border-accent-500/30"
                : "border-gray-200 bg-white hover:border-accent-500/30"
            }`}
            style={{
              borderColor: isDark ? "var(--mantine-color-dark-4)" : undefined,
              backgroundColor: isDark
                ? "var(--mantine-color-dark-7)"
                : undefined,
            }}
          >
            <div className="flex flex-col h-full">
              <div className="flex justify-between items-start mb-4">
                <div
                  className="rounded-full p-3"
                  style={{
                    backgroundColor: isDark
                      ? "rgba(114, 46, 209, 0.2)"
                      : "rgba(114, 46, 209, 0.1)",
                  }}
                >
                  <FaStar className="text-accent-color" size={22} />
                </div>
                <Badge color="teal" variant="light" size="sm">
                  +2 new
                </Badge>
              </div>

              <Text size="sm" c="dimmed" className="mb-1">
                Offers
              </Text>

              <Text size="xl" fw={700} className="mb-4">
                {stats.offered}
              </Text>

              <div className="mt-auto">
                <Group justify="space-between" className="mb-1">
                  <Text size="xs" c="dimmed">
                    Hired
                  </Text>
                  <Text
                    size="xs"
                    fw={500}
                    className={isDark ? "text-accent-300" : "text-accent-600"}
                  >
                    {stats.hired}
                  </Text>
                </Group>
                <Progress
                  value={
                    stats.offered > 0 ? (stats.hired / stats.offered) * 100 : 0
                  }
                  size="sm"
                  radius="xl"
                  color="accent"
                  className="transition-all duration-300"
                />
              </div>
            </div>
          </Card>

          {/* Profile Views Metric */}
          <Card
            withBorder
            p="lg"
            radius="md"
            className={`transition-all duration-200 hover:shadow-md ${
              isDark
                ? "border-dark-4 bg-dark-7 hover:border-primary-500/30"
                : "border-gray-200 bg-white hover:border-primary-500/30"
            }`}
            style={{
              borderColor: isDark ? "var(--mantine-color-dark-4)" : undefined,
              backgroundColor: isDark
                ? "var(--mantine-color-dark-7)"
                : undefined,
            }}
          >
            <div className="flex flex-col h-full">
              <div className="flex justify-between items-start mb-4">
                <div
                  className="rounded-full p-3"
                  style={{
                    backgroundColor: isDark
                      ? "rgba(24, 144, 255, 0.2)"
                      : "rgba(24, 144, 255, 0.1)",
                  }}
                >
                  <FaEye className="text-primary-color" size={22} />
                </div>
                <Badge
                  color="teal"
                  variant="light"
                  size="sm"
                  className="animate-pulse"
                >
                  +5 new
                </Badge>
              </div>

              <Text size="sm" c="dimmed" className="mb-1">
                Pending
              </Text>

              <Text size="xl" fw={700} className="mb-4">
                {stats.pending}
              </Text>

              <div className="mt-auto">
                <Group justify="space-between" className="mb-1">
                  <Text size="xs" c="dimmed">
                    Rejected
                  </Text>
                  <Text
                    size="xs"
                    fw={500}
                    className={isDark ? "text-primary-300" : "text-primary-600"}
                  >
                    {stats.rejected}
                  </Text>
                </Group>
                <Progress
                  value={
                    stats.total > 0 ? (stats.rejected / stats.total) * 100 : 0
                  }
                  size="sm"
                  radius="xl"
                  color="red"
                  className="transition-all duration-300"
                />
              </div>
            </div>
          </Card>
        </div>
      </div>

      {/* Recent Applications */}
      <div className="mb-8">
        <Text
          size="lg"
          fw={700}
          className={`mb-4 ${isDark ? "text-gray-200" : "text-gray-800"}`}
        >
          Recent Applications
        </Text>

        <Card
          withBorder
          p="lg"
          radius="md"
          className={`transition-all duration-200 ${
            isDark ? "border-dark-4 bg-dark-7" : "border-gray-200 bg-white"
          }`}
          style={{
            borderColor: isDark ? "var(--mantine-color-dark-4)" : undefined,
            backgroundColor: isDark ? "var(--mantine-color-dark-7)" : undefined,
          }}
        >
          <Group justify="space-between" mb="md">
            <Text
              size="md"
              fw={600}
              className={isDark ? "text-gray-300" : "text-gray-700"}
            >
              Your application history
            </Text>
            <Button
              variant="subtle"
              component={Link}
              to="/candidate/applications"
              color={isDark ? "primary.4" : "primary"}
              className="transition-all duration-200"
              size="sm"
            >
              View All
            </Button>
          </Group>

          <div className="overflow-x-auto">
            <table
              className={`min-w-full divide-y ${isDark ? "divide-dark-4" : "divide-gray-200"}`}
            >
              <thead className={isDark ? "bg-dark-6" : "bg-gray-50"}>
                <tr>
                  <th
                    className={`px-6 py-3 text-left text-xs font-medium tracking-wider uppercase ${isDark ? "text-gray-400" : "text-gray-500"}`}
                  >
                    Position
                  </th>
                  <th
                    className={`px-6 py-3 text-left text-xs font-medium tracking-wider uppercase ${isDark ? "text-gray-400" : "text-gray-500"}`}
                  >
                    Company
                  </th>
                  <th
                    className={`px-6 py-3 text-left text-xs font-medium tracking-wider uppercase ${isDark ? "text-gray-400" : "text-gray-500"}`}
                  >
                    Location
                  </th>
                  <th
                    className={`px-6 py-3 text-left text-xs font-medium tracking-wider uppercase ${isDark ? "text-gray-400" : "text-gray-500"}`}
                  >
                    Date
                  </th>
                  <th
                    className={`px-6 py-3 text-left text-xs font-medium tracking-wider uppercase ${isDark ? "text-gray-400" : "text-gray-500"}`}
                  >
                    Status
                  </th>
                </tr>
              </thead>
              <tbody
                className={`divide-y ${isDark ? "divide-dark-4 bg-dark-7" : "divide-gray-200 bg-white"}`}
              >
                {stats.recentApplications.length > 0 ? (
                  stats.recentApplications.map((application) => (
                    <tr
                      key={application._id}
                      className={
                        isDark ? "hover:bg-dark-6" : "hover:bg-gray-50"
                      }
                    >
                      <td
                        className={`px-6 py-4 text-sm font-medium whitespace-nowrap ${isDark ? "text-gray-200" : "text-gray-900"}`}
                      >
                        {application.job.title}
                      </td>
                      <td
                        className={`px-6 py-4 text-sm whitespace-nowrap ${isDark ? "text-gray-400" : "text-gray-500"}`}
                      >
                        {application.job.company.name}
                      </td>
                      <td
                        className={`px-6 py-4 text-sm whitespace-nowrap ${isDark ? "text-gray-400" : "text-gray-500"}`}
                      >
                        {application.job.location}
                      </td>
                      <td
                        className={`px-6 py-4 text-sm whitespace-nowrap ${isDark ? "text-gray-400" : "text-gray-500"}`}
                      >
                        {new Date(application.createdAt).toLocaleDateString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span
                          className={`inline-flex rounded-full px-2 text-xs leading-5 font-semibold ${
                            application.status === "pending"
                              ? isDark
                                ? "bg-primary-700/30 text-primary-200"
                                : "bg-primary-100 text-primary-800"
                              : application.status === "interview"
                                ? isDark
                                  ? "bg-secondary-700/30 text-secondary-200"
                                  : "bg-secondary-100 text-secondary-800"
                                : application.status === "rejected"
                                  ? isDark
                                    ? "bg-red-900/30 text-red-200"
                                    : "bg-red-100 text-red-800"
                                  : application.status === "offered" ||
                                      application.status === "hired"
                                    ? isDark
                                      ? "bg-green-900/30 text-green-200"
                                      : "bg-green-100 text-green-800"
                                    : isDark
                                      ? "bg-gray-700/30 text-gray-200"
                                      : "bg-gray-100 text-gray-800"
                          }`}
                        >
                          {application.status.charAt(0).toUpperCase() +
                            application.status.slice(1)}
                        </span>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td
                      colSpan={5}
                      className={`px-6 py-4 text-sm text-center ${isDark ? "text-gray-400" : "text-gray-500"}`}
                    >
                      No applications yet. Start applying to jobs to see your
                      history here.
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </Card>
      </div>

      {/* Recommended Jobs */}
      <div className="mb-8">
        <Text
          size="lg"
          fw={700}
          className={`mb-4 ${isDark ? "text-gray-200" : "text-gray-800"}`}
        >
          Recommended Jobs
        </Text>

        <Card
          withBorder
          p="lg"
          radius="md"
          className={`transition-all duration-200 ${
            isDark ? "border-dark-4 bg-dark-7" : "border-gray-200 bg-white"
          }`}
          style={{
            borderColor: isDark ? "var(--mantine-color-dark-4)" : undefined,
            backgroundColor: isDark ? "var(--mantine-color-dark-7)" : undefined,
          }}
        >
          <Group justify="space-between" mb="md">
            <Text
              size="md"
              fw={600}
              className={isDark ? "text-gray-300" : "text-gray-700"}
            >
              Jobs that match your profile
            </Text>
            <Button
              variant="subtle"
              component={Link}
              to="/candidate/jobs"
              color={isDark ? "primary.4" : "primary"}
              className="transition-all duration-200"
              size="sm"
            >
              Browse All Jobs
            </Button>
          </Group>

          <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
            {recommendedJobs.length > 0 ? (
              recommendedJobs.map((job) => (
                <Card
                  key={job._id}
                  withBorder
                  shadow="sm"
                  radius="md"
                  p="md"
                  className={`transition-all duration-200 ${
                    isDark
                      ? "border-dark-4 bg-dark-6 hover:border-primary-color/30"
                      : "border-gray-200 bg-white hover:border-primary-color/20"
                  }`}
                  style={{
                    borderColor: isDark
                      ? "var(--mantine-color-dark-4)"
                      : undefined,
                    backgroundColor: isDark
                      ? "var(--mantine-color-dark-6)"
                      : undefined,
                  }}
                >
                  <div className="flex flex-col space-y-2">
                    <Text
                      fw={700}
                      size="lg"
                      className={`${isDark ? "text-primary-color/90" : "text-primary-color"}`}
                    >
                      {job.title}
                    </Text>
                    <div className="flex items-center space-x-2">
                      <FaBuilding
                        size={14}
                        className={isDark ? "text-gray-400" : "text-gray-500"}
                      />
                      <Text size="sm" className={isDark ? "text-gray-300" : ""}>
                        {job.company.name}
                      </Text>
                    </div>
                    <div className="flex items-center space-x-2">
                      <FaMapMarkerAlt
                        size={14}
                        className={isDark ? "text-gray-400" : "text-gray-500"}
                      />
                      <Text size="sm" className={isDark ? "text-gray-300" : ""}>
                        {job.location}
                      </Text>
                    </div>
                    {(job.minSalary || job.maxSalary) && (
                      <div className="flex items-center space-x-2">
                        <FaBriefcase
                          size={14}
                          className={isDark ? "text-gray-400" : "text-gray-500"}
                        />
                        <Text
                          size="sm"
                          className={isDark ? "text-gray-300" : ""}
                        >
                          {job.minSalary && job.maxSalary
                            ? `$${job.minSalary.toLocaleString()} - $${job.maxSalary.toLocaleString()}`
                            : job.minSalary
                              ? `From $${job.minSalary.toLocaleString()}`
                              : `Up to $${job.maxSalary?.toLocaleString()}`}
                        </Text>
                      </div>
                    )}
                    <div className="flex items-center space-x-2">
                      <FaCalendarAlt
                        size={14}
                        className={isDark ? "text-gray-400" : "text-gray-500"}
                      />
                      <Text size="sm" className={isDark ? "text-gray-300" : ""}>
                        Posted {new Date(job.createdAt).toLocaleDateString()}
                      </Text>
                    </div>
                    <Divider my="xs" color={isDark ? "dark.4" : undefined} />
                    <div className="flex items-center justify-between">
                      {job.matchScore && (
                        <span
                          className={`rounded-full px-2 py-1 text-xs font-semibold ${
                            isDark
                              ? "bg-secondary-700/30 text-secondary-200"
                              : "bg-secondary-100 text-secondary-800"
                          }`}
                        >
                          {job.matchScore}% Match
                        </span>
                      )}
                      <Button
                        variant="light"
                        size="xs"
                        component={Link}
                        to={`/candidate/jobs/${job._id}`}
                        color={isDark ? "primary.4" : "primary"}
                        className="transition-all duration-200"
                      >
                        View Job
                      </Button>
                    </div>
                  </div>
                </Card>
              ))
            ) : (
              <div className="col-span-3 text-center py-8">
                <Text
                  size="sm"
                  className={isDark ? "text-gray-400" : "text-gray-500"}
                >
                  No recommended jobs available. Complete your profile to get
                  personalized recommendations.
                </Text>
              </div>
            )}
          </div>
        </Card>
      </div>
    </PageContainer>
  );
}
