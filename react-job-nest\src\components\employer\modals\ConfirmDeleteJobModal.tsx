"use client";

import { useConfirmDeleteJobModal } from "@/stores/modal-store";
import { Button, Group, LoadingOverlay, Modal, Text } from "@mantine/core";
import { useState } from "react";
import { FaExclamationTriangle } from "react-icons/fa";

export default function ConfirmDeleteJobModal() {
  const { isOpen, close, data } = useConfirmDeleteJobModal();
  const [isDeleting, setIsDeleting] = useState(false);

  const handleConfirm = async () => {
    try {
      setIsDeleting(true);

      // Call the confirm function from the modal store
      if (data.onConfirm) {
        data.onConfirm();
      }

      // Close the modal
      close();
    } catch (error) {
      console.error("Error deleting job:", error);
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <Modal
      opened={isOpen}
      onClose={close}
      title="Confirm Delete Job"
      centered
      size="md"
      closeOnClickOutside={!isDeleting}
      closeOnEscape={!isDeleting}
    >
      <div className="relative flex flex-col items-center gap-4 p-4">
        <LoadingOverlay visible={isDeleting} overlayProps={{ blur: 2 }} />

        <div className="flex h-16 w-16 items-center justify-center rounded-full bg-red-100 text-red-600">
          <FaExclamationTriangle size={32} />
        </div>
        <Text size="xl" fw={600} ta="center">
          Are you sure?
        </Text>
        <Text c="dimmed" ta="center">
          You are about to delete the job posting{" "}
          <strong>{data.jobTitle || "this job"}</strong>. This action cannot be
          undone.
        </Text>
        <Group mt="xl" justify="center" w="100%">
          <Button
            variant="outline"
            onClick={close}
            w="40%"
            disabled={isDeleting}
          >
            Cancel
          </Button>
          <Button
            color="red"
            onClick={handleConfirm}
            w="40%"
            loading={isDeleting}
            disabled={isDeleting}
          >
            Delete Job
          </Button>
        </Group>
      </div>
    </Modal>
  );
}
