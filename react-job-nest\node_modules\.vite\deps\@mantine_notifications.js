import {
  TransitionGroup_default,
  Transition_default
} from "./chunk-TEDSOOAS.js";
import {
  Box,
  Combination_default,
  Notification,
  OptionalPortal,
  createVarsResolver,
  factory,
  getDefaultZIndex,
  rem,
  useMantineTheme,
  useProps,
  useStyles
} from "./chunk-FYGUKJOL.js";
import "./chunk-FYDILROA.js";
import {
  require_jsx_runtime
} from "./chunk-MJNCUEZK.js";
import {
  randomId,
  useDidUpdate,
  useForceUpdate,
  useReducedMotion
} from "./chunk-4Q6ZKMFQ.js";
import {
  require_react
} from "./chunk-UGC3UZ7L.js";
import "./chunk-U7P2NEEE.js";
import {
  __toESM
} from "./chunk-G3PMV62Z.js";

// node_modules/@mantine/store/esm/store.mjs
var import_react = __toESM(require_react(), 1);
function createStore(initialState) {
  let state = initialState;
  let initialized = false;
  const listeners = /* @__PURE__ */ new Set();
  return {
    getState() {
      return state;
    },
    updateState(value) {
      state = typeof value === "function" ? value(state) : value;
    },
    setState(value) {
      this.updateState(value);
      listeners.forEach((listener) => listener(state));
    },
    initialize(value) {
      if (!initialized) {
        state = value;
        initialized = true;
      }
    },
    subscribe(callback) {
      listeners.add(callback);
      return () => listeners.delete(callback);
    }
  };
}
function useStore(store) {
  return (0, import_react.useSyncExternalStore)(
    store.subscribe,
    () => store.getState(),
    () => store.getState()
  );
}

// node_modules/@mantine/notifications/esm/notifications.store.mjs
function getDistributedNotifications(data, defaultPosition, limit) {
  const queue = [];
  const notifications2 = [];
  const count = {};
  for (const item of data) {
    const position = item.position || defaultPosition;
    count[position] = count[position] || 0;
    count[position] += 1;
    if (count[position] <= limit) {
      notifications2.push(item);
    } else {
      queue.push(item);
    }
  }
  return { notifications: notifications2, queue };
}
var createNotificationsStore = () => createStore({
  notifications: [],
  queue: [],
  defaultPosition: "bottom-right",
  limit: 5
});
var notificationsStore = createNotificationsStore();
var useNotifications = (store = notificationsStore) => useStore(store);
function updateNotificationsState(store, update) {
  const state = store.getState();
  const notifications2 = update([...state.notifications, ...state.queue]);
  const updated = getDistributedNotifications(notifications2, state.defaultPosition, state.limit);
  store.setState({
    notifications: updated.notifications,
    queue: updated.queue,
    limit: state.limit,
    defaultPosition: state.defaultPosition
  });
}
function showNotification(notification, store = notificationsStore) {
  const id = notification.id || randomId();
  updateNotificationsState(store, (notifications2) => {
    if (notification.id && notifications2.some((n) => n.id === notification.id)) {
      return notifications2;
    }
    return [...notifications2, { ...notification, id }];
  });
  return id;
}
function hideNotification(id, store = notificationsStore) {
  updateNotificationsState(
    store,
    (notifications2) => notifications2.filter((notification) => {
      var _a;
      if (notification.id === id) {
        (_a = notification.onClose) == null ? void 0 : _a.call(notification, notification);
        return false;
      }
      return true;
    })
  );
  return id;
}
function updateNotification(notification, store = notificationsStore) {
  updateNotificationsState(
    store,
    (notifications2) => notifications2.map((item) => {
      if (item.id === notification.id) {
        return { ...item, ...notification };
      }
      return item;
    })
  );
  return notification.id;
}
function cleanNotifications(store = notificationsStore) {
  updateNotificationsState(store, () => []);
}
function cleanNotificationsQueue(store = notificationsStore) {
  updateNotificationsState(
    store,
    (notifications2) => notifications2.slice(0, store.getState().limit)
  );
}
var notifications = {
  show: showNotification,
  hide: hideNotification,
  update: updateNotification,
  clean: cleanNotifications,
  cleanQueue: cleanNotificationsQueue,
  updateState: updateNotificationsState
};

// node_modules/@mantine/notifications/esm/Notifications.mjs
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_react3 = __toESM(require_react(), 1);

// node_modules/@mantine/notifications/esm/get-grouped-notifications/get-grouped-notifications.mjs
var positions = [
  "bottom-center",
  "bottom-left",
  "bottom-right",
  "top-center",
  "top-left",
  "top-right"
];
function getGroupedNotifications(notifications2, defaultPosition) {
  return notifications2.reduce(
    (acc, notification) => {
      acc[notification.position || defaultPosition].push(notification);
      return acc;
    },
    positions.reduce((acc, item) => {
      acc[item] = [];
      return acc;
    }, {})
  );
}

// node_modules/@mantine/notifications/esm/get-notification-state-styles.mjs
var transforms = {
  left: "translateX(-100%)",
  right: "translateX(100%)",
  "top-center": "translateY(-100%)",
  "bottom-center": "translateY(100%)"
};
var noTransform = {
  left: "translateX(0)",
  right: "translateX(0)",
  "top-center": "translateY(0)",
  "bottom-center": "translateY(0)"
};
function getNotificationStateStyles({
  state,
  maxHeight,
  position,
  transitionDuration
}) {
  const [vertical, horizontal] = position.split("-");
  const property = horizontal === "center" ? `${vertical}-center` : horizontal;
  const commonStyles = {
    opacity: 0,
    maxHeight,
    transform: transforms[property],
    transitionDuration: `${transitionDuration}ms, ${transitionDuration}ms, ${transitionDuration}ms`,
    transitionTimingFunction: "cubic-bezier(.51,.3,0,1.21), cubic-bezier(.51,.3,0,1.21), linear",
    transitionProperty: "opacity, transform, max-height"
  };
  const inState = {
    opacity: 1,
    transform: noTransform[property]
  };
  const outState = {
    opacity: 0,
    maxHeight: 0,
    transform: transforms[property]
  };
  const transitionStyles = {
    entering: inState,
    entered: inState,
    exiting: outState,
    exited: outState
  };
  return { ...commonStyles, ...transitionStyles[state] };
}

// node_modules/@mantine/notifications/esm/NotificationContainer.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_react2 = __toESM(require_react(), 1);

// node_modules/@mantine/notifications/esm/get-auto-close/get-auto-close.mjs
function getAutoClose(autoClose, notificationAutoClose) {
  if (typeof notificationAutoClose === "number") {
    return notificationAutoClose;
  }
  if (notificationAutoClose === false || autoClose === false) {
    return false;
  }
  return autoClose;
}

// node_modules/@mantine/notifications/esm/NotificationContainer.mjs
var NotificationContainer = (0, import_react2.forwardRef)(
  ({ data, onHide, autoClose, ...others }, ref) => {
    const { autoClose: _autoClose, message, ...notificationProps } = data;
    const autoCloseDuration = getAutoClose(autoClose, data.autoClose);
    const autoCloseTimeout = (0, import_react2.useRef)(-1);
    const cancelAutoClose = () => window.clearTimeout(autoCloseTimeout.current);
    const handleHide = () => {
      onHide(data.id);
      cancelAutoClose();
    };
    const handleAutoClose = () => {
      if (typeof autoCloseDuration === "number") {
        autoCloseTimeout.current = window.setTimeout(handleHide, autoCloseDuration);
      }
    };
    (0, import_react2.useEffect)(() => {
      var _a;
      (_a = data.onOpen) == null ? void 0 : _a.call(data, data);
    }, []);
    (0, import_react2.useEffect)(() => {
      handleAutoClose();
      return cancelAutoClose;
    }, [autoCloseDuration]);
    return (0, import_jsx_runtime.jsx)(
      Notification,
      {
        ...others,
        ...notificationProps,
        onClose: handleHide,
        ref,
        onMouseEnter: cancelAutoClose,
        onMouseLeave: handleAutoClose,
        children: message
      }
    );
  }
);
NotificationContainer.displayName = "@mantine/notifications/NotificationContainer";

// node_modules/@mantine/notifications/esm/Notifications.module.css.mjs
var classes = { "root": "m_b37d9ac7", "notification": "m_5ed0edd0" };

// node_modules/@mantine/notifications/esm/Notifications.mjs
var Transition = Transition_default;
var defaultProps = {
  position: "bottom-right",
  autoClose: 4e3,
  transitionDuration: 250,
  containerWidth: 440,
  notificationMaxHeight: 200,
  limit: 5,
  zIndex: getDefaultZIndex("overlay"),
  store: notificationsStore,
  withinPortal: true
};
var varsResolver = createVarsResolver((_, { zIndex, containerWidth }) => ({
  root: {
    "--notifications-z-index": zIndex == null ? void 0 : zIndex.toString(),
    "--notifications-container-width": rem(containerWidth)
  }
}));
var Notifications = factory((_props, ref) => {
  const props = useProps("Notifications", defaultProps, _props);
  const {
    classNames,
    className,
    style,
    styles,
    unstyled,
    vars,
    position,
    autoClose,
    transitionDuration,
    containerWidth,
    notificationMaxHeight,
    limit,
    zIndex,
    store,
    portalProps,
    withinPortal,
    ...others
  } = props;
  const theme = useMantineTheme();
  const data = useNotifications(store);
  const forceUpdate = useForceUpdate();
  const shouldReduceMotion = useReducedMotion();
  const refs = (0, import_react3.useRef)({});
  const previousLength = (0, import_react3.useRef)(0);
  const reduceMotion = theme.respectReducedMotion ? shouldReduceMotion : false;
  const duration = reduceMotion ? 1 : transitionDuration;
  const getStyles = useStyles({
    name: "Notifications",
    classes,
    props,
    className,
    style,
    classNames,
    styles,
    unstyled,
    vars,
    varsResolver
  });
  (0, import_react3.useEffect)(() => {
    store == null ? void 0 : store.updateState((current) => ({
      ...current,
      limit: limit || 5,
      defaultPosition: position
    }));
  }, [limit, position]);
  useDidUpdate(() => {
    if (data.notifications.length > previousLength.current) {
      setTimeout(() => forceUpdate(), 0);
    }
    previousLength.current = data.notifications.length;
  }, [data.notifications]);
  const grouped = getGroupedNotifications(data.notifications, position);
  const groupedComponents = positions.reduce(
    (acc, pos) => {
      acc[pos] = grouped[pos].map(({ style: notificationStyle, ...notification }) => (0, import_jsx_runtime2.jsx)(
        Transition,
        {
          timeout: duration,
          onEnter: () => refs.current[notification.id].offsetHeight,
          nodeRef: { current: refs.current[notification.id] },
          children: (state) => (0, import_jsx_runtime2.jsx)(
            NotificationContainer,
            {
              ref: (node) => {
                refs.current[notification.id] = node;
              },
              data: notification,
              onHide: (id) => hideNotification(id, store),
              autoClose,
              ...getStyles("notification", {
                style: {
                  ...getNotificationStateStyles({
                    state,
                    position: pos,
                    transitionDuration: duration,
                    maxHeight: notificationMaxHeight
                  }),
                  ...notificationStyle
                }
              })
            }
          )
        },
        notification.id
      ));
      return acc;
    },
    {}
  );
  return (0, import_jsx_runtime2.jsxs)(OptionalPortal, { withinPortal, ...portalProps, children: [
    (0, import_jsx_runtime2.jsx)(Box, { ...getStyles("root"), "data-position": "top-center", ref, ...others, children: (0, import_jsx_runtime2.jsx)(TransitionGroup_default, { children: groupedComponents["top-center"] }) }),
    (0, import_jsx_runtime2.jsx)(Box, { ...getStyles("root"), "data-position": "top-left", ...others, children: (0, import_jsx_runtime2.jsx)(TransitionGroup_default, { children: groupedComponents["top-left"] }) }),
    (0, import_jsx_runtime2.jsx)(
      Box,
      {
        ...getStyles("root", { className: Combination_default.classNames.fullWidth }),
        "data-position": "top-right",
        ...others,
        children: (0, import_jsx_runtime2.jsx)(TransitionGroup_default, { children: groupedComponents["top-right"] })
      }
    ),
    (0, import_jsx_runtime2.jsx)(
      Box,
      {
        ...getStyles("root", { className: Combination_default.classNames.fullWidth }),
        "data-position": "bottom-right",
        ...others,
        children: (0, import_jsx_runtime2.jsx)(TransitionGroup_default, { children: groupedComponents["bottom-right"] })
      }
    ),
    (0, import_jsx_runtime2.jsx)(Box, { ...getStyles("root"), "data-position": "bottom-left", ...others, children: (0, import_jsx_runtime2.jsx)(TransitionGroup_default, { children: groupedComponents["bottom-left"] }) }),
    (0, import_jsx_runtime2.jsx)(Box, { ...getStyles("root"), "data-position": "bottom-center", ...others, children: (0, import_jsx_runtime2.jsx)(TransitionGroup_default, { children: groupedComponents["bottom-center"] }) })
  ] });
});
Notifications.classes = classes;
Notifications.displayName = "@mantine/notifications/Notifications";
Notifications.show = notifications.show;
Notifications.hide = notifications.hide;
Notifications.update = notifications.update;
Notifications.clean = notifications.clean;
Notifications.cleanQueue = notifications.cleanQueue;
Notifications.updateState = notifications.updateState;
export {
  Notifications,
  cleanNotifications,
  cleanNotificationsQueue,
  createNotificationsStore,
  hideNotification,
  notifications,
  notificationsStore,
  showNotification,
  updateNotification,
  updateNotificationsState,
  useNotifications
};
//# sourceMappingURL=@mantine_notifications.js.map
