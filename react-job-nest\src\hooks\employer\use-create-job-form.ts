import employerApi from "@/services/employer-api";
import { URLS } from "@/utils/urls";
import { useForm } from "@mantine/form";
import { notifications } from "@mantine/notifications";
import { zodResolver } from "mantine-form-zod-resolver";
import { useState } from "react";
import { useNavigate } from "react-router";
import { z } from "zod";
import { createJobSchema } from "../../schemas/employer/create-job-schema";
import { useCompanyData } from "./use-company-data";

// Type for form values
type FormValues = z.infer<typeof createJobSchema>;

export default function useCreateJobForm() {
  const { company } = useCompanyData();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const navigate = useNavigate();

  const form = useForm<FormValues>({
    initialValues: {
      jobTitle: "",
      minSalary: undefined,
      maxSalary: undefined,
      currency: "",
      location: "",
      jobType: "",
      jobDescription: "",
      jobRequirements: "",
      applicationDeadline: undefined,
      requiredExperience: "",
      jobAttachment: undefined,
      jobCategory: "",
      jobTags: [],
      benefits: [],
      showSalary: false,
    },
    validate: zodResolver(createJobSchema),
  });

  const handleSubmit = async (
    values: FormValues & { questions?: string[] },
  ) => {
    try {
      // Check if company exists
      if (!company) {
        notifications.show({
          title: "Error",
          message: "You need to create a company profile first",
          color: "red",
        });
        return;
      }

      setIsSubmitting(true);
      console.log("Company data:", company);

      // Transform form values to match backend API
      const jobData = {
        title: values.jobTitle,
        description: values.jobDescription,
        requirements: values.jobRequirements,
        company: company._id, // Use the company ID from the company data
        jobType: values.jobType.toLowerCase(), // Backend expects lowercase values
        location: values.location.toLowerCase(), // Backend expects lowercase values
        category: values.jobCategory,
        tags: Array.isArray(values.jobTags) ? values.jobTags : [],
        minSalary: values.minSalary || undefined,
        maxSalary: values.maxSalary || undefined,
        currency: values.currency || undefined,
        showSalary: Boolean(values.showSalary),
        benefits: Array.isArray(values.benefits) ? values.benefits : [],
        applicationDeadline: values.applicationDeadline
          ? values.applicationDeadline instanceof Date
            ? values.applicationDeadline.toISOString()
            : new Date(values.applicationDeadline).toISOString()
          : undefined,
        requiredExperience: values.requiredExperience,
        questions: Array.isArray(values.questions) ? values.questions : [],
        isActive: true,
      };

      console.log("Submitting job data:", jobData);

      // Call the API to create the job
      console.log("Calling API with job data:", jobData);

      try {
        console.log("Attempting to create job with data:", jobData);
        const response = await employerApi.jobs.createJob(jobData);
        console.log("API response:", response);

        const responseData = response.data as any;

        if (responseData && responseData.success) {
          notifications.show({
            title: "Success",
            message: "Job created successfully",
            color: "green",
          });

          // Redirect to manage jobs page
          navigate(URLS.employer.manageJobs);
        } else {
          throw new Error(
            (responseData && responseData.message) || "Failed to create job",
          );
        }
      } catch (apiError: any) {
        console.error("API call error:", apiError);
        console.error("API error details:", {
          message: apiError.message,
          status: apiError.response?.status,
          data: apiError.response?.data,
        });

        // Check if we're in development mode and the backend might not be available
        if (
          process.env.NODE_ENV === "development" &&
          (apiError.message.includes("Network Error") ||
            apiError.response?.status === 404 ||
            !apiError.response)
        ) {
          console.log("Development mode: Simulating successful job creation");

          notifications.show({
            title: "Development Mode",
            message:
              "Simulated job creation successful. Backend connection not available.",
            color: "blue",
          });

          // Redirect to manage jobs page
          navigate(URLS.employer.manageJobs);
          return;
        }

        throw apiError;
      }
    } catch (error: any) {
      console.error("Error creating job:", error);

      // More detailed error logging
      if (error.response) {
        console.error("Response data:", error.response.data);
        console.error("Response status:", error.response.status);
      }

      // Check if we're in development mode and the backend might not be available
      if (
        process.env.NODE_ENV === "development" &&
        (error.message?.includes("Network Error") ||
          error.response?.status === 404 ||
          !error.response)
      ) {
        console.log("Development mode: Simulating successful job creation");

        notifications.show({
          title: "Development Mode",
          message:
            "Simulated job creation successful. Backend connection not available.",
          color: "blue",
        });

        // Redirect to manage jobs page
        navigate(URLS.employer.manageJobs);
      } else {
        // Show error notification
        notifications.show({
          title: "Error",
          message:
            error.response?.data?.message ||
            error.message ||
            "Failed to create job",
          color: "red",
        });
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    form,
    handleSubmit,
    isSubmitting,
    company,
  };
}
