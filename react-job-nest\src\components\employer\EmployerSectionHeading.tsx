import SectionHeading from "@/design-system/components/typography/SectionHeading";
import { type ReactNode } from "react";
import { type IconType } from "react-icons";

/**
 * @deprecated Use SectionHeading from design-system/components with variant="employer" instead
 */
export default function EmployerSectionHeading({
  children,
  Icon,
  className,
}: {
  children?: ReactNode;
  Icon?: IconType;
  className?: string;
}) {
  return (
    <SectionHeading icon={Icon} className={className} variant="employer">
      {children}
    </SectionHeading>
  );
}
