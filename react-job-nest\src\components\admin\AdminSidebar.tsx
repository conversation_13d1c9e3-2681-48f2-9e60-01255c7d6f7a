"use client";

import { Sidebar, type SidebarLink } from "@/design-system/components";
import { URLS } from "@/utils/urls";
import {
  FaBriefcase,
  FaBuilding,
  FaTachometerAlt,
  FaUsers,
} from "react-icons/fa";

const adminLinks: SidebarLink[] = [
  {
    id: 1,
    name: "Dashboard",
    href: URLS.admin.dashboard,
    icon: FaTachometerAlt,
  },
  {
    id: 2,
    name: "Users",
    href: URLS.admin.users,
    icon: FaUsers,
  },
  {
    id: 3,
    name: "Jobs",
    href: URLS.admin.jobs,
    icon: FaBriefcase,
  },
  {
    id: 4,
    name: "Companies",
    href: URLS.admin.companies,
    icon: FaBuilding,
  },
];

interface AdminSidebarProps {
  onLinkClick?: () => void;
}

export default function AdminSidebar({ onLinkClick }: AdminSidebarProps) {
  return (
    <Sidebar title="Admin Panel" links={adminLinks} onLinkClick={onLinkClick} />
  );
}
