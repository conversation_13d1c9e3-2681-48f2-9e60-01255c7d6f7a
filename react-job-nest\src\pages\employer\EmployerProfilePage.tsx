"use client";

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>Heading } from "@/design-system/components";
import { useThemeClasses } from "@/design-system/utils/theme-utils";
import { useEmployerProfile } from "@/hooks/employer/use-employer-profile";
import useProfileForm from "@/hooks/employer/use-profile-form-fixed";
import { useAuthStore } from "@/stores/auth-store";
import {
  ActionIcon,
  Avatar,
  Button,
  Card,
  Checkbox,
  FileInput,
  Grid,
  Group,
  Modal,
  NumberInput,
  RingProgress,
  Select,
  Tabs,
  Text,
  TextInput,
  Textarea,
  Title,
  Tooltip,
  useMantineColorScheme,
} from "@mantine/core";
import { useDisclosure } from "@mantine/hooks";
import { notifications } from "@mantine/notifications";
import { useState } from "react";
import {
  FaBriefcase,
  FaBuilding,
  FaCalendarAlt,
  FaCamera,
  FaCheckCircle,
  FaClock,
  FaEnvelope,
  FaFacebook,
  FaGlobe,
  FaInstagram,
  FaLinked<PERSON>,
  FaMapMarkerAlt,
  FaPhone,
  FaSave,
  FaTrash,
  FaTwitter,
  FaUpload,
  FaUser,
  FaUsers,
} from "react-icons/fa";
import { Link } from "react-router";

export default function EmployerProfilePage() {
  // Get form and other data from hook
  const {
    form,
    handleSubmit,
    isLoading,
    // company,
    logoPreview,
    setLogoPreview,
    companyStats,
    verificationStatus,
    industries,
    calculateProfileCompleteness,
  } = useProfileForm();

  // Get employer profile data and functions
  const { profile, uploadProfileImage } = useEmployerProfile();
  const { user } = useAuthStore();

  // Calculate profile completion using the weighted algorithm
  const profileCompletion = calculateProfileCompleteness(form.values);

  // State for active tab
  const [activeTab, setActiveTab] = useState<string | null>("company");

  // Modal states
  const [logoModalOpened, { open: openLogoModal, close: closeLogoModal }] =
    useDisclosure(false);
  const [
    profileImageModalOpened,
    { open: openProfileImageModal, close: closeProfileImageModal },
  ] = useDisclosure(false);

  // Profile image state
  const [selectedProfileImage, setSelectedProfileImage] = useState<File | null>(
    null,
  );
  const [profileImagePreview, setProfileImagePreview] = useState<string | null>(
    null,
  );
  const [isUploadingImage, setIsUploadingImage] = useState(false);

  // Pre-compute all theme classes to avoid conditional hook calls
  const { colorScheme } = useMantineColorScheme();
  const isDark = colorScheme === "dark";

  // Background gradient classes
  const backgroundGradientClasses = isDark
    ? "absolute inset-0 bg-gradient-to-r from-dark-6 via-dark-5 to-dark-6"
    : "absolute inset-0 bg-gradient-to-r from-blue-50 via-indigo-50 to-blue-100";

  // Company name classes
  const companyNameClasses = isDark
    ? "mb-2 text-gray-300"
    : "mb-2 text-gray-700";

  // Card classes
  const cardClasses = isDark ? "p-6 shadow-dark-sm bg-dark-7" : "p-6 shadow-sm";

  // Title classes
  const titleClasses = isDark
    ? "mb-4 text-xl font-semibold text-blue-300"
    : "mb-4 text-xl font-semibold text-blue-800";

  // Text classes
  const textClasses = isDark ? "mb-4 text-gray-400" : "mb-4 text-gray-600";

  // Action icon classes
  const actionIconClasses = isDark
    ? "border border-dark-4 bg-dark-5 shadow-dark-sm"
    : "border border-gray-200 bg-white shadow-sm";

  // Handle company logo upload
  const handleLogoChange = (file: File | null) => {
    if (file) {
      // Set the file in the form
      form.setFieldValue("companyLogo", file);

      // Create a preview URL
      const imageUrl = URL.createObjectURL(file);
      setLogoPreview(imageUrl);
      closeLogoModal();

      notifications.show({
        title: "Company Logo Selected",
        message: "Save your profile to upload the new logo",
        color: "blue",
      });
    }
  };

  // Handle company logo removal
  const handleRemoveLogo = () => {
    // Clear the logo in the form
    form.setFieldValue("companyLogo", undefined);
    setLogoPreview(null);

    notifications.show({
      title: "Company Logo Removed",
      message: "Save your profile to confirm removal",
      color: "yellow",
    });
  };

  // Get profile image URL with fallback
  const getProfileImageUrl = (imageUrl?: string) => {
    if (!imageUrl) return null;
    if (imageUrl.startsWith("http")) return imageUrl;
    return `${import.meta.env.VITE_API_URL || "http://localhost:5000"}/${imageUrl}`;
  };

  // Handle profile image selection (preview only)
  const handleProfileImageChange = (file: File | null) => {
    if (file) {
      setSelectedProfileImage(file);

      // Create a preview for immediate UI feedback
      const imageUrl = URL.createObjectURL(file);
      setProfileImagePreview(imageUrl);
    } else {
      setSelectedProfileImage(null);
      // Reset to original profile image with full URL or null
      const fullImageUrl = getProfileImageUrl(
        profile?.user?.profileImageUrl || user?.profileImageUrl,
      );
      setProfileImagePreview(fullImageUrl);
    }
  };

  // Handle profile image upload (save to server)
  const handleSaveProfileImage = async () => {
    if (!selectedProfileImage) return;

    setIsUploadingImage(true);

    try {
      const success = await uploadProfileImage(selectedProfileImage);

      if (success) {
        setSelectedProfileImage(null);
        closeProfileImageModal();
        notifications.show({
          title: "Profile Image Updated",
          message: "Your profile image has been successfully updated",
          color: "green",
        });
      } else {
        notifications.show({
          title: "Error",
          message: "Failed to upload profile image. Please try again.",
          color: "red",
        });
      }
    } catch (error) {
      console.error("Error uploading profile image:", error);
      notifications.show({
        title: "Error",
        message: "Failed to upload profile image. Please try again.",
        color: "red",
      });
    } finally {
      setIsUploadingImage(false);
    }
  };

  // Handle remove profile image
  const handleRemoveProfileImage = async () => {
    try {
      setIsUploadingImage(true);

      // Call API to remove profile image (upload empty file or call delete endpoint)
      const success = await uploadProfileImage(new File([], ""));

      if (success) {
        setProfileImagePreview(null);
        notifications.show({
          title: "Profile Image Removed",
          message: "Your profile image has been removed successfully",
          color: "yellow",
        });
      } else {
        notifications.show({
          title: "Error",
          message: "Failed to remove profile image. Please try again.",
          color: "red",
        });
      }
    } catch (error) {
      console.error("Error removing profile image:", error);
      notifications.show({
        title: "Error",
        message: "Failed to remove profile image. Please try again.",
        color: "red",
      });
    } finally {
      setIsUploadingImage(false);
    }
  };

  // Handle close profile image modal
  const handleCloseProfileImageModal = () => {
    setSelectedProfileImage(null);
    const fullImageUrl = getProfileImageUrl(
      profile?.user?.profileImageUrl || user?.profileImageUrl,
    );
    setProfileImagePreview(fullImageUrl);
    closeProfileImageModal();
  };

  return (
    <PageContainer
      breadcrumbItems={[{ title: "Home", href: "/" }, { title: "Profile" }]}
      variant="employer"
      className="pb-12"
    >
      <PageHeading
        title="Company Profile"
        subtitle="Manage your company information and preferences"
        variant="employer"
      />

      {/* Hero Section */}
      <Card withBorder radius="md" className="mb-8 overflow-hidden" p={0}>
        <div className="relative">
          {/* Background gradient */}
          <div className={backgroundGradientClasses}></div>

          <div className="relative p-6 md:p-8">
            <Grid>
              <Grid.Col span={{ base: 12, sm: 8 }}>
                <Group align="flex-start" className="mb-4">
                  {/* User Profile Image */}
                  <div className="relative">
                    <Avatar
                      size={120}
                      radius="md"
                      color="blue"
                      className={useThemeClasses(
                        "border-4 border-white shadow-md",
                        "border-4 border-dark-6 shadow-md",
                      )}
                      src={
                        profileImagePreview ||
                        getProfileImageUrl(
                          profile?.user?.profileImageUrl ||
                            user?.profileImageUrl,
                        )
                      }
                    >
                      {!profileImagePreview &&
                        !profile?.user?.profileImageUrl &&
                        !user?.profileImageUrl &&
                        (user?.name?.charAt(0) || "U")}
                    </Avatar>
                    <div className="absolute -right-2 -bottom-2 flex gap-1">
                      <Tooltip label="Change profile picture">
                        <ActionIcon
                          className={useThemeClasses(
                            "border border-gray-200 bg-white shadow-sm",
                            "border border-gray-700 bg-dark-6 shadow-sm",
                          )}
                          radius="xl"
                          variant="filled"
                          size="md"
                          color="blue"
                          onClick={openProfileImageModal}
                        >
                          <FaCamera size={14} />
                        </ActionIcon>
                      </Tooltip>
                      {(profileImagePreview ||
                        profile?.user?.profileImageUrl ||
                        user?.profileImageUrl) && (
                        <Tooltip label="Remove profile picture">
                          <ActionIcon
                            className={useThemeClasses(
                              "border border-gray-200 bg-white shadow-sm",
                              "border border-gray-700 bg-dark-6 shadow-sm",
                            )}
                            radius="xl"
                            variant="filled"
                            size="md"
                            color="red"
                            onClick={handleRemoveProfileImage}
                          >
                            <FaTrash size={14} />
                          </ActionIcon>
                        </Tooltip>
                      )}
                    </div>
                  </div>

                  {/* Company Logo */}
                  <div className="relative">
                    <Avatar
                      size={100}
                      radius="md"
                      color="gray"
                      className="border-4 border-white shadow-md"
                      src={logoPreview}
                    >
                      {!logoPreview && form.values.companyName.charAt(0)}
                    </Avatar>
                    <div className="absolute -right-2 -bottom-2 flex gap-1">
                      <Tooltip label="Change company logo">
                        <ActionIcon
                          className={actionIconClasses}
                          radius="xl"
                          variant="filled"
                          size="sm"
                          color="blue"
                          onClick={openLogoModal}
                        >
                          <FaCamera size={12} />
                        </ActionIcon>
                      </Tooltip>
                      {logoPreview && (
                        <Tooltip label="Remove company logo">
                          <ActionIcon
                            className={actionIconClasses}
                            radius="xl"
                            variant="filled"
                            size="sm"
                            color="red"
                            onClick={handleRemoveLogo}
                          >
                            <FaTrash size={12} />
                          </ActionIcon>
                        </Tooltip>
                      )}
                    </div>
                  </div>

                  <div>
                    <Title order={1} className="text-2xl font-bold">
                      {form.values.companyName}
                    </Title>

                    <Text size="lg" className={companyNameClasses}>
                      {form.values.industry}
                    </Text>

                    <Group gap="lg" className="mt-3">
                      <Group gap="xs">
                        <FaEnvelope className="text-blue-500" />
                        <Text size="sm">{form.values.email}</Text>
                      </Group>

                      <Group gap="xs">
                        <FaPhone className="text-blue-500" />
                        <Text size="sm">{form.values.phone}</Text>
                      </Group>

                      <Group gap="xs">
                        <FaMapMarkerAlt className="text-blue-500" />
                        <Text size="sm">{form.values.location}</Text>
                      </Group>
                    </Group>
                  </div>
                </Group>
              </Grid.Col>

              <Grid.Col span={{ base: 12, sm: 4 }}>
                <div className="space-y-4">
                  {/* Profile Completion Card */}
                  <Card
                    withBorder
                    radius="md"
                    className={useThemeClasses(
                      "bg-white shadow-sm",
                      "bg-dark-6 shadow-dark-lg",
                    )}
                  >
                    <Text fw={700} ta="center" className="mb-2">
                      Profile Completion
                    </Text>
                    <Group justify="center" className="mb-2">
                      <RingProgress
                        size={80}
                        thickness={8}
                        roundCaps
                        sections={[{ value: profileCompletion, color: "blue" }]}
                        label={
                          <Text ta="center" size="sm" fw={700}>
                            {profileCompletion}%
                          </Text>
                        }
                      />
                    </Group>
                    <Text size="sm" c="dimmed" ta="center">
                      Complete your profile to attract more candidates
                    </Text>
                    {profileCompletion >= 100 && (
                      <Group gap="xs" mt={4} justify="center">
                        <FaCheckCircle size={14} className="text-green-500" />
                        <Text size="xs" c="green">
                          Your profile is complete!
                        </Text>
                      </Group>
                    )}
                  </Card>

                  {/* Verification Status Card */}
                  <Card
                    withBorder
                    radius="md"
                    className={
                      isDark ? "bg-dark-6 shadow-dark-lg" : "bg-white shadow-sm"
                    }
                  >
                    <Text fw={700} ta="center" className="mb-2">
                      Verification Status
                    </Text>
                    <div className="flex justify-center mb-2">
                      {verificationStatus.status === "verified" ? (
                        <div className="flex flex-col items-center">
                          <div className="w-16 h-16 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center mb-2">
                            <FaCheckCircle
                              size={32}
                              className="text-green-500"
                            />
                          </div>
                          <Text fw={600} c="green" size="sm">
                            Verified
                          </Text>
                        </div>
                      ) : verificationStatus.status === "pending" ? (
                        <div className="flex flex-col items-center">
                          <div className="w-16 h-16 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center mb-2">
                            <FaClock size={32} className="text-blue-500" />
                          </div>
                          <Text fw={600} c="blue" size="sm">
                            Pending Verification
                          </Text>
                        </div>
                      ) : (
                        <div className="flex flex-col items-center">
                          <div className="w-16 h-16 rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center mb-2">
                            <FaBuilding size={32} className="text-gray-500" />
                          </div>
                          <Text fw={600} c="dimmed" size="sm">
                            Not Verified
                          </Text>
                        </div>
                      )}
                    </div>

                    <Text size="sm" c="dimmed" ta="center" className="mb-3">
                      {verificationStatus.status === "verified"
                        ? "Your company is verified. This increases trust with candidates."
                        : verificationStatus.status === "pending"
                          ? "Your verification request is being reviewed. This usually takes 1-3 business days."
                          : "Verified companies receive more applications and higher quality candidates."}
                    </Text>

                    {verificationStatus.status === "not_requested" &&
                      profileCompletion >= 80 && (
                        <div className="mt-2">
                          <Tooltip label="Complete your profile to at least 80% to request verification">
                            <div>
                              <Button
                                fullWidth
                                size="sm"
                                onClick={() =>
                                  form.setFieldValue(
                                    "verificationRequested",
                                    true,
                                  )
                                }
                                disabled={profileCompletion < 80}
                              >
                                Request Verification
                              </Button>
                            </div>
                          </Tooltip>
                        </div>
                      )}
                  </Card>

                  {/* Company Stats Card (if available) */}
                  {companyStats && (
                    <Card
                      withBorder
                      radius="md"
                      className={
                        isDark
                          ? "bg-dark-6 shadow-dark-lg"
                          : "bg-white shadow-sm"
                      }
                    >
                      <Text fw={700} ta="center" className="mb-3">
                        Company Statistics
                      </Text>
                      <div className="space-y-2">
                        <Group justify="space-between">
                          <Text size="sm">Active Jobs:</Text>
                          <Text size="sm" fw={600}>
                            {companyStats.activeJobs}
                          </Text>
                        </Group>
                        <Group justify="space-between">
                          <Text size="sm">Total Applications:</Text>
                          <Text size="sm" fw={600}>
                            {companyStats.totalApplications}
                          </Text>
                        </Group>
                        <Group justify="space-between">
                          <Text size="sm">New Applications:</Text>
                          <Text size="sm" fw={600} c="blue">
                            {companyStats.newApplications}
                          </Text>
                        </Group>
                        <Group justify="space-between">
                          <Text size="sm">Profile Views:</Text>
                          <Text size="sm" fw={600}>
                            {companyStats.viewCount}
                          </Text>
                        </Group>
                      </div>
                    </Card>
                  )}
                </div>
              </Grid.Col>
            </Grid>

            {/* Social Media Quick Links */}
            <Group className="mt-4">
              <Group ml="auto" gap="xs">
                {form.values.linkedin && (
                  <Tooltip label="LinkedIn Profile">
                    <ActionIcon
                      component={Link}
                      to={form.values.linkedin}
                      target="_blank"
                      variant="light"
                      color="blue"
                      size="lg"
                    >
                      <FaLinkedin size={20} />
                    </ActionIcon>
                  </Tooltip>
                )}

                {form.values.twitter && (
                  <Tooltip label="Twitter Profile">
                    <ActionIcon
                      component={Link}
                      to={form.values.twitter}
                      target="_blank"
                      variant="light"
                      color="cyan"
                      size="lg"
                    >
                      <FaTwitter size={20} />
                    </ActionIcon>
                  </Tooltip>
                )}

                {form.values.facebook && (
                  <Tooltip label="Facebook Page">
                    <ActionIcon
                      component={Link}
                      to={form.values.facebook}
                      target="_blank"
                      variant="light"
                      color="indigo"
                      size="lg"
                    >
                      <FaFacebook size={20} />
                    </ActionIcon>
                  </Tooltip>
                )}

                {form.values.instagram && (
                  <Tooltip label="Instagram Profile">
                    <ActionIcon
                      component={Link}
                      to={form.values.instagram}
                      target="_blank"
                      variant="light"
                      color="pink"
                      size="lg"
                    >
                      <FaInstagram size={20} />
                    </ActionIcon>
                  </Tooltip>
                )}

                {form.values.website && (
                  <Tooltip label="Company Website">
                    <ActionIcon
                      component={Link}
                      to={form.values.website}
                      target="_blank"
                      variant="light"
                      color="gray"
                      size="lg"
                    >
                      <FaGlobe size={20} />
                    </ActionIcon>
                  </Tooltip>
                )}
              </Group>
            </Group>
          </div>
        </div>
      </Card>

      {/* Tabbed Content */}
      <form onSubmit={form.onSubmit(handleSubmit)}>
        <Tabs value={activeTab} onChange={setActiveTab} className="mb-6">
          <Tabs.List grow>
            <Tabs.Tab value="company" leftSection={<FaBuilding size={16} />}>
              Company Information
            </Tabs.Tab>
            <Tabs.Tab value="social" leftSection={<FaGlobe size={16} />}>
              Social Media
            </Tabs.Tab>
            <Tabs.Tab value="details" leftSection={<FaBriefcase size={16} />}>
              Company Details
            </Tabs.Tab>
          </Tabs.List>

          {/* Company Information Tab */}
          <Tabs.Panel value="company" pt="md">
            <Card withBorder radius="md" className={cardClasses}>
              <Title order={3} className={titleClasses}>
                Company Information
              </Title>

              <Grid gutter="md">
                <Grid.Col span={{ base: 12, sm: 6 }}>
                  <TextInput
                    label="Company Name"
                    placeholder="Enter your company name"
                    required
                    leftSection={
                      <FaBuilding size={16} className="text-gray-500" />
                    }
                    {...form.getInputProps("companyName")}
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, sm: 6 }}>
                  <TextInput
                    label="Contact Person"
                    placeholder="Enter contact person's name"
                    required
                    leftSection={<FaUser size={16} className="text-gray-500" />}
                    {...form.getInputProps("contactPerson")}
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, sm: 6 }}>
                  <TextInput
                    label="Email"
                    placeholder="Enter company email"
                    required
                    leftSection={
                      <FaEnvelope size={16} className="text-gray-500" />
                    }
                    {...form.getInputProps("email")}
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, sm: 6 }}>
                  <TextInput
                    label="Phone"
                    placeholder="Enter company phone number"
                    required
                    leftSection={
                      <FaPhone size={16} className="text-gray-500" />
                    }
                    {...form.getInputProps("phone")}
                  />
                </Grid.Col>
                <Grid.Col span={12}>
                  <TextInput
                    label="Website"
                    placeholder="Enter company website URL"
                    leftSection={
                      <FaGlobe size={16} className="text-gray-500" />
                    }
                    {...form.getInputProps("website")}
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, sm: 6 }}>
                  <Select
                    label="Industry"
                    placeholder="Select your industry"
                    data={industries}
                    searchable
                    required
                    leftSection={
                      <FaBriefcase size={16} className="text-gray-500" />
                    }
                    {...form.getInputProps("industry")}
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, sm: 6 }}>
                  <TextInput
                    label="Location"
                    placeholder="Enter company location"
                    leftSection={
                      <FaMapMarkerAlt size={16} className="text-gray-500" />
                    }
                    {...form.getInputProps("location")}
                  />
                </Grid.Col>
                <Grid.Col span={12}>
                  <Text size="sm" fw={500} className="mb-2">
                    Company Description
                  </Text>
                  <Textarea
                    placeholder="Describe your company"
                    rows={6}
                    {...form.getInputProps("companyDescription")}
                  />
                </Grid.Col>
              </Grid>
            </Card>
          </Tabs.Panel>

          {/* Social Media Tab */}
          <Tabs.Panel value="social" pt="md">
            <Card withBorder radius="md" className={cardClasses}>
              <Title order={3} className={titleClasses}>
                Social Media Links
              </Title>

              <Text className={textClasses}>
                Add your company&apos;s social media profiles to increase
                visibility and connect with candidates.
              </Text>

              <Grid gutter="md">
                <Grid.Col span={{ base: 12, sm: 6 }}>
                  <TextInput
                    label="LinkedIn"
                    placeholder="Enter LinkedIn profile URL"
                    leftSection={
                      <FaLinkedin size={16} className="text-blue-500" />
                    }
                    {...form.getInputProps("linkedin")}
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, sm: 6 }}>
                  <TextInput
                    label="Twitter"
                    placeholder="Enter Twitter profile URL"
                    leftSection={
                      <FaTwitter size={16} className="text-cyan-500" />
                    }
                    {...form.getInputProps("twitter")}
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, sm: 6 }}>
                  <TextInput
                    label="Facebook"
                    placeholder="Enter Facebook profile URL"
                    leftSection={
                      <FaFacebook size={16} className="text-indigo-500" />
                    }
                    {...form.getInputProps("facebook")}
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, sm: 6 }}>
                  <TextInput
                    label="Instagram"
                    placeholder="Enter Instagram profile URL"
                    leftSection={
                      <FaInstagram size={16} className="text-pink-500" />
                    }
                    {...form.getInputProps("instagram")}
                  />
                </Grid.Col>
              </Grid>
            </Card>
          </Tabs.Panel>

          {/* Company Details Tab */}
          <Tabs.Panel value="details" pt="md">
            <Card withBorder radius="md" className={cardClasses}>
              <Title order={3} className={titleClasses}>
                Company Details
              </Title>

              <Text className={textClasses}>
                These details help candidates understand more about your company
                size and type.
              </Text>

              <Grid gutter="md">
                <Grid.Col span={{ base: 12, sm: 6 }}>
                  <Select
                    label="Company Size"
                    placeholder="Select company size"
                    data={[
                      { value: "1-10", label: "1-10 employees" },
                      { value: "11-50", label: "11-50 employees" },
                      { value: "51-200", label: "51-200 employees" },
                      { value: "201-500", label: "201-500 employees" },
                      { value: "501-1000", label: "501-1000 employees" },
                      { value: "1000+", label: "1000+ employees" },
                    ]}
                    required
                    leftSection={
                      <FaUsers size={16} className="text-gray-500" />
                    }
                    {...form.getInputProps("companySize")}
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, sm: 6 }}>
                  <Select
                    label="Company Type"
                    placeholder="Select company type"
                    data={[
                      { value: "Startup", label: "Startup" },
                      { value: "Small Business", label: "Small Business" },
                      {
                        value: "Medium Enterprise",
                        label: "Medium Enterprise",
                      },
                      { value: "Large Enterprise", label: "Large Enterprise" },
                      { value: "Corporation", label: "Corporation" },
                      { value: "Non-Profit", label: "Non-Profit" },
                      { value: "Government", label: "Government" },
                      {
                        value: "Educational Institution",
                        label: "Educational Institution",
                      },
                      {
                        value: "Freelancer/Self-Employed",
                        label: "Freelancer/Self-Employed",
                      },
                    ]}
                    required
                    leftSection={
                      <FaBuilding size={16} className="text-gray-500" />
                    }
                    {...form.getInputProps("companyType")}
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, sm: 6 }}>
                  <NumberInput
                    label="Founded Year"
                    placeholder="Enter year company was founded"
                    min={1800}
                    max={new Date().getFullYear()}
                    hideControls
                    leftSection={
                      <FaCalendarAlt size={16} className="text-gray-500" />
                    }
                    {...form.getInputProps("foundedYear")}
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, sm: 6 }}>
                  <Checkbox
                    label="Request company verification"
                    description="Verified companies receive more applications and higher quality candidates"
                    checked={form.values.verificationRequested}
                    disabled={
                      verificationStatus.status !== "not_requested" ||
                      profileCompletion < 80
                    }
                    {...form.getInputProps("verificationRequested", {
                      type: "checkbox",
                    })}
                  />
                  {profileCompletion < 80 && (
                    <Text size="xs" c="dimmed" mt={5}>
                      Complete your profile to at least 80% to request
                      verification
                    </Text>
                  )}
                </Grid.Col>
              </Grid>
            </Card>
          </Tabs.Panel>
        </Tabs>

        <Group justify="flex-end" mt="xl">
          <Button
            type="submit"
            size="md"
            leftSection={<FaSave size={16} />}
            loading={isLoading}
            disabled={isLoading}
          >
            {isLoading ? "Saving Changes..." : "Save All Changes"}
          </Button>
        </Group>
      </form>

      {/* Company Logo Modal */}
      <Modal
        opened={logoModalOpened}
        onClose={closeLogoModal}
        title="Update Company Logo"
        centered
      >
        <div className="mb-6 flex flex-col items-center">
          <Avatar
            size={150}
            radius="md"
            color="blue"
            className="mb-4 border-4 border-white shadow-md"
            src={logoPreview}
          >
            {!logoPreview && form.values.companyName.charAt(0)}
          </Avatar>

          <FileInput
            label="Upload new company logo"
            placeholder="Click to select an image"
            accept="image/png,image/jpeg,image/jpg"
            className="mb-4 w-full"
            onChange={handleLogoChange}
            leftSection={<FaUpload size={16} />}
            clearable
          />

          <Text size="sm" c="dimmed" className="mb-4 text-center">
            Recommended: Square image, at least 400x400 pixels
          </Text>

          <Group justify="center" className="w-full">
            <Button variant="default" onClick={closeLogoModal}>
              Cancel
            </Button>
            {logoPreview && (
              <Button color="red" onClick={handleRemoveLogo}>
                Remove Logo
              </Button>
            )}
          </Group>
        </div>
      </Modal>

      {/* Profile Image Modal */}
      <Modal
        opened={profileImageModalOpened}
        onClose={handleCloseProfileImageModal}
        title="Update Profile Picture"
        centered
      >
        <div className="mb-6 flex flex-col items-center">
          <Avatar
            size={150}
            radius="md"
            color="blue"
            className="mb-4 border-4 border-white shadow-md"
            src={
              profileImagePreview ||
              getProfileImageUrl(
                profile?.user?.profileImageUrl || user?.profileImageUrl,
              )
            }
          >
            {!profileImagePreview &&
              !profile?.user?.profileImageUrl &&
              !user?.profileImageUrl &&
              (user?.name?.charAt(0) || "U")}
          </Avatar>

          <FileInput
            label="Upload new profile picture"
            placeholder="Click to select an image"
            accept="image/png,image/jpeg,image/jpg"
            className="mb-4 w-full"
            onChange={handleProfileImageChange}
            leftSection={<FaUpload size={16} />}
            clearable
          />

          <Text size="sm" c="dimmed" className="mb-4 text-center">
            Recommended: Square image, at least 400x400 pixels
          </Text>

          <Group justify="space-between" className="w-full">
            <Button variant="default" onClick={handleCloseProfileImageModal}>
              Cancel
            </Button>

            <Group>
              {selectedProfileImage && (
                <Button
                  onClick={handleSaveProfileImage}
                  loading={isUploadingImage}
                  leftSection={!isUploadingImage && <FaSave size={14} />}
                >
                  {isUploadingImage ? "Saving..." : "Save Photo"}
                </Button>
              )}
            </Group>
          </Group>
        </div>
      </Modal>
    </PageContainer>
  );
}
