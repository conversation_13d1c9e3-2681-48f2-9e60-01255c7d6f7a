"use client";

import { forgotPasswordSchema } from "@/schemas/auth/auth-schemas";
import { Button, Text, TextInput } from "@mantine/core";
import { useForm } from "@mantine/form";
import { zodResolver } from "mantine-form-zod-resolver";
import { useState } from "react";
import { FaEnvelope } from "react-icons/fa";
import { Link } from "react-router";

export default function ForgotPasswordForm() {
  const [isSubmitted, setIsSubmitted] = useState(false);

  const form = useForm({
    initialValues: {
      email: "",
    },
    validate: zodResolver(forgotPasswordSchema),
  });

  const handleSubmit = (values: typeof form.values) => {
    // Form is valid, you can handle forgot password logic here
    console.log("Forgot password form submitted:", values);
    setIsSubmitted(true);
  };

  if (isSubmitted) {
    return (
      <div className="flex flex-col items-center gap-4 text-center">
        <Text size="lg" fw={500}>
          Password Reset Email Sent
        </Text>
        <Text>
          We have sent an email to {form.values.email} with instructions to
          reset your password.
        </Text>
        <Link to="/auth/login">
          <Button variant="outline" mt="xl">
            Back to Login
          </Button>
        </Link>
      </div>
    );
  }

  return (
    <form
      noValidate
      onSubmit={form.onSubmit(handleSubmit)}
      className="flex flex-col gap-4"
    >
      <Text size="lg" fw={500} ta="center" mb="md">
        Forgot Your Password?
      </Text>
      <Text size="sm" ta="center" mb="lg" c="dimmed">
        Enter your email address and we will send you a link to reset your
        password.
      </Text>

      <TextInput
        label="Email"
        placeholder="<EMAIL>"
        required
        leftSection={<FaEnvelope />}
        {...form.getInputProps("email")}
      />

      <Button type="submit" fullWidth mt="xl">
        Send Reset Link
      </Button>

      <Text ta="center" mt="md">
        <Link
          to="/auth/login"
          className="text-sm text-blue-600 hover:underline"
        >
          Back to Login
        </Link>
      </Text>
    </form>
  );
}
