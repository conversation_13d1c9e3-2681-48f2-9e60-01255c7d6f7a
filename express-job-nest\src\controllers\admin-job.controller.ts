import { Request, Response, NextFunction } from "express";
import { catchAsync } from "../utils/catch-async";
import { AppError } from "../utils/app-error";
import Job from "../models/job.model";
import Application from "../models/application.model";

// Get all jobs with filtering and pagination
export const getAllJobs = catchAsync(
  async (req: Request, res: Response, _next: NextFunction) => {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const skip = (page - 1) * limit;

    // Build query
    const queryObj: any = {};

    // Filter by status
    if (req.query.status === "active") {
      queryObj.isActive = true;
    } else if (req.query.status === "inactive") {
      queryObj.isActive = false;
    }

    // Filter by job type
    if (req.query.jobType && req.query.jobType !== "all") {
      queryObj.jobType = req.query.jobType;
    }

    // Filter by category
    if (req.query.category && req.query.category !== "all") {
      queryObj.category = req.query.category;
    }

    // Search by title or company
    if (req.query.search) {
      queryObj.$or = [
        { title: { $regex: req.query.search, $options: "i" } },
        { description: { $regex: req.query.search, $options: "i" } },
      ];
    }

    // Execute query with pagination
    const jobs = await Job.find(queryObj)
      .populate({
        path: "company",
        select: "name logo isVerified",
      })
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    // Get total count
    const totalJobs = await Job.countDocuments(queryObj);

    res.status(200).json({
      success: true,
      count: jobs.length,
      totalPages: Math.ceil(totalJobs / limit),
      currentPage: page,
      jobs,
    });
  }
);

// Get job by ID
export const getJobById = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    const job = await Job.findById(req.params.id).populate({
      path: "company",
      select: "name logo isVerified owner",
      populate: {
        path: "owner",
        select: "name email",
      },
    });

    if (!job) {
      return next(new AppError("Job not found", 404));
    }

    // Get application count for this job
    const applicationCount = await Application.countDocuments({
      job: req.params.id,
    });

    res.status(200).json({
      success: true,
      job,
      applicationCount,
    });
  }
);

// Update job
export const updateJob = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    // Find the job
    const job = await Job.findById(req.params.id);

    if (!job) {
      return next(new AppError("Job not found", 404));
    }

    // Update the job
    const updatedJob = await Job.findByIdAndUpdate(req.params.id, req.body, {
      new: true,
      runValidators: true,
    }).populate({
      path: "company",
      select: "name logo",
    });

    res.status(200).json({
      success: true,
      job: updatedJob,
    });
  }
);

// Delete job
export const deleteJob = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    // Find the job
    const job = await Job.findById(req.params.id);

    if (!job) {
      return next(new AppError("Job not found", 404));
    }

    // Delete the job
    await Job.findByIdAndDelete(req.params.id);

    // Delete all applications for this job
    await Application.deleteMany({ job: req.params.id });

    res.status(200).json({
      success: true,
      message: "Job deleted successfully",
    });
  }
);

// Approve job
export const approveJob = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    // Find the job
    const job = await Job.findById(req.params.id);

    if (!job) {
      return next(new AppError("Job not found", 404));
    }

    // Update the job status
    job.isActive = true;
    await job.save();

    res.status(200).json({
      success: true,
      message: "Job approved successfully",
      job,
    });
  }
);

// Reject job
export const rejectJob = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    const { reason } = req.body;

    // Find the job
    const job = await Job.findById(req.params.id);

    if (!job) {
      return next(new AppError("Job not found", 404));
    }

    // Update the job status
    job.isActive = false;
    await job.save();

    // TODO: Send notification to employer with rejection reason
    console.log(`Job ${job._id} rejected with reason: ${reason}`);

    res.status(200).json({
      success: true,
      message: "Job rejected successfully",
      job,
    });
  }
);

// Get job categories
export const getJobCategories = catchAsync(
  async (_req: Request, res: Response, _next: NextFunction) => {
    const categories = await Job.distinct("category");

    res.status(200).json({
      success: true,
      categories,
    });
  }
);
