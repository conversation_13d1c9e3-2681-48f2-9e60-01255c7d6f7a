"use client";

import { getApplicationsCountByJobId } from "@/data/job-applications";
import { cn } from "@/design-system/utils";
import { useThemeClasses } from "@/design-system/utils/theme-utils";
import { type Job } from "@/hooks/employer/use-manage-jobs";
import { useConfirmDeleteJobModal } from "@/stores/modal-store";
import {
  ActionIcon,
  Badge,
  Button,
  Card,
  Divider,
  Flex,
  Grid,
  Group,
  Menu,
  Paper,
  Text,
  useMantineColorScheme,
} from "@mantine/core";
import { useHover } from "@mantine/hooks";
import {
  FaBuilding,
  FaCalendarAlt,
  FaEdit,
  FaEllipsisV,
  FaMapMarkerAlt,
  FaTrash,
  FaUsers,
} from "react-icons/fa";
import { useNavigate } from "react-router";

// Extended Job type to handle both backend and frontend job formats
interface ExtendedJob extends Job {
  id?: string | number; // Add optional id field for frontend mock data
}

type JobCardProps = {
  job: ExtendedJob;
  viewMode: "grid" | "list";
  onDelete?: (id: number | string) => void;
  onToggleStatus?: (id: number | string) => void;
  isDeleting?: boolean;
  isToggling?: boolean;
};

export default function JobCard({
  job,
  viewMode,
  onDelete,
  onToggleStatus,
  isDeleting = false,
  isToggling = false,
}: JobCardProps) {
  const navigate = useNavigate();
  const confirmDeleteJobModal = useConfirmDeleteJobModal();

  // Get the applications count for this job from our mock data
  // Use _id from backend or fallback to id for mock data
  const jobId = job._id || job.id?.toString() || "unknown";
  console.log("JobCard - job:", job);
  console.log("JobCard - jobId:", jobId);
  const applicationsCount = getApplicationsCountByJobId(parseInt(jobId) || 0);

  const getStatusColor = (status: "Active" | "Pending" | "Closed") => {
    switch (status) {
      case "Active":
        return "blue";
      case "Pending":
        return "yellow";
      case "Closed":
        return "red";
      default:
        return "gray";
    }
  };

  // Function to get company initials for the logo
  const getCompanyInitials = (companyName: string) => {
    return companyName
      .split(" ")
      .map((word) => word[0])
      .join("")
      .toUpperCase()
      .substring(0, 2);
  };

  // Format date posted
  const formatDate = (date: string | undefined) => {
    if (!date || date === "Recently") return "Recently";

    try {
      // Try to parse the date
      const parsedDate = new Date(date);

      // Check if the date is valid
      if (isNaN(parsedDate.getTime())) {
        return "Recently";
      }

      return parsedDate.toLocaleDateString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric",
      });
    } catch (error) {
      console.error("Error formatting date:", error);
      return "Recently";
    }
  };

  // Get company name
  const getCompanyName = () => {
    return typeof job.company === "string"
      ? job.company
      : job.company?.name || "Unknown Company";
  };

  // Get date posted (use createdAt from backend or fallback)
  const getDatePosted = () => {
    // Check if we have a valid date string
    if (job.createdAt && typeof job.createdAt === "string") {
      return job.createdAt;
    }

    // Fallback to current date if no valid date is available
    return new Date().toISOString();
  };

  // Handle edit job
  const handleEditJob = () => {
    // Debug log the job ID
    console.log("Navigating to edit job with ID:", jobId);
    navigate(`/employer/manage-jobs/${jobId}/edit`);
  };

  // Handle view applications
  const handleViewApplications = () => {
    navigate(`/employer/manage-jobs/${jobId}/applications`);
  };

  // Handle delete job
  const handleDeleteJob = () => {
    if (onDelete && jobId !== "unknown") {
      confirmDeleteJobModal.open({
        onConfirm: () => onDelete(jobId),
        jobTitle: job.title,
      });
    } else {
      confirmDeleteJobModal.open({
        onConfirm: () => console.log("Would delete job:", jobId),
        jobTitle: job.title,
      });
    }
  };

  // Handle toggle job status
  const handleToggleStatus = () => {
    if (onToggleStatus && jobId !== "unknown") {
      onToggleStatus(jobId);
    } else {
      console.log("Would toggle status for job:", jobId);
    }
  };

  // Get random color for company avatar background
  const getRandomColor = () => {
    const { colorScheme } = useMantineColorScheme();
    const isDark = colorScheme === "dark";

    // Use a deterministic color based on company name to ensure consistency
    const getColorFromString = (str: string) => {
      const hash = str.split("").reduce((acc, char) => {
        return char.charCodeAt(0) + ((acc << 5) - acc);
      }, 0);

      const index = Math.abs(hash) % 6; // 6 colors in each array

      const lightColors = [
        "#E9F7EF", // Light green
        "#EBF5FB", // Light blue
        "#F4ECF7", // Light purple
        "#FEF9E7", // Light yellow
        "#F2F3F4", // Light gray
        "#FDEDEC", // Light red
      ];

      const darkColors = [
        "#1E3A29", // Dark green
        "#1A3A4A", // Dark blue
        "#2E2A3A", // Dark purple
        "#3A3A2A", // Dark yellow
        "#2A2A2A", // Dark gray
        "#3A2A2A", // Dark red
      ];

      return isDark ? darkColors[index] : lightColors[index];
    };

    return getColorFromString(getCompanyName());
  };

  // Get common hooks outside of conditional rendering
  const { hovered, ref } = useHover();
  const { colorScheme } = useMantineColorScheme();
  const isDark = colorScheme === "dark";

  // Grid view card
  if (viewMode === "grid") {
    return (
      <Grid.Col span={{ base: 12, sm: 6, md: 4 }}>
        <Card
          withBorder
          radius="md"
          className={cn(
            "h-full transition-all duration-200",
            isDark
              ? "border-dark-4 hover:border-primary-color/30 hover:shadow-dark-lg"
              : "border-gray-200 hover:border-primary-color/20 hover:shadow-md",
          )}
          p={{ base: "sm", sm: "md", md: "lg" }}
          ref={ref}
        >
          <Flex
            direction="column"
            gap={{ base: "xs", sm: "md" }}
            className="h-full"
          >
            {/* Header with company logo, title and status */}
            <Group justify="space-between" align="flex-start" wrap="nowrap">
              <Group gap="sm" align="flex-start" wrap="nowrap">
                <div
                  className="flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-md sm:h-12 sm:w-12"
                  style={{ backgroundColor: getRandomColor() }}
                >
                  <Text
                    fw={700}
                    size="sm"
                    className={useThemeClasses(
                      "text-gray-800",
                      "text-gray-100",
                    )}
                  >
                    {getCompanyInitials(getCompanyName())}
                  </Text>
                </div>
                <div className="min-w-0">
                  <Text size="md" fw={600} className="line-clamp-1 sm:text-lg">
                    {job.title}
                  </Text>
                  <Group gap="xs" mt={2}>
                    <FaBuilding
                      size={12}
                      className={useThemeClasses(
                        "text-gray-500",
                        "text-gray-400",
                      )}
                    />
                    <Text
                      size="xs"
                      c="dimmed"
                      className="line-clamp-1 sm:text-sm"
                    >
                      {getCompanyName()}
                    </Text>
                  </Group>
                </div>
              </Group>
              <Badge
                color={getStatusColor(job.status || "Active")}
                size="sm"
                variant="light"
                className="flex-shrink-0 sm:size-md"
              >
                {job.status}
              </Badge>
            </Group>

            {/* Job details */}
            <Paper
              withBorder
              p={{ base: "xs", sm: "sm" }}
              radius="md"
              className={useThemeClasses(
                "bg-gray-50 border-gray-100",
                "bg-dark-8 border-dark-5",
              )}
            >
              <Group gap="xs" mb={4} className="flex-nowrap">
                <FaMapMarkerAlt
                  size={12}
                  className={useThemeClasses("text-gray-500", "text-gray-400")}
                />
                <Text size="xs" c="dimmed" className="sm:text-sm">
                  {job.location}
                </Text>
              </Group>
              <Group gap="xs" mb={4} className="flex-nowrap">
                <FaCalendarAlt
                  size={12}
                  className={useThemeClasses("text-gray-500", "text-gray-400")}
                />
                <Text size="xs" c="dimmed" className="sm:text-sm">
                  Posted: {formatDate(getDatePosted())}
                </Text>
              </Group>
              <Group gap="xs" className="flex-nowrap">
                <FaUsers
                  size={12}
                  className="text-primary-color sm:text-[14px]"
                />
                <Text
                  size="xs"
                  fw={500}
                  className="text-primary-color sm:text-sm"
                >
                  {applicationsCount} Application
                  {applicationsCount !== 1 ? "s" : ""}
                </Text>
              </Group>
            </Paper>

            {/* Action buttons */}
            <div className="mt-auto">
              <Group mb="xs" grow>
                <Button
                  variant="light"
                  leftSection={<FaEdit size={12} className="sm:text-[14px]" />}
                  onClick={handleEditJob}
                  disabled={isDeleting || isToggling}
                  size="xs"
                  className="transition-all duration-200 hover:bg-primary-color/10 sm:size-sm"
                >
                  Edit
                </Button>
                <Button
                  variant="light"
                  color={job.status === "Active" ? "red" : "green"}
                  leftSection={
                    job.status === "Active" ? (
                      <FaTrash size={12} className="sm:text-[14px]" />
                    ) : (
                      <FaUsers size={12} className="sm:text-[14px]" />
                    )
                  }
                  onClick={handleToggleStatus}
                  loading={isToggling}
                  disabled={isDeleting || isToggling}
                  size="xs"
                  className="transition-all duration-200 sm:size-sm"
                >
                  {job.status === "Active" ? "Deactivate" : "Activate"}
                </Button>
              </Group>
              <Divider my="xs" />
              <Group grow>
                <Button
                  variant="filled"
                  color="primary"
                  leftSection={<FaUsers size={12} className="sm:text-[14px]" />}
                  onClick={handleViewApplications}
                  disabled={isDeleting || isToggling}
                  size="xs"
                  className="transition-all duration-200 sm:size-sm"
                >
                  Applications
                </Button>
                <Button
                  variant="outline"
                  color="red"
                  leftSection={<FaTrash size={12} className="sm:text-[14px]" />}
                  onClick={handleDeleteJob}
                  loading={isDeleting}
                  disabled={isDeleting || isToggling}
                  size="xs"
                  className="transition-all duration-200 sm:size-sm"
                >
                  Delete
                </Button>
              </Group>
            </div>
          </Flex>
        </Card>
      </Grid.Col>
    );
  }

  // List view card
  return (
    <Grid.Col span={12}>
      <Card
        withBorder
        radius="md"
        className={cn(
          "transition-all duration-200",
          isDark
            ? "border-dark-4 hover:border-primary-color/30 hover:shadow-dark-lg"
            : "border-gray-200 hover:border-primary-color/20 hover:shadow-md",
          hovered ? "transform-gpu scale-[1.005]" : "",
        )}
        p={{ base: "xs", sm: "md" }}
        ref={ref}
      >
        <Group justify="space-between" wrap="nowrap">
          {/* Left side with job info */}
          <Group
            wrap="nowrap"
            align="flex-start"
            gap="xs"
            className="min-w-0 sm:gap-md"
          >
            {/* Company logo */}
            <div
              className="flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-md sm:h-14 sm:w-14"
              style={{ backgroundColor: getRandomColor() }}
            >
              <Text
                fw={700}
                size="md"
                className={useThemeClasses("text-gray-800", "text-gray-100")}
              >
                {getCompanyInitials(getCompanyName())}
              </Text>
            </div>

            {/* Job details */}
            <div className="min-w-0 flex-1">
              {/* Title and mobile status badge */}
              <div className="flex w-full items-start justify-between">
                <Text
                  size="md"
                  fw={600}
                  className="line-clamp-1 pr-2 sm:text-lg"
                >
                  {job.title}
                </Text>
                <Badge
                  color={getStatusColor(job.status || "Active")}
                  size="sm"
                  variant="light"
                  className="flex-shrink-0 sm:hidden"
                >
                  {job.status}
                </Badge>
              </div>

              {/* Job details row */}
              <Group gap="xs" mt={4} className="flex-wrap">
                {/* Company */}
                <Group gap="xs" className="flex-nowrap">
                  <FaBuilding
                    size={12}
                    className={useThemeClasses(
                      "text-gray-500",
                      "text-gray-400",
                    )}
                  />
                  <Text
                    size="xs"
                    c="dimmed"
                    className="line-clamp-1 sm:text-sm"
                  >
                    {getCompanyName()}
                  </Text>
                </Group>

                {/* Separator - hidden on very small screens */}
                <Text
                  size="xs"
                  c="dimmed"
                  className="hidden xs:block sm:text-sm"
                >
                  •
                </Text>

                {/* Location */}
                <Group gap="xs" className="flex-nowrap">
                  <FaMapMarkerAlt
                    size={12}
                    className={useThemeClasses(
                      "text-gray-500",
                      "text-gray-400",
                    )}
                  />
                  <Text size="xs" c="dimmed" className="sm:text-sm">
                    {job.location}
                  </Text>
                </Group>
              </Group>

              {/* Second row of details */}
              <Group gap="xs" mt={4} className="flex-wrap">
                {/* Date Posted */}
                <Group gap="xs" className="flex-nowrap">
                  <FaCalendarAlt
                    size={12}
                    className={useThemeClasses(
                      "text-gray-500",
                      "text-gray-400",
                    )}
                  />
                  <Text size="xs" c="dimmed" className="sm:text-sm">
                    Posted: {formatDate(getDatePosted())}
                  </Text>
                </Group>

                {/* Separator */}
                <Text size="xs" c="dimmed" className="sm:text-sm">
                  •
                </Text>

                {/* Applications count */}
                <Group gap="xs" className="flex-nowrap">
                  <FaUsers
                    size={12}
                    className="text-primary-color sm:text-[14px]"
                  />
                  <Text
                    size="xs"
                    fw={500}
                    className="text-primary-color sm:text-sm"
                  >
                    {applicationsCount} Application
                    {applicationsCount !== 1 ? "s" : ""}
                  </Text>
                </Group>
              </Group>
            </div>
          </Group>

          {/* Right side with actions */}
          <Group wrap="nowrap" align="center" className="flex-shrink-0 ml-2">
            {/* Status badge for tablet and desktop */}
            {/* <Badge
              color={getStatusColor(job.status || "Active")}
              size="sm"
              variant="light"
              className="hidden sm:flex sm:size-md"
            >
              {job.status}
            </Badge> */}

            {/* Desktop action buttons */}
            {/* <Group gap="xs" className="hidden md:flex">
              <Tooltip label="Edit Job">
                <ActionIcon
                  variant="light"
                  onClick={handleEditJob}
                  size="md"
                  disabled={isDeleting || isToggling}
                  className="transition-all duration-200 hover:bg-primary-color/10"
                >
                  <FaEdit size={14} />
                </ActionIcon>
              </Tooltip>
              <Button
                variant="light"
                color="primary"
                leftSection={<FaUsers size={14} />}
                onClick={handleViewApplications}
                disabled={isDeleting || isToggling}
                size="sm"
                className="transition-all duration-200 hover:bg-primary-color/10"
              >
                Applications
              </Button>
              <ActionIcon
                variant="light"
                color="red"
                onClick={handleDeleteJob}
                size="md"
                loading={isDeleting}
                disabled={isDeleting || isToggling}
                className="transition-all duration-200"
              >
                <FaTrash size={14} />
              </ActionIcon>
            </Group> */}

            {/* Mobile menu */}
            <Menu position="bottom-end" withinPortal>
              <Menu.Target>
                <div className="cursor-pointer">
                  <ActionIcon
                    className="md:hidden transition-all duration-200"
                    variant="light"
                    size="md"
                  >
                    <FaEllipsisV size={14} />
                  </ActionIcon>
                </div>
              </Menu.Target>
              <Menu.Dropdown>
                <Menu.Item
                  leftSection={<FaEdit size={14} />}
                  onClick={handleEditJob}
                  disabled={isDeleting || isToggling}
                >
                  Edit Job
                </Menu.Item>
                <Menu.Item
                  leftSection={<FaUsers size={14} />}
                  onClick={handleViewApplications}
                  disabled={isDeleting || isToggling}
                >
                  View Applications
                </Menu.Item>
                <Menu.Item
                  leftSection={
                    job.status === "Active" ? (
                      <FaTrash size={14} />
                    ) : (
                      <FaUsers size={14} />
                    )
                  }
                  color={job.status === "Active" ? "red" : "green"}
                  onClick={handleToggleStatus}
                  disabled={isDeleting || isToggling}
                >
                  {isToggling
                    ? "Updating..."
                    : job.status === "Active"
                      ? "Deactivate Job"
                      : "Activate Job"}
                </Menu.Item>
                <Menu.Divider />
                <Menu.Item
                  leftSection={<FaTrash size={14} />}
                  c="red"
                  onClick={handleDeleteJob}
                  disabled={isDeleting || isToggling}
                >
                  {isDeleting ? "Deleting..." : "Delete Job"}
                </Menu.Item>
              </Menu.Dropdown>
            </Menu>
          </Group>
        </Group>
      </Card>
    </Grid.Col>
  );
}
