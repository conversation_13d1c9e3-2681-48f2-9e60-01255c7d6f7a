import { candidateData } from "@/data/candidate-data";
import { Card, Text, Title } from "@mantine/core";

export default function CandidateEducation() {
  return (
    <Card withBorder radius="md" className="mb-6 p-6 shadow-sm">
      <Title order={2} className="mb-4 text-lg font-semibold">
        Education
      </Title>
      {candidateData.education.map((edu, index) => (
        <div
          key={index}
          className="mb-4 border-b border-gray-200 pb-4 last:border-b-0"
        >
          <Text size="md" className="mb-1" component="div">
            <strong>Institution:</strong> {edu.institution}
          </Text>
          <Text size="md" className="mb-1" component="div">
            <strong>Degree:</strong> {edu.degree}
          </Text>
          <Text size="md" className="mb-1" component="div">
            <strong>Duration:</strong> {edu.duration}
          </Text>
          <Text size="md" className="mb-1" component="div">
            <strong>Description:</strong> {edu.description}
          </Text>
        </div>
      ))}
    </Card>
  );
}
