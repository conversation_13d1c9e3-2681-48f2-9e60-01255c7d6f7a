"use client";

import SectionHeading from "@/design-system/components/typography/SectionHeading";
import {
  Button,
  Chip,
  Divider,
  Group,
  MultiSelect,
  RangeSlider,
  Select,
  Stack,
  Text,
  TextInput,
} from "@mantine/core";
import { DatePickerInput } from "@mantine/dates";
import { type UseFormReturnType } from "@mantine/form";
import {
  FaBuilding,
  FaCalendarAlt,
  FaFilter,
  FaMapMarkerAlt,
  FaSearch,
  FaSort,
  FaTrash,
} from "react-icons/fa";

interface JobFiltersProps {
  form: UseFormReturnType<any>;
  onSubmit: () => void;
  onReset: () => void;
}

export default function JobFilters({
  form,
  onSubmit,
  onReset,
}: JobFiltersProps) {
  return (
    <>
      <SectionHeading variant="admin" className="mb-4">
        <Group gap="xs">
          <FaFilter size={16} />
          <span>Filters</span>
        </Group>
      </SectionHeading>

      <form onSubmit={form.onSubmit(onSubmit)}>
        <Stack gap="md">
          <TextInput
            label="Search Jobs"
            placeholder="Job title or keyword"
            leftSection={<FaSearch size={14} />}
            {...form.getInputProps("search")}
          />

          <Divider label="Job Status" labelPosition="center" />

          <Select
            label="Status"
            placeholder="Select job status"
            data={[
              { label: "All Jobs", value: "all" },
              { label: "Active Jobs", value: "active" },
              { label: "Pending Approval", value: "pending" },
              { label: "Expired Jobs", value: "expired" },
              { label: "Rejected Jobs", value: "rejected" },
            ]}
            clearable={false}
            searchable
            value={form.values.status}
            onChange={(value) => form.setFieldValue("status", value || "all")}
          />

          <Divider label="Company" labelPosition="center" />

          <TextInput
            label="Company Name"
            placeholder="Filter by company"
            leftSection={<FaBuilding size={14} />}
            {...form.getInputProps("company")}
          />

          <Divider label="Categories" labelPosition="center" />

          <MultiSelect
            label="Job Categories"
            placeholder="Select categories"
            data={[
              { value: "technology", label: "Technology" },
              { value: "marketing", label: "Marketing" },
              { value: "finance", label: "Finance" },
              { value: "healthcare", label: "Healthcare" },
              { value: "education", label: "Education" },
              { value: "design", label: "Design" },
              { value: "sales", label: "Sales" },
              { value: "customer-service", label: "Customer Service" },
            ]}
            searchable
            clearable
          />

          <Divider label="Location" labelPosition="center" />

          <TextInput
            label="Location"
            placeholder="City, state, or country"
            leftSection={<FaMapMarkerAlt size={14} />}
          />

          <Divider label="Job Type" labelPosition="center" />

          <div>
            <Text size="sm" fw={500} mb="xs">
              Employment Type
            </Text>
            <Chip.Group multiple>
              <Group gap="xs">
                <Chip value="full-time" variant="light" color="blue">
                  Full-time
                </Chip>
                <Chip value="part-time" variant="light" color="green">
                  Part-time
                </Chip>
                <Chip value="contract" variant="light" color="orange">
                  Contract
                </Chip>
                <Chip value="internship" variant="light" color="grape">
                  Internship
                </Chip>
              </Group>
            </Chip.Group>
          </div>

          <Divider label="Salary Range" labelPosition="center" />

          <div>
            <Group justify="space-between" mb="xs">
              <Text size="sm" fw={500}>
                Salary Range
              </Text>
              <Text size="sm" c="dimmed">
                $50k - $150k
              </Text>
            </Group>
            <RangeSlider
              min={30}
              max={200}
              step={5}
              minRange={10}
              defaultValue={[50, 150]}
              marks={[
                { value: 50, label: "$50k" },
                { value: 100, label: "$100k" },
                { value: 150, label: "$150k" },
              ]}
            />
          </div>

          <Divider label="Posted Date" labelPosition="center" />

          <DatePickerInput
            type="range"
            label="Date Range"
            placeholder="Select date range"
            valueFormat="YYYY-MM-DD"
            clearable
            leftSection={<FaCalendarAlt size={14} />}
          />

          <Divider label="Sorting" labelPosition="center" />

          <Select
            label="Sort By"
            placeholder="Select field"
            leftSection={<FaSort size={14} />}
            data={[
              { value: "title", label: "Title" },
              { value: "company", label: "Company" },
              { value: "createdAt", label: "Posted Date" },
              { value: "status", label: "Status" },
            ]}
            {...form.getInputProps("sortBy")}
          />

          <Select
            label="Sort Order"
            placeholder="Select sort direction"
            data={[
              { label: "Ascending (A-Z, Oldest first)", value: "asc" },
              { label: "Descending (Z-A, Newest first)", value: "desc" },
            ]}
            clearable={false}
            value={form.values.sortOrder}
            onChange={(value) =>
              form.setFieldValue("sortOrder", value || "asc")
            }
          />

          <Divider />

          <Group justify="space-between">
            <Button
              variant="subtle"
              color="red"
              onClick={onReset}
              leftSection={<FaTrash size={14} />}
            >
              Clear All
            </Button>
            <Button
              type="submit"
              leftSection={<FaFilter size={14} />}
              className="bg-primary-color hover:bg-primary-color/90"
            >
              Apply Filters
            </Button>
          </Group>
        </Stack>
      </form>
    </>
  );
}
