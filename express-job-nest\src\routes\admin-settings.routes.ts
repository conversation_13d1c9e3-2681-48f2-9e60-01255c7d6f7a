import express from 'express';
import {
  getSettings,
  updateGeneralSettings,
  updateEmailSettings,
  updateJobSettings,
  updateNotificationSettings,
  testEmailSettings,
  resetSettings,
} from '../controllers/admin-settings.controller';
import { protect, restrictTo } from '../middleware/auth.middleware';
import { body } from 'express-validator';
import { validationMiddleware } from '../validators/validation.middleware';

// Define validators
const emailSettingsValidator = [
  body('smtpHost').optional().isString().withMessage('SMTP host must be a string'),
  body('smtpPort').optional().isInt().withMessage('SMTP port must be a number'),
  body('smtpUsername').optional().isString().withMessage('SMTP username must be a string'),
  body('smtpPassword').optional().isString().withMessage('SMTP password must be a string'),
  body('fromEmail').optional().isEmail().withMessage('From email must be a valid email'),
  body('fromName').optional().isString().withMessage('From name must be a string'),
];

const jobSettingsValidator = [
  body('defaultJobExpiryDays')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Default job expiry days must be a positive number'),
  body('maxJobsPerEmployer')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Max jobs per employer must be a positive number'),
  body('requireJobApproval').optional().isBoolean().withMessage('Require job approval must be a boolean'),
  body('allowJobEditing').optional().isBoolean().withMessage('Allow job editing must be a boolean'),
  body('jobCategories').optional().isArray().withMessage('Job categories must be an array'),
  body('jobTypes').optional().isArray().withMessage('Job types must be an array'),
  body('jobLocations').optional().isArray().withMessage('Job locations must be an array'),
];

const notificationSettingsValidator = [
  body('enableEmailNotifications').optional().isBoolean().withMessage('Enable email notifications must be a boolean'),
  body('notifyAdminOnNewUser').optional().isBoolean().withMessage('Notify admin on new user must be a boolean'),
  body('notifyAdminOnNewJob').optional().isBoolean().withMessage('Notify admin on new job must be a boolean'),
  body('notifyAdminOnNewCompany').optional().isBoolean().withMessage('Notify admin on new company must be a boolean'),
  body('notifyEmployerOnNewApplication')
    .optional()
    .isBoolean()
    .withMessage('Notify employer on new application must be a boolean'),
  body('notifyCandidateOnApplicationStatusChange')
    .optional()
    .isBoolean()
    .withMessage('Notify candidate on application status change must be a boolean'),
];

const generalSettingsValidator = [
  body('siteName').optional().isString().withMessage('Site name must be a string'),
  body('siteDescription').optional().isString().withMessage('Site description must be a string'),
  body('contactEmail').optional().isEmail().withMessage('Contact email must be a valid email'),
  body('supportEmail').optional().isEmail().withMessage('Support email must be a valid email'),
  body('phoneNumber').optional().isString().withMessage('Phone number must be a string'),
  body('address').optional().isString().withMessage('Address must be a string'),
  body('logoUrl').optional().isString().withMessage('Logo URL must be a string'),
  body('faviconUrl').optional().isString().withMessage('Favicon URL must be a string'),
  body('socialLinks').optional().isObject().withMessage('Social links must be an object'),
  body('maintenanceMode').optional().isBoolean().withMessage('Maintenance mode must be a boolean'),
];

const testEmailValidator = [
  body('email').isEmail().withMessage('Please provide a valid email address'),
];

const router = express.Router();

// All routes are protected and restricted to admins
router.use(protect);
router.use(restrictTo('admin'));

// Get all settings
router.get('/', getSettings);

// Update settings
router.patch(
  '/general',
  generalSettingsValidator,
  validationMiddleware,
  updateGeneralSettings
);

router.patch(
  '/email',
  emailSettingsValidator,
  validationMiddleware,
  updateEmailSettings
);

router.patch(
  '/job',
  jobSettingsValidator,
  validationMiddleware,
  updateJobSettings
);

router.patch(
  '/notification',
  notificationSettingsValidator,
  validationMiddleware,
  updateNotificationSettings
);

// Test email settings
router.post(
  '/test-email',
  testEmailValidator,
  validationMiddleware,
  testEmailSettings
);

// Reset settings to default
router.post('/reset', resetSettings);

export default router;
