import { z } from "zod";

// List of industries for validation and selection
export const INDUSTRIES = [
  "Technology",
  "Healthcare",
  "Finance",
  "Education",
  "Retail",
  "Manufacturing",
  "Media",
  "Hospitality",
  "Construction",
  "Transportation",
  "Energy",
  "Agriculture",
  "Government",
  "Non-profit",
  "Legal",
  "Real Estate",
  "Telecommunications",
  "Entertainment",
  "Consulting",
  "Other",
];

// Company size options
export const COMPANY_SIZES = [
  "1-10",
  "11-50",
  "51-200",
  "201-500",
  "501-1000",
  "1000+",
];

// Company types
export const COMPANY_TYPES = [
  "Startup",
  "Small Business",
  "Medium Enterprise",
  "Large Enterprise",
  "Corporation",
  "Non-Profit",
  "Government",
  "Educational Institution",
  "Freelancer/Self-Employed",
];

// Current year for founded year validation
const currentYear = new Date().getFullYear();

export const profileSchema = z.object({
  companyName: z.string().min(2, "Company name must be at least 2 characters"),
  contactPerson: z
    .string()
    .min(2, "Contact person name must be at least 2 characters"),
  email: z.string().email("Invalid email address"),
  phone: z.string().min(10, "Phone number must be at least 10 digits"),
  website: z.string().url("Invalid URL").optional().or(z.literal("")),
  companyDescription: z
    .string()
    .min(10, "Description must be at least 10 characters"),
  industry: z.enum(INDUSTRIES as [string, ...string[]], {
    errorMap: () => ({ message: "Please select a valid industry" }),
  }),
  location: z.string().min(2, "Location must be at least 2 characters"),
  companyLogo: z.instanceof(File).optional(),
  linkedin: z.string().url("Invalid URL").optional().or(z.literal("")),
  twitter: z.string().url("Invalid URL").optional().or(z.literal("")),
  facebook: z.string().url("Invalid URL").optional().or(z.literal("")),
  instagram: z.string().url("Invalid URL").optional().or(z.literal("")),
  companySize: z.enum(COMPANY_SIZES as [string, ...string[]], {
    errorMap: () => ({ message: "Please select a valid company size" }),
  }),
  companyType: z.enum(COMPANY_TYPES as [string, ...string[]], {
    errorMap: () => ({ message: "Please select a valid company type" }),
  }),
  foundedYear: z
    .number()
    .min(1800, "Founded year must be after 1800")
    .max(currentYear, "Founded year cannot be in the future")
    .optional()
    .nullable(),
  verificationRequested: z.boolean().optional(),
});
