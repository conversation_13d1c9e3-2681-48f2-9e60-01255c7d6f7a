import express from "express";
import {
  getAllJobs,
  getJob<PERSON><PERSON><PERSON>d,
  updateJob,
  deleteJob,
  approveJob,
  rejectJob,
  getJobCategories,
} from "../controllers/admin-job.controller";
import { protect, restrictTo } from "../middleware/auth.middleware";
import { body } from "express-validator";
import { validationMiddleware } from "../validators/validation.middleware";

// Define validators
const updateJobValidator = [
  body("title")
    .optional()
    .isString()
    .withMessage("Title must be a string")
    .isLength({ min: 3, max: 100 })
    .withMessage("Title must be between 3 and 100 characters"),
  body("description")
    .optional()
    .isString()
    .withMessage("Description must be a string")
    .isLength({ min: 10 })
    .withMessage("Description must be at least 10 characters"),
  body("requirements")
    .optional()
    .isString()
    .withMessage("Requirements must be a string")
    .isLength({ min: 10 })
    .withMessage("Requirements must be at least 10 characters"),
  body("jobType")
    .optional()
    .isIn(["full-time", "part-time", "contract", "internship", "temporary"])
    .withMessage(
      "Job type must be one of: full-time, part-time, contract, internship, temporary"
    ),
  body("location")
    .optional()
    .isIn(["remote", "onsite", "hybrid"])
    .withMessage("Location must be one of: remote, onsite, hybrid"),
  body("category").optional().isString().withMessage("Category must be a string"),
  body("isActive").optional().isBoolean().withMessage("isActive must be a boolean"),
];

const rejectJobValidator = [
  body("reason")
    .notEmpty()
    .withMessage("Rejection reason is required")
    .isString()
    .withMessage("Reason must be a string"),
];

const router = express.Router();

// All routes are protected and restricted to admins
router.use(protect);
router.use(restrictTo("admin"));

// Job management routes
router.get("/", getAllJobs);
router.get("/categories", getJobCategories);
router.get("/:id", getJobById);
router.patch("/:id", updateJobValidator, validationMiddleware, updateJob);
router.delete("/:id", deleteJob);
router.patch("/approve/:id", approveJob);
router.patch(
  "/reject/:id",
  rejectJobValidator,
  validationMiddleware,
  rejectJob
);

export default router;
