import UserButton from "@/components/auth/UserButton";
import { cn } from "@/design-system/utils";
import { useAuthStore } from "@/stores/auth-store";
import {
  Bad<PERSON>,
  Burger,
  Drawer,
  ThemeIcon,
  useMantineColorScheme,
} from "@mantine/core";
import { useEffect, useMemo, useState } from "react";
import {
  FaBriefcase,
  FaEnvelope,
  FaGraduationCap,
  FaSearch,
} from "react-icons/fa";
import { Link, useLocation } from "react-router";
import Logo from "./Logo";
import { ThemeToggleButton } from "./ThemeToggleButton";

// Define the type for navigation items
interface NavigationItem {
  id: number;
  name: string;
  href: string;
  icon: React.ComponentType<{ className?: string; size?: number }>;
  badge?: string;
}

// Base navigation items that are always shown
const baseNavigationItems: NavigationItem[] = [
  {
    id: 1,
    name: "Find Jobs",
    href: "/candidate/jobs",
    icon: FaSearch,
  },
  {
    id: 3,
    name: "Resources",
    href: "/resources",
    icon: FaGraduationCap,
  },
  {
    id: 4,
    name: "Contact Us",
    href: "/contact-us",
    icon: FaEnvelope,
  },
];

// Employer navigation item
const employerItem: NavigationItem = {
  id: 2,
  name: "For Employers",
  href: "/employer/dashboard",
  icon: FaBriefcase,
  badge: "Hiring",
};

export default function Navbar() {
  const [isOpened, setIsOpened] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const { pathname } = useLocation();
  const { colorScheme } = useMantineColorScheme();
  const isDark = colorScheme === "dark";

  // Get auth state
  const { isLoggedIn, user } = useAuthStore();

  // Generate navigation menu based on auth state and user role
  const navigationMenu = useMemo(() => {
    const menu = [...baseNavigationItems];

    // Add employer link based on user role
    // Show employer link if user is not logged in or if user is an employer
    if (!isLoggedIn || user?.role === "employer") {
      // For non-logged in users, redirect to login page with employer role
      // For logged in employers, redirect to employer dashboard
      const employerLink = {
        ...employerItem,
        href: isLoggedIn ? "/employer/dashboard" : "/auth/login?role=employer",
      };

      // Insert employer item after "Find Jobs"
      menu.splice(1, 0, employerLink);
    }

    return menu;
  }, [isLoggedIn, user]);

  // Handle scroll effect for sticky navbar
  useEffect(() => {
    const handleScroll = () => {
      const offset = window.scrollY;
      if (offset > 50) {
        setScrolled(true);
      } else {
        setScrolled(false);
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  return (
    <header
      className={cn(
        "sticky top-0 z-50 w-full transition-all duration-300",
        scrolled
          ? isDark
            ? "bg-gray-900 py-2 shadow-md border-b border-gray-800"
            : "bg-white py-2 shadow-md"
          : isDark
            ? "bg-gray-900/95 py-3 backdrop-blur-sm border-b border-gray-800/50"
            : "bg-white/95 py-3 backdrop-blur-sm",
      )}
    >
      {/* Main navbar container */}
      <div className="mx-auto max-w-7xl px-4 sm:px-6 xl:px-8">
        <div className="flex h-16 items-center justify-between">
          {/* Left section - Logo */}
          <div className="flex-shrink-0">
            <Logo />
          </div>

          {/* Middle section - Desktop Navigation */}
          <div className="hidden xl:flex xl:items-center xl:justify-center xl:space-x-8">
            {navigationMenu.map((item) => (
              <Link
                key={item.id}
                to={item.href}
                className={cn(
                  "group relative flex items-center gap-1.5 rounded-full px-4 py-2 text-sm font-medium transition-all duration-200",
                  pathname === item.href
                    ? "bg-primary-color/10 text-primary-color"
                    : isDark
                      ? "text-gray-300 hover:bg-gray-800"
                      : "text-gray-700 hover:bg-gray-100",
                )}
              >
                <item.icon className="h-4 w-4" />
                <span>{item.name}</span>
                {item.badge && (
                  <Badge size="xs" radius="sm" color="green" className="ml-1">
                    {item.badge}
                  </Badge>
                )}
                {pathname === item.href && (
                  <span className="bg-primary-color absolute -bottom-1 left-1/2 h-0.5 w-8 -translate-x-1/2 rounded-full" />
                )}
              </Link>
            ))}
          </div>

          {/* Right section - User actions */}
          <div className="flex items-center gap-2 sm:gap-4">
            {/* Theme toggle button */}
            <div className="hidden sm:block">
              <ThemeToggleButton />
            </div>

            {/* User button */}
            <div className="hidden xl:block">
              <UserButton />
            </div>

            {/* Mobile menu button */}
            <div
              className={`inline-flex items-center justify-center rounded-full p-2 transition-all xl:hidden ${
                isDark
                  ? "bg-gray-800 text-gray-300 hover:bg-gray-700"
                  : "bg-gray-100 text-gray-700 hover:bg-gray-200"
              }`}
              onClick={() => setIsOpened(true)}
              aria-label="Open main menu"
            >
              <Burger opened={isOpened} size="md" color="currentColor" />
            </div>
          </div>
        </div>
      </div>

      {/* Mobile menu drawer */}
      <Drawer
        opened={isOpened}
        onClose={() => setIsOpened(false)}
        position="right"
        size="sm"
        withCloseButton
        title={<Logo />}
        classNames={{
          header: isDark
            ? "border-b border-gray-700 pb-2 bg-gray-900"
            : "border-b pb-2",
          body: "px-0",
          content: isDark ? "bg-gray-900" : "",
          close: isDark ? "text-gray-300 hover:bg-gray-800" : "",
        }}
        zIndex={1000}
        overlayProps={{ opacity: 0.55, blur: 3 }}
      >
        <div className="flex h-full flex-col">
          {/* Navigation links */}
          <div className="space-y-2 px-4 py-4">
            {navigationMenu.map((item) => (
              <Link
                key={item.id}
                to={item.href}
                className={cn(
                  "flex items-center gap-3 rounded-lg px-4 py-3 text-base font-medium transition-all",
                  pathname === item.href
                    ? "bg-primary-color/10 text-primary-color"
                    : isDark
                      ? "text-gray-300 hover:bg-gray-800"
                      : "text-gray-700 hover:bg-gray-100",
                )}
                onClick={() => setIsOpened(false)}
              >
                <ThemeIcon
                  variant="light"
                  radius="xl"
                  size="md"
                  color={pathname === item.href ? "blue" : "gray"}
                >
                  <item.icon size={16} />
                </ThemeIcon>
                <span>{item.name}</span>
                {item.badge && (
                  <Badge size="sm" color="green">
                    {item.badge}
                  </Badge>
                )}
              </Link>
            ))}
          </div>

          {/* User section */}
          <div
            className={`mt-auto border-t px-4 py-4 ${
              isDark ? "border-gray-700" : "border-gray-200"
            }`}
          >
            <div className="flex items-center justify-between mb-4">
              <span
                className={`text-sm font-medium ${
                  isDark ? "text-gray-300" : "text-gray-600"
                }`}
              >
                Theme
              </span>
              <ThemeToggleButton />
            </div>
            <UserButton inDrawer={true} />
          </div>
        </div>
      </Drawer>
    </header>
  );
}
