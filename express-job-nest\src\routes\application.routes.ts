import express from "express";
import {
  getAllApplications,
  getApplicationById,
  createApplication,
  updateApplicationStatus,
  getApplicationsByJob,
  getMyApplications,
} from "../controllers/application.controller";
import { protect, restrictTo } from "../middleware/auth.middleware";
import {
  checkSubscriptionForCVViewing,
  incrementCVViewCount,
} from "../middleware/subscription.middleware";
import {
  createApplicationValidator,
  updateApplicationStatusValidator,
} from "../validators/application.validator";
import { validationMiddleware } from "../validators/validation.middleware";

const router = express.Router();

// All routes are protected
router.use(protect);

// Candidate routes
router.get("/my", getMyApplications);
router.post(
  "/",
  createApplicationValidator,
  validationMiddleware,
  createApplication
);

// Get specific application (accessible by candidate, employer, or admin)
// Subscription check removed to allow CV viewing without subscription
router.get("/:id", async (req, res, next) => {
  // Directly proceed to get application for all users
  getApplicationById(req, res, next);
});

// Employer and admin routes
router.use(restrictTo("employer", "admin"));
// Subscription check removed to allow CV viewing without subscription
router.get(
  "/job/:jobId",
  // checkSubscriptionForCVViewing and incrementCVViewCount removed
  getApplicationsByJob
);
router.patch(
  "/:id/status",
  updateApplicationStatusValidator,
  validationMiddleware,
  updateApplicationStatus
);

// Admin only routes
router.use(restrictTo("admin"));
router.get("/", getAllApplications);

export default router;
