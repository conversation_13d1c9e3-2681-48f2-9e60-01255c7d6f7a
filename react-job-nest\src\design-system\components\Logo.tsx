import { URLS } from "@/utils/urls";
import { useMantineColorScheme } from "@mantine/core";
import { Link } from "react-router";

const Logo = () => {
  const { colorScheme } = useMantineColorScheme();
  const isDark = colorScheme === "dark";

  // Use the appropriate logo based on the color scheme
  const logoSrc = isDark ? "/logo-dark.svg" : "/logo-light.svg";

  return (
    <Link to={URLS.home}>
      <img
        src={logoSrc}
        alt="JobNest Logo"
        width="100"
        height="50"
        className="transition-opacity duration-300"
      />
    </Link>
  );
};

export default Logo;
