"use client";

import FilterBadges from "@/components/candidate/jobs/FilterBadges";
import JobsList from "@/components/candidate/jobs/JobsList";
import JobsViewControls from "@/components/candidate/jobs/JobsViewControls";
import SearchAndFilterForms from "@/components/candidate/jobs/SearchAndFilterForms";
import PageContainer from "@/design-system/components/layout/PageContainer";
import PageHeading from "@/design-system/components/typography/PageHeading";
import { useThemeClasses } from "@/design-system/utils/theme-utils";
import useJobsPage from "@/hooks/candidate/use-jobs-page";
import { useJobsStore } from "@/stores/candidate-store";
import { useDrawerContext } from "@/stores/ui-store";
import {
  Box,
  Button,
  Card,
  Divider,
  Drawer,
  Grid,
  Group,
  Pagination,
  Text,
  TextInput,
  Title,
  useMantineTheme,
} from "@mantine/core";
import { useDisclosure, useMediaQuery } from "@mantine/hooks";
import { useEffect } from "react";
import { FaFilter, FaSearch } from "react-icons/fa";

export default function CandidateJobsPage() {
  const {
    filterForm,
    handleFilterRemove,
    searchForm,
    handleSearch,
    page,
    setPage,
    totalPages,
    totalJobs,
  } = useJobsPage();

  const [
    filtersDrawerOpened,
    { open: openFiltersDrawer, close: closeFiltersDrawer },
  ] = useDisclosure(false);
  const theme = useMantineTheme();
  const isMobile = useMediaQuery(`(max-width: ${theme.breakpoints.md})`);
  const { setIsInDrawer } = useDrawerContext();

  // Set drawer context when drawer opens/closes
  useEffect(() => {
    setIsInDrawer(filtersDrawerOpened);

    return () => {
      // Reset when component unmounts
      setIsInDrawer(false);
    };
  }, [filtersDrawerOpened, setIsInDrawer]);

  return (
    <PageContainer
      breadcrumbItems={[{ title: "Home", href: "/" }, { title: "Jobs" }]}
      variant="candidate"
    >
      {/* Hero Section with Search */}
      <Box
        className={useThemeClasses(
          "bg-primary-color/5 mb-8 rounded-lg px-4 py-8 md:px-6 md:py-10",
          "bg-primary-color/10 dark:bg-primary-color/20 mb-8 rounded-lg px-4 py-8 md:px-6 md:py-10",
        )}
        mx={{ base: -4, sm: -6 }}
      >
        <PageHeading
          title="Browse Jobs"
          subtitle="Find your next career opportunity"
          className="mb-6 text-center md:mb-8"
          variant="candidate"
        />

        <Card
          className="mx-auto max-w-2xl shadow-sm"
          withBorder={false}
          p={{ base: "xs", sm: "md" }}
        >
          <Group wrap="nowrap" align="flex-start">
            <TextInput
              placeholder="Search for jobs..."
              className="min-w-0 flex-grow"
              leftSection={<FaSearch size={16} />}
              size={isMobile ? "sm" : "md"}
              {...searchForm.getInputProps("search")}
            />
            <Group wrap="nowrap" gap="xs">
              <Button
                size={isMobile ? "sm" : "md"}
                onClick={handleSearch}
                loading={useJobsStore((state) => state.isLoading)}
              >
                Search
              </Button>
              {isMobile && (
                <Button
                  variant="outline"
                  onClick={openFiltersDrawer}
                  leftSection={<FaFilter size={14} />}
                  size="sm"
                >
                  Filters
                </Button>
              )}
            </Group>
          </Group>
        </Card>
      </Box>

      {/* Main Content */}
      <Grid gutter="xl">
        {/* Desktop Filters - Hidden on Mobile */}
        <Grid.Col span={{ base: 0, md: 3 }} className="hidden md:block">
          <div className="sticky top-4">
            <SearchAndFilterForms
              searchForm={searchForm}
              filterForm={filterForm}
              handleSearch={handleSearch}
            />
          </div>
        </Grid.Col>

        {/* Jobs List */}
        <Grid.Col span={{ base: 12, md: 9 }}>
          <Card
            withBorder
            p={{ base: "xs", sm: "md" }}
            radius="md"
            className="mb-4 md:mb-6"
          >
            <Group justify="space-between" mb="sm" wrap="nowrap" align="center">
              <Group wrap="nowrap" gap="xs">
                <Text fw={500} size="sm" className="sm:text-base">
                  Results
                </Text>
                <Text c="dimmed" size="xs" className="hidden sm:block">
                  {totalJobs > 0
                    ? `Showing ${(page - 1) * 10 + 1} - ${Math.min(page * 10, totalJobs)} of ${totalJobs} results`
                    : "No results found"}
                </Text>
              </Group>
              <JobsViewControls />
            </Group>

            <Divider mb="sm" />

            <FilterBadges
              searchTerm={searchForm.values.search}
              filters={filterForm.values}
              removeFilter={handleFilterRemove}
            />
          </Card>

          <JobsList />

          {totalJobs > 0 && (
            <Pagination
              mt="xl"
              total={totalPages}
              value={page}
              onChange={setPage}
              className="flex justify-center"
              radius="md"
              size={isMobile ? "sm" : "md"}
            />
          )}
        </Grid.Col>
      </Grid>

      {/* Mobile Filters Drawer */}
      <Drawer
        opened={filtersDrawerOpened}
        onClose={closeFiltersDrawer}
        title={
          <Group gap="xs">
            <FaFilter size={16} className="text-primary-color" />
            <Title order={4}>Filter Jobs</Title>
          </Group>
        }
        position="right"
        size="xs"
        overlayProps={{ backgroundOpacity: 0.5, blur: 4 }}
      >
        <div className="flex h-[calc(100%-80px)] flex-col">
          <div className="flex-grow overflow-auto">
            <SearchAndFilterForms
              searchForm={searchForm}
              filterForm={filterForm}
              isDrawer={true}
              handleSearch={handleSearch}
            />
          </div>
          <div
            className={useThemeClasses(
              "sticky bottom-0 mt-4 border-t bg-white pt-4 pb-2",
              "sticky bottom-0 mt-4 border-t border-gray-700 bg-dark-7 pt-4 pb-2",
            )}
          >
            <Group gap="md" grow>
              <Button
                variant="outline"
                onClick={() => {
                  filterForm.reset();
                  searchForm.reset();
                  closeFiltersDrawer();
                }}
              >
                Reset
              </Button>
              <Button
                onClick={() => {
                  handleSearch();
                  closeFiltersDrawer();
                }}
                loading={useJobsStore((state) => state.isLoading)}
              >
                Apply Filters
              </Button>
            </Group>
          </div>
        </div>
      </Drawer>
    </PageContainer>
  );
}
