import { z } from "zod";

export const generalSettingsSchema = z.object({
  siteName: z.string().min(2, "Site name must be at least 2 characters"),
  siteDescription: z
    .string()
    .min(10, "Site description must be at least 10 characters"),
  contactEmail: z.string().email("Invalid email address"),
  supportPhone: z.string().min(10, "Phone number must be at least 10 digits"),
  maintenanceMode: z.boolean(),
});

export const emailSettingsSchema = z.object({
  smtpHost: z.string().min(1, "SMTP host is required"),
  smtpPort: z.number().min(1, "SMTP port is required"),
  smtpUsername: z.string().min(1, "SMTP username is required"),
  smtpPassword: z.string().min(1, "SMTP password is required"),
  fromEmail: z.string().email("Invalid email address"),
  fromName: z.string().min(2, "From name must be at least 2 characters"),
});

export const jobSettingsSchema = z.object({
  defaultJobExpiryDays: z
    .number()
    .min(1, "Default job expiry days must be at least 1"),
  maxJobsPerEmployer: z
    .number()
    .min(1, "Max jobs per employer must be at least 1"),
  requireJobApproval: z.boolean(),
  allowJobEditing: z.boolean(),
});
