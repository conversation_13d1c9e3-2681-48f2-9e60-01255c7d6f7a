"use client";

import { useThemeClasses } from "@/design-system/utils/theme-utils";
import {
  Accordion,
  Button,
  Card,
  Checkbox,
  Divider,
  Group,
  RangeSlider,
  Select,
  Stack,
  Text,
  TextInput,
  Title,
  useMantineColorScheme,
} from "@mantine/core";
import {
  FaBriefcase,
  FaBuilding,
  FaCalendarAlt,
  FaDollarSign,
  FaFilter,
  FaGraduationCap,
  FaMapMarkerAlt,
  FaSearch,
  FaTags,
} from "react-icons/fa";

type SearchAndFilterFormsProps = {
  searchForm: any;
  filterForm: any;
  isDrawer?: boolean;
  handleSearch?: () => void;
};

export default function SearchAndFilterForms({
  filterForm,
  searchForm,
  isDrawer = false,
  handleSearch,
}: SearchAndFilterFormsProps) {
  const { colorScheme } = useMantineColorScheme();
  const isDark = colorScheme === "dark";
  // Render the filter content
  const renderFilterContent = () => (
    <Accordion
      multiple
      defaultValue={["jobType", "location"]}
      styles={{
        item: {
          borderColor: isDark
            ? "var(--mantine-color-dark-4) !important"
            : undefined,
        },
        control: {
          backgroundColor: isDark
            ? "var(--mantine-color-dark-7) !important"
            : undefined,
        },
        panel: {
          backgroundColor: isDark
            ? "var(--mantine-color-dark-7) !important"
            : undefined,
        },
        label: {
          color: isDark ? "var(--mantine-color-gray-3) !important" : undefined,
        },
        icon: {
          color: isDark
            ? "var(--mantine-color-primary-4) !important"
            : undefined,
        },
      }}
    >
      <Accordion.Item value="jobType">
        <Accordion.Control
          icon={
            <FaBriefcase
              size={16}
              className={useThemeClasses(
                "text-primary-color",
                "text-primary-color/80",
              )}
            />
          }
        >
          Job Type
        </Accordion.Control>
        <Accordion.Panel>
          <Stack gap="xs">
            <Checkbox
              label="Full-time"
              value="Full-time"
              checked={filterForm.values.jobType === "Full-time"}
              onChange={() => filterForm.setFieldValue("jobType", "Full-time")}
            />
            <Checkbox
              label="Part-time"
              value="Part-time"
              checked={filterForm.values.jobType === "Part-time"}
              onChange={() => filterForm.setFieldValue("jobType", "Part-time")}
            />
            <Checkbox
              label="Contract"
              value="Contract"
              checked={filterForm.values.jobType === "Contract"}
              onChange={() => filterForm.setFieldValue("jobType", "Contract")}
            />
            <Checkbox
              label="Internship"
              value="Internship"
              checked={filterForm.values.jobType === "Internship"}
              onChange={() => filterForm.setFieldValue("jobType", "Internship")}
            />
          </Stack>
        </Accordion.Panel>
      </Accordion.Item>

      <Accordion.Item value="workType">
        <Accordion.Control
          icon={
            <FaMapMarkerAlt
              size={16}
              className={useThemeClasses(
                "text-primary-color",
                "text-primary-color/80",
              )}
            />
          }
        >
          Work Type
        </Accordion.Control>
        <Accordion.Panel>
          <Stack gap="xs">
            <Checkbox
              label="Remote"
              value="Remote"
              checked={filterForm.values.workType === "Remote"}
              onChange={() => filterForm.setFieldValue("workType", "Remote")}
            />
            <Checkbox
              label="Onsite"
              value="Onsite"
              checked={filterForm.values.workType === "Onsite"}
              onChange={() => filterForm.setFieldValue("workType", "Onsite")}
            />
            <Checkbox
              label="Hybrid"
              value="Hybrid"
              checked={filterForm.values.workType === "Hybrid"}
              onChange={() => filterForm.setFieldValue("workType", "Hybrid")}
            />
          </Stack>
        </Accordion.Panel>
      </Accordion.Item>

      <Accordion.Item value="location">
        <Accordion.Control
          icon={
            <FaMapMarkerAlt
              size={16}
              className={useThemeClasses(
                "text-primary-color",
                "text-primary-color/80",
              )}
            />
          }
        >
          Location
        </Accordion.Control>
        <Accordion.Panel>
          <TextInput
            placeholder="Enter location..."
            leftSection={<FaMapMarkerAlt size={14} />}
            mb="xs"
          />
          <Select
            label="Distance"
            placeholder="Any distance"
            data={[
              "Within 5 miles",
              "Within 10 miles",
              "Within 25 miles",
              "Within 50 miles",
              "Within 100 miles",
            ]}
          />
        </Accordion.Panel>
      </Accordion.Item>

      <Accordion.Item value="category">
        <Accordion.Control
          icon={
            <FaTags
              size={16}
              className={useThemeClasses(
                "text-primary-color",
                "text-primary-color/80",
              )}
            />
          }
        >
          Category
        </Accordion.Control>
        <Accordion.Panel>
          <Select
            placeholder="Select category"
            data={[
              "Software Development",
              "Design",
              "Marketing",
              "Sales",
              "Customer Service",
              "Finance",
              "Human Resources",
              "Engineering",
              "Product Management",
            ]}
            searchable
            clearable
            {...filterForm.getInputProps("category")}
          />
        </Accordion.Panel>
      </Accordion.Item>

      <Accordion.Item value="salary">
        <Accordion.Control
          icon={
            <FaDollarSign
              size={16}
              className={useThemeClasses(
                "text-primary-color",
                "text-primary-color/80",
              )}
            />
          }
        >
          Salary Range
        </Accordion.Control>
        <Accordion.Panel>
          <Text size="sm" mb="xs">
            $30,000 - $200,000+
          </Text>
          <RangeSlider
            min={30}
            max={200}
            step={5}
            minRange={10}
            defaultValue={[50, 120]}
            marks={[
              { value: 30, label: "$30k" },
              { value: 100, label: "$100k" },
              { value: 200, label: "$200k+" },
            ]}
            className="my-5"
          />
        </Accordion.Panel>
      </Accordion.Item>

      <Accordion.Item value="datePosted">
        <Accordion.Control
          icon={
            <FaCalendarAlt
              size={16}
              className={useThemeClasses(
                "text-primary-color",
                "text-primary-color/80",
              )}
            />
          }
        >
          Date Posted
        </Accordion.Control>
        <Accordion.Panel>
          <Stack gap="xs">
            <Checkbox
              label="Last 24 hours"
              value="Last 24 hours"
              checked={filterForm.values.datePosted === "Last 24 hours"}
              onChange={() =>
                filterForm.setFieldValue("datePosted", "Last 24 hours")
              }
            />
            <Checkbox
              label="Last week"
              value="Last week"
              checked={filterForm.values.datePosted === "Last week"}
              onChange={() =>
                filterForm.setFieldValue("datePosted", "Last week")
              }
            />
            <Checkbox
              label="Last month"
              value="Last month"
              checked={filterForm.values.datePosted === "Last month"}
              onChange={() =>
                filterForm.setFieldValue("datePosted", "Last month")
              }
            />
            <Checkbox
              label="Any time"
              value="Any time"
              checked={filterForm.values.datePosted === "Any time"}
              onChange={() =>
                filterForm.setFieldValue("datePosted", "Any time")
              }
            />
          </Stack>
        </Accordion.Panel>
      </Accordion.Item>

      <Accordion.Item value="experience">
        <Accordion.Control
          icon={
            <FaGraduationCap
              size={16}
              className={useThemeClasses(
                "text-primary-color",
                "text-primary-color/80",
              )}
            />
          }
        >
          Experience Level
        </Accordion.Control>
        <Accordion.Panel>
          <Stack gap="xs">
            <Checkbox
              label="Entry Level (0-1 years)"
              value="0-1 years"
              checked={filterForm.values.experience === "0-1 years"}
              onChange={() =>
                filterForm.setFieldValue("experience", "0-1 years")
              }
            />
            <Checkbox
              label="Junior (1-3 years)"
              value="1-3 years"
              checked={filterForm.values.experience === "1-3 years"}
              onChange={() =>
                filterForm.setFieldValue("experience", "1-3 years")
              }
            />
            <Checkbox
              label="Mid-Level (3-5 years)"
              value="3-5 years"
              checked={filterForm.values.experience === "3-5 years"}
              onChange={() =>
                filterForm.setFieldValue("experience", "3-5 years")
              }
            />
            <Checkbox
              label="Senior (5+ years)"
              value="5+ years"
              checked={filterForm.values.experience === "5+ years"}
              onChange={() =>
                filterForm.setFieldValue("experience", "5+ years")
              }
            />
          </Stack>
        </Accordion.Panel>
      </Accordion.Item>

      <Accordion.Item value="company">
        <Accordion.Control
          icon={
            <FaBuilding
              size={16}
              className={useThemeClasses(
                "text-primary-color",
                "text-primary-color/80",
              )}
            />
          }
        >
          Company
        </Accordion.Control>
        <Accordion.Panel>
          <TextInput
            placeholder="Search companies..."
            leftSection={<FaSearch size={14} />}
          />
        </Accordion.Panel>
      </Accordion.Item>
    </Accordion>
  );

  return (
    <Stack gap="md">
      {!isDrawer ? (
        <Card
          withBorder
          radius="md"
          shadow="sm"
          p="md"
          className={`transition-all duration-200 ${
            isDark ? "border-dark-4 bg-dark-7" : "border-gray-200 bg-white"
          }`}
          styles={{
            root: {
              backgroundColor: isDark
                ? "var(--mantine-color-dark-7) !important"
                : undefined,
              borderColor: isDark
                ? "var(--mantine-color-dark-4) !important"
                : undefined,
            },
          }}
        >
          <Group justify="space-between" mb="xs">
            <Title
              order={4}
              className={useThemeClasses(
                "text-primary-color flex items-center gap-2",
                "text-primary-color/90 flex items-center gap-2",
              )}
            >
              <FaFilter size={16} />
              Filters
            </Title>
            <Button
              variant="subtle"
              size="xs"
              color={isDark ? "gray.5" : "gray.7"}
              onClick={() => {
                filterForm.reset();
                searchForm.reset();
              }}
              className="transition-all duration-200"
            >
              Reset All
            </Button>
          </Group>

          <Divider
            mb="md"
            styles={{
              root: {
                borderColor: isDark
                  ? "var(--mantine-color-dark-4) !important"
                  : undefined,
              },
            }}
          />

          {renderFilterContent()}

          {!isDrawer && (
            <Button
              fullWidth
              mt="xl"
              color={isDark ? "primary.6" : "primary"}
              className="transition-all duration-200 hover:bg-primary-600"
              onClick={handleSearch}
            >
              Apply Filters
            </Button>
          )}
        </Card>
      ) : (
        <div>{renderFilterContent()}</div>
      )}
    </Stack>
  );
}
