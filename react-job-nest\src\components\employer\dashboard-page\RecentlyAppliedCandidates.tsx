import employerApi from "@/services/employer-api";
import {
  Avatar,
  Badge,
  Button,
  Group,
  LoadingOverlay,
  Text,
} from "@mantine/core";
import { notifications } from "@mantine/notifications";
import { useState } from "react";
import {
  FaBriefcase,
  FaCalendarAlt,
  FaEye,
  FaThumbsDown,
  FaThumbsUp,
} from "react-icons/fa";
import { useNavigate } from "react-router";

// Activity interface
interface Activity {
  id: string;
  type: string;
  message: string;
  date: string;
  status?: string;
  data?: {
    applicationId?: string;
    userId?: string;
    jobId?: string;
    jobTitle?: string;
    candidateName?: string;
    candidateEmail?: string;
  };
}

interface RecentlyAppliedCandidatesProps {
  candidates?: Activity[];
}

export default function RecentlyAppliedCandidates({
  candidates = [],
}: RecentlyAppliedCandidatesProps) {
  const navigate = useNavigate();
  const [loadingStates, setLoadingStates] = useState<Record<string, boolean>>(
    {},
  );
  const [candidateStatuses, setCandidateStatuses] = useState<
    Record<string, string>
  >({});

  // Map activities to candidate format
  const recentCandidates = candidates.map((activity) => ({
    id: activity.id,
    name:
      activity.data?.candidateName ||
      activity.message.split(" for ")[0].replace("New application from ", ""),
    jobTitle:
      activity.data?.jobTitle ||
      activity.message.split(" for ")[1] ||
      "Unknown Position",
    appliedDate: new Date(activity.date).toLocaleDateString(),
    status: candidateStatuses[activity.id] || activity.status || "New",
    experience: "Not specified",
    skills: [],
    // Add additional data for actions
    applicationId: activity.data?.applicationId,
    userId: activity.data?.userId,
    jobId: activity.data?.jobId,
    email: activity.data?.candidateEmail,
  }));

  // Handle view profile action
  const handleViewProfile = (userId: string) => {
    if (userId) {
      navigate(`/employer/candidates/${userId}`);
    } else {
      notifications.show({
        title: "Error",
        message: "Cannot view profile. User ID not available.",
        color: "red",
      });
    }
  };

  // Handle shortlist action
  const handleShortlist = async (
    applicationId: string,
    candidateId: string,
  ) => {
    if (!applicationId) {
      notifications.show({
        title: "Error",
        message: "Cannot shortlist candidate. Application ID not available.",
        color: "red",
      });
      return;
    }

    try {
      setLoadingStates((prev) => ({ ...prev, [candidateId]: true }));

      const response = await employerApi.candidates.updateApplicationStatus(
        applicationId,
        "shortlisted",
      );

      if (response.data.success) {
        setCandidateStatuses((prev) => ({
          ...prev,
          [candidateId]: "Shortlisted",
        }));
        notifications.show({
          title: "Success",
          message: "Candidate has been shortlisted",
          color: "green",
        });
      } else {
        throw new Error(
          response.data.message || "Failed to shortlist candidate",
        );
      }
    } catch (error) {
      console.error("Error shortlisting candidate:", error);
      notifications.show({
        title: "Error",
        message: "Failed to shortlist candidate. Please try again.",
        color: "red",
      });
    } finally {
      setLoadingStates((prev) => ({ ...prev, [candidateId]: false }));
    }
  };

  // Handle reject action
  const handleReject = async (applicationId: string, candidateId: string) => {
    if (!applicationId) {
      notifications.show({
        title: "Error",
        message: "Cannot reject candidate. Application ID not available.",
        color: "red",
      });
      return;
    }

    try {
      setLoadingStates((prev) => ({ ...prev, [candidateId]: true }));

      const response = await employerApi.candidates.updateApplicationStatus(
        applicationId,
        "rejected",
      );

      if (response.data.success) {
        setCandidateStatuses((prev) => ({
          ...prev,
          [candidateId]: "Rejected",
        }));
        notifications.show({
          title: "Success",
          message: "Candidate has been rejected",
          color: "blue",
        });
      } else {
        throw new Error(response.data.message || "Failed to reject candidate");
      }
    } catch (error) {
      console.error("Error rejecting candidate:", error);
      notifications.show({
        title: "Error",
        message: "Failed to reject candidate. Please try again.",
        color: "red",
      });
    } finally {
      setLoadingStates((prev) => ({ ...prev, [candidateId]: false }));
    }
  };

  // Handle view all candidates action
  const handleViewAllCandidates = () => {
    navigate("/employer/candidates");
  };
  return (
    <div className="relative">
      {recentCandidates.length > 0 ? (
        <>
          {recentCandidates.map((candidate) => (
            <div key={candidate.id} className="mb-4 border-b pb-4 relative">
              <LoadingOverlay visible={loadingStates[candidate.id] || false} />
              <Group justify="space-between" wrap="nowrap" className="mb-2">
                <Group gap="sm" wrap="nowrap">
                  <Avatar color="blue" radius="xl">
                    {candidate.name
                      .split(" ")
                      .map((n) => n[0])
                      .join("")}
                  </Avatar>
                  <div>
                    <Text fw={600}>{candidate.name}</Text>
                    <Text size="sm" c="dimmed">
                      {candidate.experience} experience
                    </Text>
                  </div>
                </Group>
                <Badge
                  size="lg"
                  color={
                    candidate.status === "New"
                      ? "blue"
                      : candidate.status === "Shortlisted"
                        ? "green"
                        : candidate.status === "Rejected"
                          ? "red"
                          : "yellow"
                  }
                >
                  {candidate.status}
                </Badge>
              </Group>

              <Group className="mt-2 ml-12">
                <Group gap="xs">
                  <FaBriefcase size={14} className="text-blue-500" />
                  <Text size="sm">Applied for: {candidate.jobTitle}</Text>
                </Group>
                <Group gap="xs">
                  <FaCalendarAlt size={14} className="text-blue-500" />
                  <Text size="sm">Applied on: {candidate.appliedDate}</Text>
                </Group>
              </Group>

              <Group className="mt-2 ml-12">
                {candidate.skills.map((skill, index) => (
                  <Badge key={index} variant="light" color="gray">
                    {skill}
                  </Badge>
                ))}
              </Group>

              <Group className="mt-3 ml-12">
                <Button
                  variant="light"
                  size="xs"
                  leftSection={<FaEye size={12} />}
                  onClick={() => handleViewProfile(candidate.userId || "")}
                >
                  View Profile
                </Button>
                {candidate.status !== "Shortlisted" &&
                  candidate.status !== "Rejected" && (
                    <>
                      <Button
                        variant="outline"
                        size="xs"
                        color="green"
                        leftSection={<FaThumbsUp size={12} />}
                        onClick={() =>
                          handleShortlist(
                            candidate.applicationId || "",
                            candidate.id,
                          )
                        }
                        loading={loadingStates[candidate.id] || false}
                      >
                        Shortlist
                      </Button>
                      <Button
                        variant="outline"
                        size="xs"
                        color="red"
                        leftSection={<FaThumbsDown size={12} />}
                        onClick={() =>
                          handleReject(
                            candidate.applicationId || "",
                            candidate.id,
                          )
                        }
                        loading={loadingStates[candidate.id] || false}
                      >
                        Reject
                      </Button>
                    </>
                  )}
              </Group>
            </div>
          ))}

          <Group justify="center" className="mt-4">
            <Button variant="subtle" onClick={handleViewAllCandidates}>
              View All Candidates
            </Button>
          </Group>
        </>
      ) : (
        <div className="py-6 text-center border rounded-md">
          <Text size="lg" fw={500} className="mb-2">
            No applications yet
          </Text>
          <Text size="sm" c="dimmed" className="mb-4">
            You have not received any job applications recently. Make sure your
            job postings are active and visible to candidates.
          </Text>
          <Button
            variant="filled"
            onClick={() => navigate("/employer/manage-jobs")}
            leftSection={<FaBriefcase size={14} />}
          >
            View Your Jobs
          </Button>
        </div>
      )}
    </div>
  );
}
